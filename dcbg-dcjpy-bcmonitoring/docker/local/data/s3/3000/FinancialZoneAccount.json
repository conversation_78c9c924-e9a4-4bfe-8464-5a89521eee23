{"address": "0xF908c90F27013E2E85eB6aF516F7363C674BBeC3", "abi": [{"type": "event", "anonymous": false, "name": "AddAccountLimit", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256[]", "name": "limitAmounts"}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddCumulativeAmount", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "cumulativeDate", "indexed": false}, {"type": "uint256", "name": "cumulativeAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SubtractCumulativeAmount", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "cumulativeDate", "indexed": false}, {"type": "uint256", "name": "cumulativeAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncBurn", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "cumulativeDate", "indexed": false}, {"type": "uint256", "name": "cumulativeAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncCharge", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "cumulativeDate", "indexed": false}, {"type": "uint256", "name": "cumulativeAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncCumulativeReset", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncMint", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "cumulativeDate", "indexed": false}, {"type": "uint256", "name": "cumulativeAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SyncTransfer", "inputs": [{"type": "bytes32", "name": "accountId", "indexed": false}, {"type": "uint256", "name": "amount", "indexed": false}, {"type": "uint256", "name": "cumulativeDate", "indexed": false}, {"type": "uint256", "name": "cumulativeAmount", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "addAccountLimit", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256[]", "name": "limitAmounts"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "addCumlativeAmount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "uint256", "name": "cumulativeDate"}, {"type": "uint256", "name": "cumulativeAmount"}]}, {"type": "function", "name": "checkBurn", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkCharge", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkMint", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkTransactionLimits", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "operationType"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkTransfer", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "convertJSTDay", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "uint256", "name": "timestamp"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "cumulativeReset", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": []}, {"type": "function", "name": "getAccountLimitData", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "tuple", "name": "accountLimitData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getFinAccountAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "index"}], "outputs": [{"type": "tuple", "name": "finAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "financialZoneAccountData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}]}]}, {"type": "function", "name": "getJSTDay", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "hasAccount", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "modAccountLimit", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bool[]", "name": "itemFlgs"}, {"type": "uint256[]", "name": "limitAmounts"}], "outputs": [{"type": "uint256[]", "name": ""}]}, {"type": "function", "name": "setFinAccountAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "finAccount", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "financialZoneAccountData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "subtractCumulativeAmount", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "uint256", "name": "cumulativeDate"}, {"type": "uint256", "name": "cumulativeAmount"}]}, {"type": "function", "name": "syncBurn", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "syncCharge", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "syncCumulativeReset", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "syncMint", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "syncTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x762f6a896647ff14f65833ba4d267d86ce1db08b2239a7a0aae7f3eb905b4d0f", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0xF908c90F27013E2E85eB6aF516F7363C674BBeC3", "transactionIndex": 0, "gasUsed": "3358537", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x86d27374c32befec27fc66085d73411cce0b845b148b120d73aa3f9683198d90", "blockNumber": 335, "cumulativeGasUsed": "3358537", "status": 1}}