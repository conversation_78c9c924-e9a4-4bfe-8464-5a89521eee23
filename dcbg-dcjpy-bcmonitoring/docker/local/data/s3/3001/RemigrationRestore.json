{"address": "0x4405030286E7FC7B6938Aa880368146280AA472E", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "restoreAccounts", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreBusinessZoneAccounts", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "bizAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreFinancialZoneAccounts", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "financialZoneAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "financialZoneAccountData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restore<PERSON><PERSON>uer<PERSON>", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreProviders", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "providers", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreToken", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "restoreValidators", "constant": false, "payable": false, "inputs": [{"type": "tuple[]", "name": "validators", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "validatorAccountId"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "validatorIdExistence"}, {"type": "bool", "name": "issuerIdLinkedFlag"}, {"type": "address", "name": "validatorEoa"}, {"type": "tuple[]", "name": "validAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByValidatorId"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x200beb9d5ea95ed42cccda3dc0202ae3a2a3357cfac79198065a49c51415ea0b", "receipt": {"to": null, "from": "0x81B1425696634636B8d90141452f9491C92797E7", "contractAddress": "0x4405030286E7FC7B6938Aa880368146280AA472E", "transactionIndex": 0, "gasUsed": "2642311", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xa302771587bfdad7b5f1f22e2522148126e5faf1bbb7e0a81f7799d2625221aa", "blockNumber": 670, "cumulativeGasUsed": "2642311", "status": 1}}