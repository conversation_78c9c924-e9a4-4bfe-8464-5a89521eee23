#!/bin/bash
# 引数:
# $1 : リポジトリ名
# $2 : 環境名(local) local環境ではない場合は指定しない

ROOT=$(cd $(dirname $BASH_SOURCE)/..; pwd)
pushd $ROOT > /dev/null

if [ $# -le 0 ]; then
    echo "エラー: 引数が不足しています。少なくとも1つ以上の引数が必要です。" >&2
    exit 1
fi
REPOSITORY_NAME=$1

if [ $# -eq 2 ] && [ "$2" = "local" ] ; then
  aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin "${ECR_REGISTRY}" || exit 1
fi

chmod +x gradlew
./gradlew :core:"$REPOSITORY_NAME":clean :core:"$REPOSITORY_NAME":build || exit 1

IMAGE_TAG=$(git rev-parse --short HEAD)

docker build -t "$ECR_REGISTRY"/"$ECR_REPOSITORY":"$IMAGE_TAG" -f "core/$REPOSITORY_NAME/Dockerfile" . --platform linux/amd64
docker push "$ECR_REGISTRY"/"$ECR_REPOSITORY":"$IMAGE_TAG" || exit 1