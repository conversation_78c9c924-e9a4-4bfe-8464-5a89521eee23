<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <JavaCodeStyleSettings>
      <option name="LAYOUT_STATIC_IMPORTS_SEPARATELY" value="false" />
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1" />
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="java" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="org" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="com" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
        </value>
      </option>
      <option name="JD_ALIGN_PARAM_COMMENTS" value="false" />
      <option name="JD_ALIGN_EXCEPTION_COMMENTS" value="false" />
      <option name="JD_ADD_BLANK_AFTER_PARM_COMMENTS" value="true" />
      <option name="JD_P_AT_EMPTY_LINES" value="false" />
      <option name="JD_DO_NOT_WRAP_ONE_LINE_COMMENTS" value="true" />
      <option name="JD_PRESERVE_LINE_FEEDS" value="true" />
    </JavaCodeStyleSettings>
    <Properties>
      <option name="KEEP_BLANK_LINES" value="true" />
    </Properties>
    <SqlCodeStyleSettings version="6">
      <option name="KEYWORD_CASE" value="2" />
      <option name="IDENTIFIER_CASE" value="1" />
      <option name="QUERY_EL_LINE" value="0" />
      <option name="QUERY_IN_ONE_STRING" value="1" />
      <option name="SUBQUERY_CONTENT" value="2" />
      <option name="INSERT_CONTENT" value="3" />
      <option name="SET_ALIGN_EQUAL_SIGN" value="false" />
      <option name="SELECT_EL_LINE" value="101" />
      <option name="SELECT_USE_AS_WORD" value="1" />
      <option name="SELECT_ALIGN_AS" value="false" />
      <option name="FROM_INDENT_JOIN" value="false" />
      <option name="TABLE_OPENING" value="0" />
    </SqlCodeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
      <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
      <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="true" />
      <option name="ALIGN_MULTILINE_RESOURCES" value="false" />
      <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACES" value="true" />
      <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="RESOURCE_LIST_WRAP" value="5" />
      <option name="EXTENDS_LIST_WRAP" value="1" />
      <option name="THROWS_LIST_WRAP" value="1" />
      <option name="EXTENDS_KEYWORD_WRAP" value="1" />
      <option name="THROWS_KEYWORD_WRAP" value="1" />
      <option name="TERNARY_OPERATION_WRAP" value="1" />
      <option name="ARRAY_INITIALIZER_WRAP" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="SQL">
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
    </codeStyleSettings>
  </code_scheme>
</component>