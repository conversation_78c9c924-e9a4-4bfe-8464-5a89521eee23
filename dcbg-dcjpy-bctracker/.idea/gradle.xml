<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleHome" value="" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/bctracker-base" />
            <option value="$PROJECT_DIR$/bpm" />
            <option value="$PROJECT_DIR$/bpm/bpm-repository" />
            <option value="$PROJECT_DIR$/bpm/email-send-tracker" />
            <option value="$PROJECT_DIR$/bpm/push-notification-tracker" />
            <option value="$PROJECT_DIR$/core" />
            <option value="$PROJECT_DIR$/core/balance-tracker" />
            <option value="$PROJECT_DIR$/core/core-repository" />
            <option value="$PROJECT_DIR$/core/invoke-core-tracker" />
            <option value="$PROJECT_DIR$/core/transaction-tracker" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>