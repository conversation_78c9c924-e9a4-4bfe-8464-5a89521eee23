package com.decurret_dcp.dcjpy.bctracker.core.balance.config;

import java.net.URI;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClientBuilder;

@Configuration
@Slf4j
public class BalanceTrackerConfig {

    @Bean
    public DynamoDbClient dynamoDbClient(BalanceTrackerProperty property) {
        DynamoDbClientBuilder builder = DynamoDbClient.builder();

        if (property.toAwsConnection() == false) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(property.dynamodbLocalEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));

            log.error(
                    "Change the DynamoDb connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    property.dynamodbLocalEndpoint
            );
        }

        return builder.build();
    }
}
