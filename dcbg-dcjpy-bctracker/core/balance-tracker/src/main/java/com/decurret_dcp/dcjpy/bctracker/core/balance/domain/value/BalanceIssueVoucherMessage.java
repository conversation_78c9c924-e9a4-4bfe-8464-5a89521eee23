package com.decurret_dcp.dcjpy.bctracker.core.balance.domain.value;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.IssueVoucherEvent;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class BalanceIssueVoucherMessage implements BalanceMessage {

    public final AccountId accountId;

    public final ValidatorId fromValidatorId;

    public final ValidatorId toValidatorId;

    public final ZoneId zoneId;

    public final Amount amount;

    public final TransactionType transactionType;

    public AccountId accountId() {
        return this.accountId;
    }

    public ValidatorId fromValidatorId() {
        return this.fromValidatorId;
    }

    public ValidatorId toValidatorId() {
        return this.toValidatorId;
    }

    public ZoneId zoneId() {
        return this.zoneId;
    }

    public Amount amount() {
        return this.amount;
    }

    public TransactionType transactionType() {
        return this.transactionType;
    }

    /**
     * イベントのFilter条件に使用するvalidatorId
     *
     * @return validatorId
     */
    public ValidatorId validatorId() {
        return this.fromValidatorId;
    }

    public static BalanceIssueVoucherMessage create(IssueVoucherEvent event) {
        // FinZone にてチャージを行った結果、IBC 経由で BizZone にて受信するイベント
        return BalanceIssueVoucherMessage.builder()
                .accountId(event.accountId)
                .fromValidatorId(event.validatorId)
                .zoneId(event.zoneId)
                .amount(event.amount)
                .transactionType(TransactionType.CHARGE)
                .build();
    }

}
