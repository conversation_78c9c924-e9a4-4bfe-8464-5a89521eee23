package com.decurret_dcp.dcjpy.bctracker.core.balance.domain.repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

public interface BalanceCacheRepository {

    /**
     * 残高キャッシュの残高を加算する
     *
     * @param accountId 更新対象のアカウントID
     * @param zoneId 更新対象のゾーンID
     * @param addAmount 加算額
     */
    public void addBalance(AccountId accountId, ZoneId zoneId, Amount addAmount);
}
