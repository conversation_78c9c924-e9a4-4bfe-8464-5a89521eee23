package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor

import com.decurret_dcp.dcjpy.bctracker.base.adaptor.AwsSqsMessageAdaptor
import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import spock.lang.Specification

/**
 * AwsSqsMessageAdaptorのテストクラス。
 * 本来はbctracker-baseにテストクラスを作成すべきだが、dockerを使用するため、このレポジトリでテストする。
 */
@Testcontainers
@SpringBootTest()
class AwsSqsMessageAdaptorSpec extends Specification {

    @Autowired
    AwsSqsMessageAdaptor awsSqsMessageAdaptor

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication


    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec(){
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_invoke-core.fifo")
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    def "testPoll_SQSにメッセージが送信されていない場合"() {
        setup:

        when:
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        Objects.isNull(result)
    }

    def "testPoll_SQSにBurnイベントメッセージが送信された場合"() {
        setup:
        Map<String, String> indexedValues = new HashMap<>()
        indexedValues.put("issuerId", "[50,48,48,122,122,117,113,81,52,117,82,76,49,90,83,55,119,97,72,69,54,50,48,51,55,74,121,77,114,80,69,86]")

        Map<String, String> nonIndexedValues = Map.ofEntries(
                Map.entry("zoneId", "3000"),
                Map.entry("amount", "100"),
                Map.entry("balance", "1000"),
                Map.entry("accountId", "[54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]"),
                Map.entry("accountName", "[227,131,162,227,131,131,227,130,175,227,130,174,227,131,179,227,130,179,227,130,166,227,130,179,227,130,166,227,130,182,49,0]"),
                Map.entry("validatorId", "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]")
        )


        when:
        CoreAdhocHelper.sendMessage(sqsClient,"Burn", indexedValues, nonIndexedValues)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        !Objects.isNull(result)
        result.name.equals("Burn")
        result.transactionHash.getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        result.blockTimestamp == BlockTimeStamp.of(**********)
        result.logIndex == 0
        result.log.equals("{\"address\":\"0xeec918d74c746167564401103096d45bbd494b74\"}")
    }

    def "testPoll_SQSにMintイベントメッセージが送信された場合"() {
        setup:
        Map<String, String> indexedValues = new HashMap<>()
        indexedValues.put("issuerId", "[50,48,48,122,122,117,113,81,52,117,82,76,49,90,83,55,119,97,72,69,54,50,48,51,55,74,121,77,114,80,69,86]")

        Map<String, String> nonIndexedValues = new HashMap<>()
        nonIndexedValues.put("zoneId", "3000")
        nonIndexedValues.put("amount", "1000")
        nonIndexedValues.put("balance", "2500")
        nonIndexedValues.put("accountId", "[54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]")
        nonIndexedValues.put("accountName", "[227,131,162,227,131,131,227,130,175,227,130,174,227,131,179,227,130,179,227,130,166,227,130,179,227,130,166,227,130,182,49,0]")
        nonIndexedValues.put("validatorId", "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]")

        when:
        CoreAdhocHelper.sendMessage(sqsClient,"Mint", indexedValues, nonIndexedValues)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        !Objects.isNull(result)
        result.name.equals("Mint")
        result.transactionHash.getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        result.blockTimestamp == BlockTimeStamp.of(**********)
        result.logIndex == 0
        result.log.equals("{\"address\":\"0xeec918d74c746167564401103096d45bbd494b74\"}")
    }

    def "testPoll_JSON形式でないメッセージを送った時"() {
        setup:
        def message = "foo"

        when:
        CoreAdhocHelper.sendMessage(sqsClient, message)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        Objects.isNull(result)
    }

    def "testPoll_不正なメッセージ(Messageがnull)を送った時"() {
        setup:
        def message = "{\"Message\":null}"

        when:
        CoreAdhocHelper.sendMessage(sqsClient, message)
        this.awsSqsMessageAdaptor.poll()

        then:
        thrown(IllegalArgumentException)
    }

    def "testPoll_不正なメッセージ(textValueとして読めない)を送った時"() {
        setup:
        def message = "{\"Message\":\"a\"}"

        when:
        CoreAdhocHelper.sendMessage(sqsClient, message)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        thrown(RuntimeException)
    }

    def "testPoll_不正なメッセージ(dynamoDBがnull)を送った時"() {
        setup:
        def message = "{\"Message\":\"{\\\"foo\\\":\\\"bar\\\"}\"}"

        when:
        CoreAdhocHelper.sendMessage(sqsClient, message)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        Objects.isNull(result)
    }

    def "testPoll_不正なメッセージ(NewImageがnull)を送った時"() {
        setup:
        def message = "{\"Message\":\"{\\\"dynamodb\\\":{\\\"foo\\\":\\\"bar\\\"}}\"}"

        when:
        CoreAdhocHelper.sendMessage(sqsClient, message)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        Objects.isNull(result)
    }

    def "testPoll_不正なメッセージ(logIndexが存在しない)を送った時"() {
        setup:
        def message = "{\"Message\":\"{\\\"dynamodb\\\":{\\\"NewImage\\\":{\\\"foo\\\":\\\"bar\\\"}}}\"}"

        when:
        CoreAdhocHelper.sendMessage(sqsClient, message)
        def result = this.awsSqsMessageAdaptor.poll()

        then:
        Objects.isNull(result)
    }

}
