package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception;

import java.util.Map;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.result.CoreApiDischargeResult;

public class InvokeCoreApiDischargeFailureException extends RuntimeException {

    public InvokeCoreApiDischargeFailureException(String message, Throwable cause) {
        super(message, cause);
    }

    private static final Map<String, String> CORE_API_INVOKE_ERROR_MAP = Map.ofEntries(
            Map.entry("E0005", "CoreAPI Error Code [E0005] Returned. Insufficient DC Account Balance."),
            Map.entry("E0032", "CoreAPI Error Code [E0032] Returned. Invalid DC Account Status."),
            Map.entry("E0073", "CoreAPI Error Code [E0073] Returned. Exceeded Discharge Limit."),
            Map.entry("E0025", "CoreAPI Error Code [E0025] Returned. Exceeded Daily Limit."),
            Map.entry("E0075", "CoreAPI Error Code [E0075] Returned. Duplicated RequestId.")
    );

    public static InvokeCoreApiDischargeFailureException fromCoreApiDischarge(
            Either<CoreApiDischargeResult> dischargeResult
    ) {
        if (dischargeResult.isSuccess()) {
            throw new IllegalStateException("This Either is success.");
        }

        String errorCode = dischargeResult.errorCode();
        Exception cause = dischargeResult.cause();
        String invokeErrorMessage = CORE_API_INVOKE_ERROR_MAP.get(errorCode);
        if (invokeErrorMessage == null) {
            invokeErrorMessage = "CoreAPI Error Code [" + errorCode + "] Returned.";
        }

        return new InvokeCoreApiDischargeFailureException(invokeErrorMessage, cause);
    }
}
