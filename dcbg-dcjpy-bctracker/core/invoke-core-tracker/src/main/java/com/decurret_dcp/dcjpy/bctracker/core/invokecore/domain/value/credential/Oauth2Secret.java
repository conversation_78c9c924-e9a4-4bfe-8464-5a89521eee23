package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.credential;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(exclude = { "clientSecret" })
@EqualsAndHashCode
@Builder
@JsonDeserialize(builder = Oauth2Secret.Builder.class)
public class Oauth2Secret implements CredentialEntity {

    @JsonProperty("client_secret")
    public final ClientSecret clientSecret;
}
