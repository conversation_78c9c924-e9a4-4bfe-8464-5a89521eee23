package com.decurret_dcp.dcjpy.bctracker.core.invokecore.application;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.base.application.BaseTracker;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.service.CoreApiInvoker;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.DischargeRequestedMessage;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.service.ValidatorFilter;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.InvokeCoreMessage;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * DLTからのイベント発行によりCoreAPIを実行するトラッカー.
 */
@Slf4j
@RequiredArgsConstructor
@Transactional
@Component
public class InvokeCoreTracker implements BaseTracker<InvokeCoreMessage> {

    private final ValidatorFilter validatorFilter;

    private final CoreApiInvoker coreApiInvoker;

    @Override
    public InvokeCoreMessage acceptable(BCEvent<?> event) {
        InvokeCoreMessage message = InvokeCoreMessage.create(event.to());
        if (message == null) {
            return null;
        }

        boolean acceptable = this.validatorFilter.acceptable(message.validatorIds());
        if (acceptable == false) {
            return null;
        }

        return message;
    }

    @Override
    public boolean onMessage(InvokeCoreMessage message) {

        switch (message.eventType()) {
            case DISCHARGE_REQUESTED ->
                    this.coreApiInvoker.invokeCoreApiDischarge((DischargeRequestedMessage) message);
        }

        return true;
    }
}
