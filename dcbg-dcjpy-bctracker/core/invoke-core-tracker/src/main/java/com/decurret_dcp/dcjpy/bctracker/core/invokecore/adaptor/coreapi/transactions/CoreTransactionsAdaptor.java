package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi.transactions;

import org.springframework.http.RequestEntity;
import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi.CoreAdaptorSupport;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi.transactions.request.CoreApiDischargeRequest;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi.transactions.response.CoreApiDischargeResponse;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.CoreTransactionsApi;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.command.CoreApiDischargeCommand;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.result.CoreApiDischargeResult;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class CoreTransactionsAdaptor implements CoreTransactionsApi {

    /** コアAPI関連のヘルパークラス */
    private final CoreAdaptorSupport support;

    @Override
    public Either<CoreApiDischargeResult> discharge(CoreApiDischargeCommand command) {
        CoreApiDischargeRequest body = CoreApiDischargeRequest.createBody(command);
        RequestEntity<CoreApiDischargeRequest> request = this.support
                .initPost(command.validatorId, "/transactions/discharge").body(body);

        Either<CoreApiDischargeResponse> response = this.support.execute(request, CoreApiDischargeResponse.class);
        return response.map(success -> success.toResult());
    }
}
