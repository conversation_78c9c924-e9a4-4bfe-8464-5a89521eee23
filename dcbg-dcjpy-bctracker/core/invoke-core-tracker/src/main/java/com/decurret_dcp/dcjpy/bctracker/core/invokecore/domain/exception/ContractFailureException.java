package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception;

public class ContractFailureException extends RuntimeException {

    private ContractFailureException(String message) {
        super(message);
    }

    public static ContractFailureException failed(String detail) {
        return new ContractFailureException(detail);
    }

//    public MessageCode getMessageCode() {return MessageCode.UNEXPECTED_FAILURE_OCCURRED_FROM_CONTRACT;}
}
