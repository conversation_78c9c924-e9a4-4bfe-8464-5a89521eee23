package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi.transactions.request;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.command.CoreApiDischargeCommand;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class CoreApiDischargeRequest {

    public final String requestId;

    public final String accountId;

    public final BigInteger dischargeAmount;

    public static CoreApiDischargeRequest createBody(CoreApiDischargeCommand command) {
        return CoreApiDischargeRequest.builder()
                .requestId(command.requestId.getValue())
                .accountId(command.accountId.getValue())
                .dischargeAmount(command.dischargeAmount.getValue())
                .build();
    }
}
