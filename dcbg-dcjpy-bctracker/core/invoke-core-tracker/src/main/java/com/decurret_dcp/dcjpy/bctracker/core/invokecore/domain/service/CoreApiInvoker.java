package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.CoreTransactionsApi;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.command.CoreApiDischargeCommand;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.result.CoreApiDischargeResult;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception.InvokeCoreApiDischargeFailureException;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.DischargeRequestedMessage;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@Service
@Slf4j
@Transactional
public class CoreApiInvoker {

    private final CoreTransactionsApi coreTransactionsApi;

    /**
     * ディスチャージのCore APIを呼び出す。
     *
     * @param message API実行メッセージ
     *
     */
    public void invokeCoreApiDischarge(DischargeRequestedMessage message) {
        CoreApiDischargeCommand command = message.toCommand();
        Either<CoreApiDischargeResult> dischargeResult = this.coreTransactionsApi.discharge(command);

        if (dischargeResult.isSuccess() == false) {
            throw InvokeCoreApiDischargeFailureException.fromCoreApiDischarge(dischargeResult);
        }

        log.info("Succeed to send discharge. accountId : {}, dischargeAmount : {}, balance : {}",
                 dischargeResult.success().accountId,
                 dischargeResult.success().dischargeAmount,
                 dischargeResult.success().balance);
    }
}
