package com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.result;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class CoreApiDischargeResult {

    public final AccountId accountId;

    public final String accountName;

    public final Amount balance;

    public final Amount dischargeAmount;

    public final ZoneId fromZoneId;

    public final String fromZoneName;
}
