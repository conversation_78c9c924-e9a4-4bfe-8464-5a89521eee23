package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi.transactions.response;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.coreapi.result.CoreApiDischargeResult;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class CoreApiDischargeResponse {

    /* アカウントID */
    public String accountId;

    /* アカウント名 */
    public String accountName;

    /* ディスチャージ後の BizZone 残高 */
    public BigInteger balance;

    /* チャージ額 */
    public BigInteger dischargeAmount;

    /* チャージ元のゾーンID */
    public Integer fromZoneId;

    /* チャージ元のゾーン名 */
    public String fromZoneName;

    public CoreApiDischargeResult toResult() {
        return CoreApiDischargeResult.builder()
                .accountId(AccountId.of(this.accountId))
                .accountName(this.accountName)
                .balance(Amount.of(this.balance))
                .dischargeAmount(Amount.of(this.dischargeAmount))
                .fromZoneId(ZoneId.of(this.fromZoneId))
                .fromZoneName(this.fromZoneName)
                .build();
    }
}
