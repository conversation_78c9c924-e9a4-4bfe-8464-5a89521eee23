version: '3.8'
services:
  db:
    image: postgres:11.10-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres

  cognito-local:
    image: jagregory/cognito-local:3.21.2
    volumes:
      - ./bctracker-core-local-env/cognito:/app/.cognito
    ports:
      - "19229:9229"

  core-idp-mock:
    build:
      context: bctracker-core-local-env/idp-mock
      dockerfile: Dockerfile
    ports:
      - "18182:8080"

  localstack:
    image: localstack/localstack:3.0.2
    environment:
      - SERVICES=sqs,sns,ses,dynamodb,secretsmanager
    ports:
      - "14566-14599:4566-4599"
