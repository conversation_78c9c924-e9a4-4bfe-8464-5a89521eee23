package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.validator.response;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value.ResponseContractBytes32Value;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(toBuilder = true)
@JsonDeserialize(builder = ValidatorGetAccountResponse.Builder.class)
public class ValidatorGetAccountResponse {

    @JsonProperty("accountData")
    public final AccountDataResponse accountData;

    public ValidatorGetAccountResult toResult() {
        return this.accountData.toResult();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Builder {
    }

    @lombok.Builder(toBuilder = true)
    @JsonDeserialize(builder = AccountDataResponse.Builder.class)
    public static class AccountDataResponse {

        @JsonProperty("accountName")
        public final String accountName;

        @JsonProperty("accountStatus")
        public final ResponseContractBytes32Value accountStatus;

        @JsonProperty("reasonCode")
        public final ResponseContractBytes32Value reasonCode;

        @JsonProperty("balance")
        public final Balance balance;

        @JsonProperty("appliedAt")
        public final BlockTimeStamp appliedAt;

        @JsonProperty("registeredAt")
        public final BlockTimeStamp registeredAt;

        @JsonProperty("terminatingAt")
        public final BlockTimeStamp terminatingAt;

        @JsonProperty("terminatedAt")
        public final BlockTimeStamp terminatedAt;

        private ValidatorGetAccountResult toResult() {
            return ValidatorGetAccountResult.builder()
                    .accountName(this.accountName)
                    .accountStatus(AccountStatus.of(this.accountStatus.getValue()))
                    .reasonCode(this.reasonCode.getValue())
                    .balance(this.balance)
                    .appliedAt(AppTimeStamp.create(this.appliedAt))
                    .registeredAt(AppTimeStamp.create(this.registeredAt))
                    .terminatingAt(AppTimeStamp.create(this.terminatingAt))
                    .terminatedAt(AppTimeStamp.create(this.terminatedAt))
                    .build();
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Builder {
        }
    }
}
