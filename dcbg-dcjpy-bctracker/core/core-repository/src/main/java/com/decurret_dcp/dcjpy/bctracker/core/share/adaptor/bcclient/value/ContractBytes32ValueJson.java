package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class ContractBytes32ValueJson {

    static class Serializer extends JsonSerializer<ContractBytes32Value> {

        @Override
        public void serialize(ContractBytes32Value value, JsonGenerator generator, SerializerProvider provider)
                throws IOException {
            if (value == null) {
                return;
            }
            generator.writeString(value.getValue());
        }
    }

    static class Deserializer extends JsonDeserializer<ContractBytes32Value> {

        @Override
        public ContractBytes32Value deserialize(<PERSON><PERSON><PERSON><PERSON><PERSON> parser, DeserializationContext context) throws IOException {
            String value = parser.getValueAsString();
            return new ContractBytes32Value(value);
        }
    }
}
