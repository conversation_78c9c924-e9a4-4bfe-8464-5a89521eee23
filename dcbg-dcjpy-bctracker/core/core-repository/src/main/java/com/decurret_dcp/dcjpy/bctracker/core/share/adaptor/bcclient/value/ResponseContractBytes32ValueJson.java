package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value;

import java.io.IOException;
import java.util.Arrays;

import org.web3j.utils.Numeric;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

class ResponseContractBytes32ValueJson {

    static class Deserializer extends JsonDeserializer<ResponseContractBytes32Value> {

        @Override
        public ResponseContractBytes32Value deserialize(JsonParser parser, DeserializationContext context)
                throws IOException {

            String contractHexToString = contractHexToString(parser.getValueAsString());
            return new ResponseContractBytes32Value(contractHexToString);
        }
    }

    /**
     * コントラクトから取得したHEX文字列をStringに変換する.
     * Bytes32をStringにすることを主目的としているため、0x00を文字列の終端とみなしています
     *
     * @param str コントラクトから取得した文字列
     *
     * @return 変換後文字列
     */
    private static String contractHexToString(String str) {
        byte[] array = Numeric.hexStringToByteArray(str);
        int end;
        for (end = 0; end < array.length; end++) {
            // 0x00を終端とみなしています。Bytes32がそのようになっているため
            if (array[end] == 0x00) {
                break;
            }
        }
        byte[] target = Arrays.copyOf(array, end);
        return new String(target);
    }
}
