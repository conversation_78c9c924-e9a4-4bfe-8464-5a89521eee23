package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient;

import java.util.Map;
import java.util.stream.Collectors;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.ContractName;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.ContractValue;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class BCClientCallRequest {

    public final String zoneId;

    public final String contractName;

    public final String method;

    public final Map<String, Object> args;

    public static BCClientCallRequest create(
            ZoneId zoneId, ContractName contractName, String contractMethod, Map<String, ContractValue> parameters
    ) {
        Map<String, Object> args = parameters.entrySet().stream()
                .collect(Collectors.toMap(entry -> entry.getKey(), entry -> entry.getValue().getValue()));

        return BCClientCallRequest.builder()
                .zoneId(zoneId.getValue().toString())
                .contractName(contractName.getValue())
                .method(contractMethod)
                .args(args)
                .build();
    }
}
