package com.decurret_dcp.dcjpy.bctracker.core.share.domain.service;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.base.domain.repository.TransactionSupport;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.ClientEntityRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Component
@Slf4j
public class ValidatorFilter {

    private static final Map<ValidatorId, Boolean> CACHED_IDS = new ConcurrentHashMap<>();

    private final TransactionSupport transactionSupport;

    private final ClientEntityRepository clientEntityRepository;

    /**
     * 指定された validator_id が処理対象か否か判定する。
     * このメソッドの外側で DB トランザクションを設定しないこと。
     *
     * @param validatorId 判定対象の validator_id
     *
     * @return 処理対象ならば true
     */
    public boolean acceptable(ValidatorId validatorId) {
        if (CACHED_IDS.containsKey(validatorId)) {
            return true;
        }

        Boolean acceptable = this.transactionSupport.transaction(
                () -> {
                    boolean exists = this.clientEntityRepository.isExists(validatorId);
                    return Boolean.valueOf(exists);
                }
        );

        if (acceptable.booleanValue() == false) {
            return false;
        }

        CACHED_IDS.put(validatorId, Boolean.TRUE);

        log.info("Cached accepting validator_id : {}", validatorId.getValue());

        return true;
    }

    public boolean acceptable(Collection<ValidatorId> validatorIds) {
        for (ValidatorId validatorId : validatorIds) {
            if (this.acceptable(validatorId) == true) {
                return true;
            }
        }

        return false;
    }
}
