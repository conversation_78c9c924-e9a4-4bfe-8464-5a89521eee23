package com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.command.ValidatorGetAccountCommand;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result.ValidatorGetAccountResult;

public interface ValidatorApi {

    public Either<ValidatorGetAccountResult> getAccount(ValidatorGetAccountCommand command);
}
