package com.decurret_dcp.dcjpy.bctracker.core.share.config;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Range;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ConstructorBinding
@ConfigurationProperties(prefix = "bctracker.base.bcclient")
@AllArgsConstructor
@ToString
public class BCClientProperty {

    public final String baseUrl;

    @NotNull
    @Range(min = 1, max = 50)
    public final Integer httpConnectionMaxPerRoute;

    @NotNull
    @Range(min = 1, max = 50)
    public final Integer httpConnectionMaxTotal;

    @NotNull
    public final Long readTimeoutMillisecond;
}
