package com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity;

import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;
import org.seasar.doma.jdbc.entity.NamingType;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ClientId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;

@Entity(immutable = true, naming = NamingType.SNAKE_LOWER_CASE)
@Table(name = "client_entity")
@Builder
@EqualsAndHashCode
public class ClientEntity {

    @Id
    public final ClientId clientId;

    public final EntityType entityType;

    public final ZoneId zoneId;

    public final EntityId entityId;
}
