package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao.ClientEntityDao;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.ClientEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.ClientEntityRepository;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaClientEntityRepository implements ClientEntityRepository {

    private final ClientEntityDao clientEntityDao;

    @Override
    public boolean isExists(ValidatorId validatorId) {
        return this.clientEntityDao.existsForValidatorId(validatorId);
    }

    @Override
    public ZoneId findZoneIdByEntityId(EntityId entityId) {
        return this.clientEntityDao.findZoneIdByEntityId(entityId);
    }

    @Override
    public ClientEntity findClientEntityId(EntityId entityId) {
        return this.clientEntityDao.selectByEntityId(entityId);
    }
}
