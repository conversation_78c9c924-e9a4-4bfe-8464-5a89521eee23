package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient;

import java.util.Map;

import lombok.AllArgsConstructor;

@AllArgsConstructor
class BCClientCallResponse {

    public final Map<String, Object> data;

    public boolean isSuccess() {
        Object err = this.data.get("err");
        if (err == null) {
            return true;
        }

        return "".equals(err.toString());
    }

    public String getError() {
        if (this.isSuccess()) {
            throw new IllegalArgumentException("Call contract is succeed.");
        }

        Object err = this.data.get("err");
        return err.toString();
    }
}
