package com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity;

import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;
import org.seasar.doma.jdbc.entity.NamingType;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;

import lombok.Builder;

@Entity(immutable = true, naming = NamingType.SNAKE_LOWER_CASE)
@Table(name = "transaction_sender")
@Builder(toBuilder = true)
public class TransactionSenderEntity {

    /** トランザクションID */
    @Id
    @Column(name = "transaction_id")
    public final TransactionId transactionId;

    /** 送金指示者アカウントID */
    @Column(name = "send_account_id")
    public final AccountId sendAccountId;
}
