package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.ClientEntity;

@ConfigAutowireable
@Dao
public interface ClientEntityDao {

    @Select
    public boolean existsForValidatorId(ValidatorId validatorId);

    @Select
    public ZoneId findZoneIdByEntityId(EntityId entityId);

    @Select
    ClientEntity selectByEntityId(EntityId entityId);
}
