package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.value;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.ContractValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.io.BaseEncoding;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode
@ToString
@JsonSerialize(using = ContractBytes32ValueJson.Serializer.class)
@JsonDeserialize(using = ContractBytes32ValueJson.Deserializer.class)
public class ContractBytes32Value implements ContractValue {

    private final byte[] value;

    public ContractBytes32Value(String value) {
        this.value = new byte[32];
        if (value == null) {
            return;
        }

        byte[] bytes = value.getBytes(StandardCharsets.UTF_8);
        if (bytes.length > 32) {
            throw new IllegalArgumentException("value is invalid length : " + value);
        }

        System.arraycopy(bytes, 0, this.value, 0, bytes.length);
    }

    public ContractBytes32Value(byte[] value) {
        this.value = Arrays.copyOf(value, value.length);
    }

    @Override
    public String getValue() {
        return "0x" + BaseEncoding.base16().lowerCase().encode(this.value);
    }

    public byte[] asBytes32() {
        return this.value;
    }
}
