package com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;

@ConfigAutowireable
@Dao
public interface TransactionDao {

    @Insert
    public Result<TransactionEntity> insert(TransactionEntity transaction);

    @Select
    public boolean existsForTransactionId(TransactionId transactionId);
}
