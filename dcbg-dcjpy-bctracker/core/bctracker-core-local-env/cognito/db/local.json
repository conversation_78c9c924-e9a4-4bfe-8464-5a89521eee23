{"Users": {}, "Options": {"Policies": {"PasswordPolicy": {"MinimumLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireNumbers": true, "RequireSymbols": true, "TemporaryPasswordValidityDays": 7}}, "LambdaConfig": {}, "SchemaAttributes": [{"Name": "sub", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": false, "Required": true, "StringAttributeConstraints": {"MinLength": "1", "MaxLength": "2048"}}, {"Name": "name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "given_name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "family_name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "middle_name", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "nickname", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "preferred_username", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "profile", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "picture", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "website", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "email", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "email_verified", "AttributeDataType": "Boolean", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false}, {"Name": "gender", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "birthdate", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "10", "MaxLength": "10"}}, {"Name": "zoneinfo", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "locale", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "phone_number", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "phone_number_verified", "AttributeDataType": "Boolean", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false}, {"Name": "address", "AttributeDataType": "String", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "StringAttributeConstraints": {"MinLength": "0", "MaxLength": "2048"}}, {"Name": "updated_at", "AttributeDataType": "Number", "DeveloperOnlyAttribute": false, "Mutable": true, "Required": false, "NumberAttributeConstraints": {"MinValue": "0"}}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE"}, "MfaConfiguration": "OFF", "EstimatedNumberOfUsers": 0, "EmailConfiguration": {"EmailSendingAccount": "COGNITO_DEFAULT"}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false, "UnusedAccountValidityDays": 7}, "UsernameAttributes": ["email"], "Id": "local"}}