{"Clients": {"test_client": {"AccessTokenValidity": 24, "AllowedOAuthFlows": ["client_credentials"], "AllowedOAuthFlowsUserPoolClient": true, "AllowedOAuthScopes": ["Foo/Foo"], "ClientId": "test_client_id", "ClientName": "test_client", "ClientSecret": "test_client_secret", "CreationDate": "2023-03-28T05:01:21.865Z", "ExplicitAuthFlows": ["ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_PASSWORD_AUTH"], "LastModifiedDate": "2023-03-28T05:01:21.865Z", "PreventUserExistenceErrors": "ENABLED", "RefreshTokenValidity": 30, "TokenValidityUnits": {"AccessToken": "hours", "IdToken": "minutes", "RefreshToken": "days"}, "UserPoolId": "local"}}}