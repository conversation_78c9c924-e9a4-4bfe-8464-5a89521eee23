#!/usr/bin/env bash

# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

enqueue() {
    local BODY=$1
    DEDUPLICATION_ID=$(shuf -i 1-100 -n 1)
    aws --endpoint-url=http://localhost:14566 sqs send-message \
        --queue-url http://localhost:14566/000000000000/dcjpy_bctracker_queue_transaction.fifo \
        --message-body "${BODY}" \
        --message-deduplication-id "${DEDUPLICATION_ID}" \
        --message-group-id "group-id-2"
}
enqueue '{
            "Type": "Notification",
          	"MessageId": "b1f3c9d8-8096-5d85-b1a8-975e2ce4fd9f",
          	"SequenceNumber": "10000000000000019000",
          	"TopicArn": "arn:aws:sns:ap-northeast-1:************:prod-biz-a-tokyo-bcmonitoring-stream.fifo",
            "Message": "{\"eventID\": \"9ea2f5fe8942b88f52d57d70e63e2a52\", \"eventName\": \"INSERT\", \"eventVersion\": \"1.1\", \"eventSource\": \"aws:dynamodb\", \"awsRegion\": \"ap-northeast-1\", \"dynamodb\": { \"ApproximateCreationDateTime\": 1594874888, \"Keys\": { \"logIndex\": { \"N\": \"0\" }, \"transactionHash\": { \"S\": \"0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46986f3144898e109b3f0de22\" } }, \"NewImage\": { \"logIndex\": { \"N\": \"0\" }, \"log\": { \"S\": \"{\\\"address\\\":\\\"0xeec918d74c746167564401103096d45bbd494b74\\\",\\\"topics\\\":[\\\"0x33799229c0d6a2493d11ea73e29b33e66baacd54d6831f9d3385d6b585cafc07\\\",\\\"0x0000000000000000000000000000000000000000000000000000000000002222\\\",\\\"0x0000000000000000000000000000000000000000000000000000000000055551\\\",\\\"0x0000000000000000000000000000000000000000000000000000000000055551\\\"],\\\"data\\\":\\\"0x00000000000000000000000000000000000000000000000000000000000444410000000000000000000000000000000000000000000000000000000000000064\\\",\\\"blockNumber\\\":\\\"0xf2\\\",\\\"transactionHash\\\":\\\"0xc5d2e27d885399d7df10367811c9da3af72a750c4ac8699bf47ee7f4043934f1\\\",\\\"transactionIndex\\\":\\\"0x0\\\",\\\"blockHash\\\":\\\"0x0e1345b2aab0c9d39e7e5167118403687b2bf0790f6750d3f513c062c9fa6f93\\\",\\\"logIndex\\\":\\\"0x1\\\",\\\"removed\\\":false}\" }, \"blockTimestamp\": { \"N\": \"**********\" }, \"name\": { \"S\": \"Burn\" }, \"transactionHash\": { \"S\": \"0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20\" }, \"indexedValues\": { \"S\": \"{\\\"issuerId\\\":[50,48,48,122,122,117,113,81,52,117,82,76,49,90,83,55,119,97,72,69,54,50,48,51,55,74,121,77,114,80,69,86]}\" }, \"nonIndexedValues\": { \"S\": \"{\\\"zoneId\\\":3000,\\\"amount\\\":100,\\\"balance\\\":1000,\\\"accountId\\\":[54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],\\\"accountName\\\":\\\"0xe383a2e38383e382afe382aee383b3e382b3e382a6e382b3e382a6e382b631\\\",\\\"validatorId\\\":[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]}\" } }, \"SequenceNumber\": \"46474900000000004188509015\", \"SizeBytes\":319, \"StreamViewType\": \"NEW_AND_OLD_IMAGES\" }, \"eventSourceARN\": \"arn:aws:dynamodb:ap-northeast-1:************:table/tsys-vetification-Events/stream/2020-07-07T06:11:40.425\"}",
            "Timestamp": "2024-04-03T01:00:34.433Z",
            "UnsubscribeURL": "https://sns.ap-northeast-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:ap-northeast-1:************:prod-biz-a-tokyo-bcmonitoring-stream.fifo:8d1a841d-ca35-4ae2-869b-4d4e9eb1a48c"
}'
