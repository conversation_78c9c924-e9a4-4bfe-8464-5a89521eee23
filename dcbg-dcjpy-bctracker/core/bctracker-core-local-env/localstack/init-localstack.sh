#!/usr/bin/env bash

# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

create_queue() {
    local QUEUE_NAME=$1
    awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
      sqs create-queue --queue-name "${QUEUE_NAME}" \
      --region ${AWS_REGION} \
      --attributes FifoQueue=true,VisibilityTimeout=30

    # dead letter queueを作成
    # 注意：dead letter queueもfifoにすること
    awslocal sqs create-queue --queue-name dead-letter-queue.fifo --attributes FifoQueue=true

    # 作成したqueueの読み込みが、4回失敗した場合、dead letter queueに送信するよう設定
    awslocal sqs set-queue-attributes \
      --queue-url http://sqs.ap-northeast-1.localhost.localstack.cloud:4566/************/"${QUEUE_NAME}" \
      --attributes '{
        "RedrivePolicy": "{\"deadLetterTargetArn\":\"arn:aws:sqs:us-east-1:************:dead-letter-queue.fifo\",\"maxReceiveCount\":\"4\"}"
      }'
}

create_dynamodb() {
  awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 dynamodb create-table \
      --table-name balance_cache \
        --attribute-definitions \
          AttributeName=account_id,AttributeType=S \
          AttributeName=zone_id,AttributeType=N \
        --key-schema \
          AttributeName=account_id,KeyType=HASH \
          AttributeName=zone_id,KeyType=RANGE \
        --region ${AWS_REGION} \
        --billing-mode PAY_PER_REQUEST
}

insert_dynamodb(){
  awslocal dynamodb put-item \
          --table-name balance_cache \
          --region ap-northeast-1 \
          --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
          --item '{ "account_id": { "S": "60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc" }, "zone_id": { "N": "3000" }, "balance": { "N": "0" },"updated_at": { "S": "2024-01-01T00:00:00+09:00" } }'

  awslocal dynamodb put-item \
            --table-name balance_cache \
            --region ap-northeast-1 \
            --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
            --item '{ "account_id": { "S": "60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc" }, "zone_id": { "N": "3001" }, "balance": { "N": "0" },"updated_at": { "S": "2024-01-01T00:00:00+09:00" } }'
}

echo "Start to configure SQS."

create_queue "dcjpy_bctracker_queue_balance.fifo"
create_queue "dcjpy_bctracker_queue_transaction.fifo"
create_queue "dcjpy_bctracker_queue_invoke-core.fifo"
create_dynamodb
insert_dynamodb