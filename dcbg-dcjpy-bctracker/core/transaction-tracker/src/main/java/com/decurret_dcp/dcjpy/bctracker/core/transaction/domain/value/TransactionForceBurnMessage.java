package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ForceDischargeEventDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ForceBurnEvent;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 強制償却時の取引履歴を保存するためのメッセージ
 */
@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
@Slf4j
public class TransactionForceBurnMessage implements TransactionMessage {

    private static final Comparator<ForceDischargeEventDetail> SORTER_BY_ZONE_ID =
            Comparator.comparing(detail -> detail.zoneId.getValue(), Comparator.reverseOrder());

    /** トランザクションハッシュ. */
    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimestamp;

    public final ValidatorId validatorId;

    /** アカウントID. */
    public final AccountId accountId;

    public final Amount burnedAmount;

    public final Balance burnedBalance;

    /** zoneIdの降順でソートした強制ディスチャージのイミュータブルリスト　 */
    public final List<ForceDischargeEventDetail> zoneIdDescSortedForceDischargeEventDetails;

    @Override
    public List<ValidatorId> validatorIds() {
        return Collections.singletonList(this.validatorId);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.FORCE_BURN;
    }

    public static TransactionForceBurnMessage create(ForceBurnEvent event) {
        // 残高の計算で使用するため、ゾーンIDの降順でソート
        List<ForceDischargeEventDetail> forceDischargeEvents = event.forceDischargeEventDetails.stream()
                .sorted(SORTER_BY_ZONE_ID)
                .toList();

        return TransactionForceBurnMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimestamp(event.blockTimeStamp)
                .validatorId(event.validatorId)
                .accountId(event.accountId)
                .burnedAmount(event.burnedAmount)
                .burnedBalance(event.burnedBalance)
                .zoneIdDescSortedForceDischargeEventDetails(forceDischargeEvents)
                .build();
    }

    public List<TransactionEntity> toTransactions(ValidatorGetAccountResult account) {
        // 強制償却の金額が0円の場合は取引を作成しない
        if (this.burnedAmount.isZero()) {
            log.info("Burned amount is zero. Skip creating transactions. transactionHash: {}", this.transactionHash);
            return List.of();
        }

        List<TransactionEntity> entities = new ArrayList<>();
        Balance finZoneBalance = null;
        Amount previousDisChargeAmount = Amount.of(BigInteger.ZERO);
        String accountName = (account != null) ? account.accountName : null;

        // create force_discharge transaction
        for (ForceDischargeEventDetail item : this.zoneIdDescSortedForceDischargeEventDetails) {

            // 強制ディスチャージの金額が0円の場合は取引を作成しない
            if (item.dischargeAmount.isZero()) {
                log.info("ForceDischarge amount is zero. Skip creating transactions. transactionHash: {}, zoneId: {}",
                         this.transactionHash.getValue(), item.zoneId.getValue());
                continue;
            }

            // 強制ディスチャージ後のFinZoneの取引後残高
            // 一番大きいゾーンIDの取引後残高は強制償却額。それ以外は一つ前の取引後残高から取引額を減算した値
            if (finZoneBalance == null) {
                finZoneBalance = Balance.of(this.burnedAmount.getValue());
            } else {
                finZoneBalance = Balance.of(finZoneBalance.getValue().subtract(previousDisChargeAmount.getValue()));
            }

            // fin transaction
            TransactionEntity finTransaction = TransactionEntity.builder()
                    .transactionHash(this.transactionHash)
                    .accountId(this.accountId)
                    .accountName(accountName)
                    .validatorId(validatorId)
                    .zoneId(ZoneId.financialZoneId())
                    .transactedAt(this.blockTimestamp)
                    .transactionType(TransactionType.FORCE_DISCHARGE)
                    .amount(item.dischargeAmount)
                    .postBalance(finZoneBalance)
                    .otherAccountId(this.accountId)
                    .otherZoneId(item.zoneId)
                    .build();
            entities.add(finTransaction);

            // biz transaction
            TransactionEntity bizTransaction = TransactionEntity.builder()
                    .transactionHash(this.transactionHash)
                    .accountId(this.accountId)
                    .accountName(accountName)
                    .validatorId(this.validatorId)
                    .zoneId(item.zoneId)
                    .transactedAt(this.blockTimestamp)
                    .transactionType(TransactionType.FORCE_DISCHARGE)
                    .amount(Amount.of(item.dischargeAmount.getValue().negate()))
                    .postBalance(Balance.ZERO)
                    .otherAccountId(this.accountId)
                    .otherZoneId(ZoneId.financialZoneId())
                    .build();
            entities.add(bizTransaction);

            // 前のディスチャージ額として保持
            previousDisChargeAmount = item.dischargeAmount;
        }

        // create force_burn transaction
        TransactionEntity forceBurnTransaction = TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.accountId)
                .accountName(accountName)
                .validatorId(this.validatorId)
                .zoneId(ZoneId.financialZoneId())
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.FORCE_BURN)
                .amount(Amount.of(this.burnedAmount.getValue().negate()))
                .postBalance(Balance.ZERO)
                .build();
        entities.add(forceBurnTransaction);

        return entities;
    }
}
