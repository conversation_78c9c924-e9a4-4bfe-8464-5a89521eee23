package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value;

import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.BurnEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ForceBurnEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.IssueVoucherEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.MintEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.RedeemVoucherEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneBalanceEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;

public interface TransactionMessage {

    public List<ValidatorId> validatorIds();

    public BCEventType eventType();

    public static TransactionMessage create(BCEventTypeHolder event) {
        return switch (event.eventType()) {
            case MINT -> TransactionMintMessage.create((MintEvent) event);
            case BURN -> TransactionBurnMessage.create((BurnEvent) event);
            case TRANSFER -> TransactionTransferMessage.create((TransferEvent) event);
            case SYNC_BUSINESS_ZONE_BALANCE ->
                    TransactionSyncBizZoneBalanceMessage.create((SyncBusinessZoneBalanceEvent) event);
            case ISSUE_VOUCHER -> TransactionIssueVoucherMessage.create((IssueVoucherEvent) event);
            case REDEEM_VOUCHER -> TransactionRedeemVoucherMessage.create((RedeemVoucherEvent) event);
            case FORCE_BURN -> TransactionForceBurnMessage.create((ForceBurnEvent) event);
            default -> null;
        };
    }
}
