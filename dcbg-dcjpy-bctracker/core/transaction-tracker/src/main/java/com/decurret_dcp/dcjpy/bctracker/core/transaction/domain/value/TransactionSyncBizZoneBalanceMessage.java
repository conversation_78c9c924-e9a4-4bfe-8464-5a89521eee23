package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value;

import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneBalanceEvent;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class TransactionSyncBizZoneBalanceMessage implements TransactionMessage {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimestamp;

    public final ZoneId zoneId;

    public final ValidatorId fromValidatorId;

    public final AccountId fromAccountId;

    public final String fromAccountName;

    public final Balance fromAccountBalance;

    public final ValidatorId toValidatorId;

    public final AccountId toAccountId;

    public final String toAccountName;

    public final Balance toAccountBalance;

    public final Amount amount;

    @Override
    public List<ValidatorId> validatorIds() {
        return List.of(this.fromValidatorId, this.toValidatorId);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.SYNC_BUSINESS_ZONE_BALANCE;
    }

    public static TransactionSyncBizZoneBalanceMessage create(SyncBusinessZoneBalanceEvent event) {
        return TransactionSyncBizZoneBalanceMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimestamp(event.blockTimeStamp)
                .zoneId(event.zoneId)
                .fromValidatorId(event.fromValidatorId)
                .fromAccountId(event.fromAccountId)
                .fromAccountName(event.fromAccountName)
                .fromAccountBalance(event.fromAccountBalance)
                .toValidatorId(event.toValidatorId)
                .toAccountId(event.toAccountId)
                .toAccountName(event.toAccountName)
                .toAccountBalance(event.toAccountBalance)
                .amount(event.amount)
                .build();
    }

    public TransactionEntity createSyncBizBalanceTransactionFromAccount() {
        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.fromAccountId)
                .accountName(this.fromAccountName)
                .validatorId(this.fromValidatorId)
                .zoneId(this.zoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.TRANSFER)
                .amount(Amount.of(this.amount.getValue().negate()))
                .postBalance(this.fromAccountBalance)
                .otherAccountId(this.toAccountId)
                .otherAccountName(this.toAccountName)
                .build();
    }

    public TransactionEntity createSyncBizBalanceTransactionToAccount() {

        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.toAccountId)
                .accountName(this.toAccountName)
                .validatorId(this.toValidatorId)
                .zoneId(this.zoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.TRANSFER)
                .amount(this.amount)
                .postBalance(this.toAccountBalance)
                .otherAccountId(this.fromAccountId)
                .otherAccountName(this.fromAccountName)
                .build();
    }
}
