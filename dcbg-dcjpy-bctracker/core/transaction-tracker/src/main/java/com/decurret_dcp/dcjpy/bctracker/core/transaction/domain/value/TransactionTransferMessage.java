package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value;

import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.TransferEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.MiscValue;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntities;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMemoEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionMiscEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionSenderEntity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 移転時の取引履歴を保存するためのメッセージ
 */
@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class TransactionTransferMessage implements TransactionMessage {

    /** トランザクションハッシュ. */
    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimestamp;

    public final TransferEventType transferType;

    public final ZoneId zoneId;

    public final ValidatorId fromValidatorId;

    public final AccountId fromAccountId;

    public final String fromAccountName;

    public final Balance fromAccountBalance;

    public final ValidatorId toValidatorId;

    public final AccountId toAccountId;

    public final String toAccountName;

    public final Balance toAccountBalance;

    public final AccountId sendAccountId;

    public final Amount amount;

    public final ZoneId otherZoneId;

    public final Balance businessZoneBalance;

    public final MiscValue miscValue1;

    public final MiscValue miscValue2;

    public final String memo;

    @Override
    public List<ValidatorId> validatorIds() {
        return List.of(this.fromValidatorId, this.toValidatorId);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.TRANSFER;
    }

    public static TransactionTransferMessage create(TransferEvent event) {
        return TransactionTransferMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimestamp(event.blockTimeStamp)
                .transferType(event.transferType)
                .zoneId(event.zoneId)
                .fromValidatorId(event.fromValidatorId)
                .fromAccountId(event.fromAccountId)
                .fromAccountName(event.fromAccountName)
                .fromAccountBalance(event.fromAccountBalance)
                .toValidatorId(event.toValidatorId)
                .toAccountId(event.toAccountId)
                .toAccountName(event.toAccountName)
                .toAccountBalance(event.toAccountBalance)
                .sendAccountId(event.sendAccountId)
                .amount(event.amount)
                .otherZoneId(event.otherZoneId())
                .businessZoneBalance(event.businessZoneBalance)
                .miscValue1(event.miscValue1)
                .miscValue2(event.miscValue2)
                .memo(event.memo)
                .build();
    }

    public TransactionType transactionType() {
        return switch (this.transferType) {
            case CHARGE -> TransactionType.CHARGE;
            case DISCHARGE -> TransactionType.DISCHARGE;
            default -> TransactionType.TRANSFER;
        };
    }

    public TransactionEntities createTransferTransactionEntitiesFromAccount() {

        TransactionEntity fromTransaction = TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.fromAccountId)
                .accountName(this.fromAccountName)
                .validatorId(this.fromValidatorId)
                .zoneId(this.zoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.TRANSFER)
                .amount(Amount.of(this.amount.getValue().negate()))
                .postBalance(this.fromAccountBalance)
                .otherAccountId(this.toAccountId)
                .otherAccountName(this.toAccountName)
                .build();

        TransactionSenderEntity senderEntity = null;
        if (isCreateSender()) {
            senderEntity = TransactionSenderEntity.builder()
                    .sendAccountId(this.sendAccountId)
                    .build();
        }

        TransactionMiscEntity miscEntity = null;
        if (isCreateMisc()) {
            miscEntity = TransactionMiscEntity.builder()
                    .miscValue1(this.miscValue1)
                    .miscValue2(this.miscValue2)
                    .build();
        }

        TransactionMemoEntity memoEntity = null;
        if (isCreateMemo()) {
            memoEntity = TransactionMemoEntity.builder()
                    .memo(this.memo)
                    .build();
        }

        return TransactionEntities.builder()
                .transaction(fromTransaction)
                .transactionSender(senderEntity)
                .transactionMisc(miscEntity)
                .transactionMemo(memoEntity)
                .build();
    }

    public TransactionEntities createTransferTransactionEntitiesToAccount() {

        TransactionEntity toTransaction = TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.toAccountId)
                .accountName(this.toAccountName)
                .validatorId(this.toValidatorId)
                .zoneId(this.zoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.TRANSFER)
                .amount(this.amount)
                .postBalance(this.toAccountBalance)
                .otherAccountId(this.fromAccountId)
                .otherAccountName(this.fromAccountName)
                .build();

        TransactionSenderEntity senderEntity = null;
        if (isCreateSender()) {
            senderEntity = TransactionSenderEntity.builder()
                    .sendAccountId(this.sendAccountId)
                    .build();
        }

        TransactionMiscEntity miscEntity = null;
        if (isCreateMisc()) {
            miscEntity = TransactionMiscEntity.builder()
                    .miscValue1(this.miscValue1)
                    .miscValue2(this.miscValue2)
                    .build();
        }

        TransactionMemoEntity memoEntity = null;
        if (isCreateMemo()) {
            memoEntity = TransactionMemoEntity.builder()
                    .memo(this.memo)
                    .build();
        }

        return TransactionEntities.builder()
                .transaction(toTransaction)
                .transactionSender(senderEntity)
                .transactionMisc(miscEntity)
                .transactionMemo(memoEntity)
                .build();
    }

    public TransactionEntity createChargeTransactionFromAccount() {
        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.fromAccountId)
                .accountName(this.fromAccountName)
                .validatorId(this.fromValidatorId)
                .zoneId(this.zoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.CHARGE)
                .amount(Amount.of(this.amount.getValue().negate()))
                .postBalance(this.fromAccountBalance)
                .otherAccountId(this.fromAccountId)
                .otherZoneId(this.otherZoneId)
                .build();
    }

    public TransactionEntity createChargeTransactionToAccount() {

        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.fromAccountId)
                .validatorId(this.fromValidatorId)
                .zoneId(this.otherZoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.CHARGE)
                .amount(this.amount)
                .postBalance(this.businessZoneBalance)
                .otherAccountId(this.fromAccountId)
                .otherZoneId(this.zoneId)
                .build();
    }

    public TransactionEntity createDisChargeTransactionFromAccount() {
        Amount fromAmount = Amount.of(this.amount.getValue().negate());
        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.toAccountId)
                .validatorId(this.toValidatorId)
                .zoneId(this.otherZoneId)
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.DISCHARGE)
                .amount(fromAmount)
                .postBalance(this.businessZoneBalance)
                .otherAccountId(this.toAccountId)
                .otherZoneId(ZoneId.financialZoneId())
                .build();
    }

    public TransactionEntity createDisChargeTransactionToAccount() {
        // Escrowから送金
        return TransactionEntity.builder()
                .transactionHash(this.transactionHash)
                .accountId(this.toAccountId)
                .accountName(this.toAccountName)
                .validatorId(this.toValidatorId)
                .zoneId(ZoneId.financialZoneId())
                .transactedAt(this.blockTimestamp)
                .transactionType(TransactionType.DISCHARGE)
                .amount(this.amount)
                .postBalance(this.toAccountBalance)
                .otherAccountId(this.toAccountId)
                .otherZoneId(this.otherZoneId)
                .build();
    }

    private boolean isCreateSender() {
        return !this.fromAccountId.equals(this.sendAccountId);
    }

    private boolean isCreateMisc() {
        return this.miscValue1 != null || this.miscValue2 != null;
    }

    private boolean isCreateMemo() {
        return this.memo != null;
    }
}
