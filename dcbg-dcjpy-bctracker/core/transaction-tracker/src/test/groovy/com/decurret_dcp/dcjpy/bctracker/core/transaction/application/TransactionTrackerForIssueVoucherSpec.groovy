package com.decurret_dcp.dcjpy.bctracker.core.transaction.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.IssueVoucherEvent
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionIssueVoucherMessage
import com.decurret_dcp.dcjpy.bctracker.core.transaction.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class TransactionTrackerForIssueVoucherSpec extends Specification {

    @Autowired
    TransactionTracker transactionTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_transaction.fifo")
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * IssueVoucherイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return IssueイベントのnonIndexValuesの値
     */
    private static JsonNode getIssueVoucherIndexValues() {
        String indexValues = """
            {
                "zoneId": "3001",
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * IssueVoucherイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return IssueVoucherイベントのindexValuesの値
     */
    private static JsonNode getIssueVoucherNonIndexValues() {
        String nonIndexValues = """
            {
                "accountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                "accountName": "モックギンコウコウザ1",
                "amount": "2000",
                "balance": "1000",
                "traceId":  [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "testAcceptable_IssueVoucherイベントで、validatorIdがclient entityに存在しない場合"() {
        setup:

        String testData = """
            {
                "zoneId": "3001",
                "validatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """

        JsonNode indexValues = objectMapper.readTree(testData)
        JsonNode nonIndexValues = getIssueVoucherNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("IssueVoucher")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_IssueVoucherイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getIssueVoucherIndexValues()
        JsonNode nonIndexValues = getIssueVoucherNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("IssueVoucher")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorIds().size() == 1
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.eventType() == BCEventType.ISSUE_VOUCHER
    }

    def "testOnMessage_IssueVoucherイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getIssueVoucherIndexValues()
        JsonNode nonIndexValues = getIssueVoucherNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("IssueVoucher")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        IssueVoucherEvent issueVoucherEvent = IssueVoucherEvent.create(testEvent)
        TransactionIssueVoucherMessage transactionIssueVoucherMessagee = TransactionIssueVoucherMessage.create(issueVoucherEvent)

        when:
        def result = this.transactionTracker.onMessage(transactionIssueVoucherMessagee)
        def accountRecord = sql.firstRow("""
            SELECT * FROM transaction WHERE account_id = '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc';
        """)
        def memoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo;
        """
        )
        def miscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc;
        """)
        def senderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender;
        """)


        then:
        result == true
        !Objects.isNull(accountRecord)
        !Objects.isNull(accountRecord.get("transaction_id"))
        accountRecord.get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        accountRecord.get("account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        accountRecord.get("account_name").equals("モックギンコウコウザ1")
        accountRecord.get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        accountRecord.get("zone_id") == 3001
        !Objects.isNull(accountRecord.get("transacted_at"))
        accountRecord.get("transaction_type").equals("charge")
        accountRecord.get("amount") == 2000
        accountRecord.get("post_balance") == 1000
        accountRecord.get("other_account_id").equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc")
        Objects.isNull(accountRecord.get("other_account_name"))
        accountRecord.get("other_zone_id") == 3000

        Objects.isNull(memoRecord)
        Objects.isNull(miscRecord)
        Objects.isNull(senderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """)

    }

}
