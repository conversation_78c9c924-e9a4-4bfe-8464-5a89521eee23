package com.decurret_dcp.dcjpy.bctracker.core.transaction.helper

import groovy.sql.Sql
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest
import software.amazon.awssdk.services.sqs.model.QueueAttributeName
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

class CoreAdhocHelper {

    private static final int DB_POOL_CONNECTION_IDLE_SIZE = 2

    private static final ObjectMapper mapper = new ObjectMapper()

    private static dbPort

    private static localStackPort

    private static DockerComposeContainer composeContainer

    private static connectionCount = 0

    static Sql sql

    static SqsClient sqsClient

    static String queueUri

    static String getDbPort() { return dbPort }

    static String getLocalStackPort() {return localStackPort}

    static {

        startContainer()
    }

    private static void startContainer() {
        // dbが起動した後にpostgresにアクセスできるように、Wait.forLogMessage()でアクセス可能になるログが出力されるまで待つ
        composeContainer = new DockerComposeContainer(new File("../docker-compose_cicd.yml"))
                .withExposedService("db", 5432)
                .withExposedService("localstack", 4566)
                .waitingFor("db", Wait.forListeningPort())
                .waitingFor("localstack", Wait.forListeningPort())
                .waitingFor("db", Wait.forLogMessage(".*database system is ready to accept connections.*\\n", 1))

        composeContainer.start()
        dbPort = String.valueOf(composeContainer.getServicePort("db", 5432))
        localStackPort = String.valueOf(composeContainer.getServicePort("localstack", 4566))
    }

    static Sql initAdhoc(DynamicPropertyRegistry registry) {
        var jdbcUrl = "***************************:${dbPort}/postgres"

        registry.add("spring.datasource.url", () -> jdbcUrl)
        registry.add("bctracker.base.sqs.local-endpoint", () -> "http://localhost:${localStackPort}")

        // テストではコネクションを多く用意する必要はないので、少なめにする
        registry.add("spring.datasource.hikari.minimum-idle", () -> DB_POOL_CONNECTION_IDLE_SIZE)

        sql = Sql.newInstance(jdbcUrl, "postgres", "postgres", "org.postgresql.Driver")

        sql.execute("DROP SCHEMA IF EXISTS public CASCADE")
        sql.execute("CREATE SCHEMA public")
        // DDL およびマスタデータの投入
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000001__create_table.sql").text)

        // テスト用データの投入
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000011__test_init_client_entity.sql").text)
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000012__test_init_entity_signer.sql").text)
        sql.execute(new File("../bctracker-core-local-env/core-db/V00000013__test_init_messages.sql").text)

        return sql
    }

    static void cleanupSpec() {
        sql.close()
        sqsClient.close()

        // テストクラスごとにSpringBootを起動しているにも関わらず、
        // なぜか以前のテストケースでのDBコネクションが残ったままなので、
        // デフォルトのDBコネクションプールのサイズを超えたら、コンテナを再起動する
        connectionCount += DB_POOL_CONNECTION_IDLE_SIZE
        if (connectionCount >= 100) {
            composeContainer.stop()
            connectionCount = 0

            startContainer()
        }
    }

    static SqsClient createSQS(String queueName){
        // 一旦Dead Letter Queueは作成しない。
       sqsClient = SqsClient.builder()
                .region(Region.AP_NORTHEAST_1)
                .endpointOverride(URI.create("http://localhost:${localStackPort}"))
                .build()

        Map<QueueAttributeName, String> createQueueParams = new HashMap<>()
        createQueueParams.put(QueueAttributeName.FIFO_QUEUE, "true")
        createQueueParams.put(QueueAttributeName.VISIBILITY_TIMEOUT, "30")
        createQueueParams.put(QueueAttributeName.CONTENT_BASED_DEDUPLICATION, "true")

        def createQueueRequest = CreateQueueRequest.builder()
            .queueName(queueName)
            .attributes(createQueueParams)
            .build()

        def response = sqsClient.createQueue(createQueueRequest)
        queueUri = response.queueUrl()

        return sqsClient
    }

    static void sendMessage(SqsClient sqsClient, String message) {
        SendMessageRequest messageRequest = SendMessageRequest.builder()
                .queueUrl(queueUri)
                .messageBody(message)
                .messageGroupId("test")
                .build() as SendMessageRequest
        def response = sqsClient.sendMessage(messageRequest)

        if (!response.sdkHttpResponse().statusCode().equals(200)) {
            throw new RuntimeException("Failed send message to " + queueUri)
        }
    }

    static void sendMessage(SqsClient sqsClient, String eventName, Map<String, String> indexedValue, Map<String, String> nonIndexedValue) {
        if (Objects.isNull(eventName) || Objects.isNull(indexedValue) || Objects.isNull(nonIndexedValue) || indexedValue.isEmpty() ||
                nonIndexedValue.isEmpty()) {
            throw new IllegalArgumentException("Arguments must not be null.")
        }

        // 置換しないとエラーになる。
        def message = createSqsMessage(eventName, indexedValue, nonIndexedValue)

        SendMessageRequest messageRequest = SendMessageRequest.builder()
                .queueUrl(queueUri)
                .messageBody(message)
                .messageGroupId("test")
                .build() as SendMessageRequest
        def response = sqsClient.sendMessage(messageRequest)

        if (!response.sdkHttpResponse().statusCode().equals(200)) {
            throw new RuntimeException("Failed send message to " + queueUri)
        }
    }

    private static String createSqsMessage(String eventName, Map<String, String> indexedValue, Map<String, String> nonIndexedValue) {
        StringBuilder template = new StringBuilder("""
        {
            \"Message\": \"{
                \\\"dynamodb\\\": {
                    \\\"NewImage\\\": {
                        \\\"logIndex\\\": {
                            \\\"N\\\": \\\"0\\\"\
                        },
                        \\\"log\\\": {
                            \\\"S\\\": \\\"{
                                \\\\\\\"address\\\\\\\":\\\\\\\"0xeec918d74c746167564401103096d45bbd494b74\\\\\\\"
                            } \\\"
                        },
                        \\\"blockTimestamp\\\": {
                            \\\"N\\\": \\\"1592807820\\\"
                        },
                        \\\"transactionHash\\\": {
                            \\\"S\\\": \\\"0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20\\\"
                        },
                        \\\"name\\\": {
                            \\\"S\\\": \\\"$eventName\\\"
                        },
                        \\\"indexedValues\\\": {
                            \\\"S\\\": \\\"{
        """)
        indexedValue.forEach ((k, v) -> {
            template.append("\\\\\\\"")
                    .append(k)
                    .append("\\\\\\\":")
                    .append(v)
                    .append(",")
        })
        template.deleteCharAt(template.length() - 1)
        template.append("}\\\"")
                .append("},")
        template.append("""
                        \\\"nonIndexedValues\\\": {
                            \\\"S\\\": \\\"{ 
        """)
        nonIndexedValue.forEach ((k, v) -> {
            template.append("\\\\\\\"")
                    .append(k)
                    .append("\\\\\\\":")
                    .append(v)
                    .append(",")
        })
        template.deleteCharAt(template.length() - 1)
        template.append("}\\\"")
                .append("}")
                .append("}")
                .append("}")
                .append("}\"")
                .append("}")

        // 置換しないとエラーになる。
        return template.replaceAll("\n", "").replaceAll("\\s", "")
    }

}
