# dcbg-dcjpy-bctracker

BCTracker モジュールを扱う。

## ディレクトリ構成

```
dcbg-dcjpy-bctracker
├── bctracker-base                      # BCTracker 全体に共通するモジュールを扱う
│   ├── build.gradle
│   └── src
│
├── bpm                                 # BPM パッケージに関するモジュールを扱う
│   ├── bctracker-bpm-local-env         # BPM パッケージで利用する docker compose コンテナのファイルを扱う
│   │   ├── bpm-db
│   │   └── localstack
│   ├── bpm-repository                  # BPM パッケージ共通のリポジトリを扱う
│   │   ├── build.gradle
│   │   └── src
│   ├── email-send-tracker              # メール配信 Tracker を扱う
│   │   ├── build.gradle
│   │   └── src
│   ├── push-notification-tracker       # Push 通知 Tracker を扱う
│   │   ├── build.gradle
│   │   └── src
│   └── docker-compose.yml
│
├── core                                # CORE パッケージに関するモジュールを扱う
│   ├── balance-tracker                 # 残高キャッシュ更新 Tracker を扱う
│   │   ├── build.gradle
│   │   └── src
│   ├── core-repository                 # CORE パッケージ共通のリポジトリを扱う
│   │   ├── build.gradle
│   │   └── src
│   └── transaction-tracker             # 取引履歴保存 Tracker を扱う
│
├── build.common.gradle                 # 全モジュール共通の依存関係を定義する
├── build.gradle
├── gradle
├── gradlew
├── gradlew.bat
├── lombok.config
├── settings.gradle
└── README.md
```

## 初回構築

以下のディレクトリ構成になるよう、必要となる各リポジトリをチェックアウトする。

```
(root dir)
├── dcbg-dcjpy-bctracker            # 本リポジトリ
└── dcbg-dcjpy-bpm-db-migration     # DB 構成管理リポジトリ
```

DB のテーブル構成もしくはテストデータが変更になるたびに
dcbg-dcjpy-bpm-db-migration リポジトリにて `createEntityToBCTracker` タスクを実行する必要がある。

詳細は dcbg-dcjpy-bpm-db-migration リポジトリの `README.md` 参照。

## ローカル環境起動方法

### BPM パッケージ

1. 以下のコマンドでAWSサービスのローカル用コンテナを立ち上げる。

```shell
cd bpm
docker compose up
```
- psqlにログイン
```shell
psql -h localhost -U bpm_user -d bpm_db -p 15432
bpm_password 
```
- 実行前のDBの値を確認
```shell
select * from dc_user_notification; 
```

上記コマンドでは以下のことを行っている

- SQSにキューの作成
- RDSにテーブルの作成およびデータの投入
- SESに対して送信元のメールアドレスの検証処理

2. SpringBootの起動
   起動したいTrackerのリポジトリでSpringBootを起動する。
   方法は以下の二つ

- 起動したいリポジトリのMainクラスをIntelliJで実行する
- gradlew bootRunコマンドで実行する

gradlew bootRunのコマンドは下記
```shell
# EmailTracker の起動
./gradlew bpm:email-send-tracker:bootRun

# PushNotificationTracker の起動
./gradlew bpm:push-notification-tracker:bootRun
```

3. SQSへのキューの投入
   以下のコマンドを実行

```shell
sh ./bctracker-bpm-local-env/sqs/email/transferBizZoneTransactionEnqueue.sh 
```

キューへ投入後はSpringBootのログなどで実行結果を確認する

4. 実行後のBPM DBの値を確認する

```shell
select * from dc_user_notification; 
```


### Core パッケージ

1. 以下のコマンドでAWSサービスのローカル用コンテナを立ち上げる。

```shell
cd core
docker compose up
```

上記コマンドでは以下のことを行っている

- SQSにキューの作成
- RDSにテーブルの作成およびデータの投入
- DynamoDBに残高キャッシュとそのデータの投入

2. SpringBootの起動
   起動したいTrackerのリポジトリでSpringBootを起動する。

   ```shell
   # BalanceTracker の起動
   ./gradlew core:balance-tracker:bootRun
   
   # TransactionTracker の起動
   ./gradlew core:transaction-tracker:bootRun
   ```

3. SQSへのキューの投入
   以下のコマンドを実行

```shell
sh ./bctracker-core-local-env/sqs/balance/issueVoucherBalanceEnqueue.sh 
```

4. 残高キャッシュの確認方法
   ターミナル上で以下のコマンドを実行

```shell
aws dynamodb get-item \
        --table-name balance_cache \
        --endpoint-url http://localhost:14566 \
        --key '{ "account_id": {"S": "60bOvB8L3VxYSCM5QWd1WpSNenGaGFbc" },"zone_id" : {"N" : "3000"}  }'
```

5. Coreのテーブルの確認方法(TransactionTrackerのみ)
   ターミナル上で以下のコマンドを実行

```shell
SELECT * FROM transaction;
SELECT * FROM transaction_memo;
SELECT * FROM transaction_misc;
SELECT * FROM transaction_sender;
```


必要に応じてキーなどは変更すること

