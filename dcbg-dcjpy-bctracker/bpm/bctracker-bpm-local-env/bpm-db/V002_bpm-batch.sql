--Spring Batch が利用するテーブルの登録
CREATE TABLE public.batch_job_instance
(
    job_instance_id bigint       NOT NULL
        PRIMARY KEY,
    version         bigint,
    job_name        varchar(100) NOT NULL,
    job_key         varchar(32)  NOT NULL,
    CONSTRAINT job_inst_un
        UNIQUE (job_name, job_key)
);
CREATE TABLE public.batch_job_execution
(
    job_execution_id           bigint    NOT NULL
        PRIMARY KEY,
    version                    bigint,
    job_instance_id            bigint    NOT NULL
        CONSTRAINT job_inst_exec_fk
            REFERENCES public.batch_job_instance,
    create_time                timestamp NOT NULL,
    start_time                 timestamp,
    end_time                   timestamp,
    status                     varchar(10),
    exit_code                  varchar(2500),
    exit_message               varchar(2500),
    last_updated               timestamp,
    job_configuration_location varchar(2500)
);
CREATE TABLE public.batch_job_execution_params
(
    job_execution_id bigint       NOT NULL
        CONSTRAINT job_exec_params_fk
            REFERENCES public.batch_job_execution,
    type_cd          varchar(6)   NOT NULL,
    key_name         varchar(100) NOT NULL,
    string_val       varchar(250),
    date_val         timestamp,
    long_val         bigint,
    double_val       double precision,
    identifying      char         NOT NULL
);
CREATE TABLE public.batch_step_execution
(
    step_execution_id  bigint       NOT NULL
        PRIMARY KEY,
    version            bigint       NOT NULL,
    step_name          varchar(100) NOT NULL,
    job_execution_id   bigint       NOT NULL
        CONSTRAINT job_exec_step_fk
            REFERENCES public.batch_job_execution,
    start_time         timestamp    NOT NULL,
    end_time           timestamp,
    status             varchar(10),
    commit_count       bigint,
    read_count         bigint,
    filter_count       bigint,
    write_count        bigint,
    read_skip_count    bigint,
    write_skip_count   bigint,
    process_skip_count bigint,
    rollback_count     bigint,
    exit_code          varchar(2500),
    exit_message       varchar(2500),
    last_updated       timestamp
);
CREATE TABLE public.batch_step_execution_context
(
    step_execution_id  bigint        NOT NULL
        PRIMARY KEY
        CONSTRAINT step_exec_ctx_fk
            REFERENCES public.batch_step_execution,
    short_context      varchar(2500) NOT NULL,
    serialized_context text
);
CREATE TABLE public.batch_job_execution_context
(
    job_execution_id   bigint        NOT NULL
        PRIMARY KEY
        CONSTRAINT job_exec_ctx_fk
            REFERENCES public.batch_job_execution,
    short_context      varchar(2500) NOT NULL,
    serialized_context text
);
CREATE SEQUENCE batch_step_execution_seq;
CREATE SEQUENCE batch_job_execution_seq;
CREATE SEQUENCE batch_job_seq;
