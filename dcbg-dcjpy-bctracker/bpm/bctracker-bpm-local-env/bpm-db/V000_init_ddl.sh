#!/bin/bash

# 暫定的に XML 型で定義したものを JSONB 型に変換する
# 2023年11月末時点の最新の docker desktop 環境だと `sed -i` が想定通りの動作をしないため、いったん -i オプションを利用しないように暫定対処
# sed -i 's/ xml/ jsonb/g' /docker-entrypoint-initdb.d/V001_bpm-server.sql
sed 's/ xml/ jsonb/g' /docker-entrypoint-initdb.d/V001_bpm-server.sql > /docker-entrypoint-initdb.d/V001_bpm-server.sql.temp
mv -f /docker-entrypoint-initdb.d/V001_bpm-server.sql.temp /docker-entrypoint-initdb.d/V001_bpm-server.sql
