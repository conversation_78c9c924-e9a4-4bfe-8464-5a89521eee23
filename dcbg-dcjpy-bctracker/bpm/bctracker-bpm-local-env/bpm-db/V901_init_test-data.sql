-- テスト用ロール
INSERT INTO dc_user_role (role_id, role_name, role_type, service_id, created_at)
VALUES ('********-0000-4000-0000-********0100', '個人ユーザ', 'individual', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0110', '口座管理者', 'account_owner', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0111', 'ユーザ管理者', 'user_owner', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0112', '業務担当者', 'operator', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0113', '業務承認者', 'reviewer', '1', CURRENT_TIMESTAMP);

INSERT INTO dc_user_role (role_id, role_name, role_type, service_id, belonged_dc_bank_number, created_at)
VALUES ('********-0000-4000-0000-********1012', '業務担当者 (アカウントA用)', 'operator', '0', 'DC001-1234-1234-1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********1013', '業務承認者 (アカウントA用)', 'reviewer', '0', 'DC001-1234-1234-1', CURRENT_TIMESTAMP);

INSERT INTO dc_user_role_authority (role_id, authority_key)
VALUES ('********-0000-4000-0000-********0110', 'mint'),
       ('********-0000-4000-0000-********0110', 'burn'),
       ('********-0000-4000-0000-********0110', 'transfer'),
       ('********-0000-4000-0000-********0110', 'charge'),
       ('********-0000-4000-0000-********0110', 'discharge'),
       ('********-0000-4000-0000-********0110', 'view_account'),
       ('********-0000-4000-0000-********0110', 'view_transactions'),
       ('********-0000-4000-0000-********0110', 'change_account'),
       ('********-0000-4000-0000-********0112', 'mint'),
       ('********-0000-4000-0000-********0112', 'burn'),
       ('********-0000-4000-0000-********0112', 'transfer'),
       ('********-0000-4000-0000-********0112', 'charge'),
       ('********-0000-4000-0000-********0112', 'discharge'),
       ('********-0000-4000-0000-********0112', 'view_account'),
       ('********-0000-4000-0000-********0112', 'view_transactions'),
       ('********-0000-4000-0000-********0112', 'change_account'),
       ('********-0000-4000-0000-********0113', 'mint'),
       ('********-0000-4000-0000-********0113', 'burn'),
       ('********-0000-4000-0000-********0113', 'transfer'),
       ('********-0000-4000-0000-********0113', 'charge'),
       ('********-0000-4000-0000-********0113', 'discharge'),
       ('********-0000-4000-0000-********0113', 'view_account'),
       ('********-0000-4000-0000-********0113', 'view_transactions'),
       ('********-0000-4000-0000-********0113', 'change_account'),
       ('********-0000-4000-0000-********1012', 'mint'),
       ('********-0000-4000-0000-********1012', 'burn'),
       ('********-0000-4000-0000-********1012', 'view_account'),
       ('********-0000-4000-0000-********1013', 'mint'),
       ('********-0000-4000-0000-********1013', 'burn');

INSERT INTO service_user_role (role_id, role_name, role_type, service_id, created_at)
VALUES ('********-0000-4000-0000-************', 'サービス管理者', 'service_owner', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0221', 'ユーザ管理者', 'user_owner', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0222', '業務担当者', 'operator', '1', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0223', '業務承認者', 'reviewer', '1', CURRENT_TIMESTAMP);

INSERT INTO service_user_role_authority (role_id, authority_key)
VALUES ('********-0000-4000-0000-************', 'view_accounts'),
       ('********-0000-4000-0000-************', 'view_account_detail'),
       ('********-0000-4000-0000-************', 'view_transactions'),
       ('********-0000-4000-0000-************', 'change_account'),
       ('********-0000-4000-0000-************', 'reset_authentication'),
       ('********-0000-4000-0000-************', 'user_suspended'),
       ('********-0000-4000-0000-************', 'user_activated'),
       ('********-0000-4000-0000-************', 'account_frozen'),
       ('********-0000-4000-0000-************', 'account_activated'),
       ('********-0000-4000-0000-************', 'account_force_burned'),
       ('********-0000-4000-0000-************', 'account_force_terminated'),
       ('********-0000-4000-0000-************', 'view_information'),
       ('********-0000-4000-0000-************', 'change_information'),
       ('********-0000-4000-0000-********0222', 'view_accounts'),
       ('********-0000-4000-0000-********0222', 'view_account_detail'),
       ('********-0000-4000-0000-********0222', 'view_transactions'),
       ('********-0000-4000-0000-********0222', 'change_account'),
       ('********-0000-4000-0000-********0222', 'reset_authentication'),
       ('********-0000-4000-0000-********0222', 'user_suspended'),
       ('********-0000-4000-0000-********0222', 'user_activated'),
       ('********-0000-4000-0000-********0222', 'account_frozen'),
       ('********-0000-4000-0000-********0222', 'account_activated'),
       ('********-0000-4000-0000-********0222', 'account_force_burned'),
       ('********-0000-4000-0000-********0222', 'account_force_terminated'),
       ('********-0000-4000-0000-********0222', 'view_information'),
       ('********-0000-4000-0000-********0222', 'change_information'),
       ('********-0000-4000-0000-********0223', 'view_accounts'),
       ('********-0000-4000-0000-********0223', 'view_account_detail'),
       ('********-0000-4000-0000-********0223', 'view_transactions'),
       ('********-0000-4000-0000-********0223', 'change_account'),
       ('********-0000-4000-0000-********0223', 'reset_authentication'),
       ('********-0000-4000-0000-********0223', 'user_suspended'),
       ('********-0000-4000-0000-********0223', 'user_activated'),
       ('********-0000-4000-0000-********0223', 'account_frozen'),
       ('********-0000-4000-0000-********0223', 'account_activated'),
       ('********-0000-4000-0000-********0223', 'account_force_burned'),
       ('********-0000-4000-0000-********0223', 'account_force_terminated'),
       ('********-0000-4000-0000-********0223', 'view_information'),
       ('********-0000-4000-0000-********0223', 'change_information');

INSERT INTO validator_account (validator_id, dc_bank_number)
VALUES ('80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 'DC001-1234-1234-1');

-- サービスオーナ関連
INSERT INTO service_owner (service_id, service_name, zone_id, validator_id, issuer_id)
VALUES ('0', '-', '3000', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', '200zzuqQ4uRL1ZS7waHE62037JyMrPEV'),
       ('1', '-', '3000', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', '200zzuqQ4uRL1ZS7waHE62037JyMrPEV');

--INSERT INTO auth_client(entity_id, entity_type, client_id, client_secret)
--VALUES ('80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 'validator', 'test_client_id__validator1', 'test_secret'),
--       ('200zzuqQ4uRL1ZS7waHE62037JyMrPEV', 'issuer', 'test_client_id__issuer1', 'test_secret');

-- 法人ユーザ関連
INSERT INTO dc_account(dc_bank_number, service_id, account_id, core_linked)
VALUES ('DC001-1234-1234-1', '0', '60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb', TRUE),
       ('DC001-2222-0102-1', '0', '601nYszi6baA9yiXsjn0fMelnsbi1sA6', TRUE),
       ('DC001-3333-0103-1', '1', '601N3ZpA5DPqiq0O6ypM2nv5NZDA1Zq5', TRUE);

INSERT INTO bank_account(
                        dc_bank_number, service_id, bank_account_id, bank_name, bank_code, branch_code,
                        bank_account_type, bank_account_number, bank_account_name)
VALUES ('DC001-1234-1234-1', '0', '0310-302-02-1234567', 'モック銀行', '0310', '302', '01', '1234567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1'),
       ('DC001-2222-0102-1', '0', '0310-302-02-8834567', 'モック銀行', '0310', '302', '02', '8834567', 'ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ2');

INSERT INTO dc_account_bank_linkage(dc_bank_number, service_id, bank_link_status, expires_at)
VALUES ('DC001-1234-1234-1', '0', 'authenticated', CURRENT_TIMESTAMP + INTERVAL '30 minute'),
       ('DC001-2222-0102-1', '0', 'authenticated', CURRENT_TIMESTAMP + INTERVAL '30 minute'),
       ('DC001-3333-0103-1', '1', 'authenticated', CURRENT_TIMESTAMP + INTERVAL '30 minute');

INSERT INTO dc_user(sign_in_id, user_name, dc_user_type, user_status, service_id, role_id, registered_at)
VALUES -- アカウントA : 通常のデータ
       ('SID01AA-1001', 'テストアカウント管理者A', 'company_owner', 'active', '0', '********-0000-4000-0000-********0010', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1011', 'テストユーザ管理者A1', 'company', 'active', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1012', 'テストユーザ管理者A2', 'company', 'active', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1013', 'テストユーザ管理者A3', 'company', 'active', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1021', 'テスト業務担当者A1', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1022', 'テスト業務担当者A2', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1023', 'テスト業務担当者A3', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1031', 'テスト業務承認者A1', 'company', 'active', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1032', 'テスト業務承認者A2', 'company', 'active', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1033', 'テスト業務承認者A3', 'company', 'active', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),
       -- アカウントA : サインイン停止
       ('SID01AA-1411', 'サインイン停止 ユーザ管理者A', 'company', 'suspended', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1421', 'テスト業務担当者A1 (サインイン停止)', 'company', 'suspended', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1422', 'サインイン停止 テスト業務担当者A2', 'company', 'suspended', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1431', 'サインイン停止 事業承認者A1', 'company', 'suspended', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1432', 'サインイン停止 事業承認者A2', 'company', 'suspended', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),
       -- アカウントA : サインイン一時停止
       ('SID01AA-1521', 'テスト業務担当者A1 サインイン一時停止', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1522', 'テスト業務担当者A2 サインイン一時停止', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1523', 'サインイン一時停止 テスト業務担当者A3', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       -- アカウントA : 初期設定待ち
       ('SID01AA-1711', '初期設定待ちユーザ管理者A1', 'company', 'initializing', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1721', '初期設定待ち業務担当者A1', 'company', 'initializing', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1722', '認証リセット業務担当者A2', 'company', 'initializing', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1731', '初期設定待ち業務承認者A1', 'company', 'initializing', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),
       -- アカウントA : ユーザ無効
       ('SID01AA-1911', 'ユーザ無効ユーザ管理者A', 'company', 'inactive', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1921', 'ユーザ無効業務担当者A', 'company', 'inactive', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1931', 'ユーザ無効業務承認者A', 'company', 'inactive', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),

       -- アカウントB : 通常のデータ
       ('SID01AB-2001', 'テストアカウント管理者B', 'company_owner', 'active', '0', '********-0000-4000-0000-********0010', '2023-12-01T12:00:00+0900'),
       ('SID01AB-2011', 'テストユーザ管理者B', 'company', 'active', '0', '********-0000-4000-0000-********0011', '2023-12-01T12:00:00+0900'),
       ('SID01AB-2021', 'テスト業務担当者B', 'company', 'active', '0', '********-0000-4000-0000-********0012', '2023-12-01T12:00:00+0900'),
       ('SID01AB-2031', 'テスト業務承認者B', 'company', 'active', '0', '********-0000-4000-0000-********0013', '2023-12-01T12:00:00+0900'),

       -- アカウントC : 通常のデータ(別テナント)
       ('SID01AC-3001', 'テストアカウント管理者C', 'company_owner', 'active', '1', '********-0000-4000-0000-********0110', '2023-12-01T12:00:00+0900'),
       ('SID01AC-3011', 'テストユーザ管理者C', 'company', 'active', '1', '********-0000-4000-0000-********0111', '2023-12-01T12:00:00+0900'),
       ('SID01AC-3021', 'テスト業務担当者C', 'company', 'active', '1', '********-0000-4000-0000-********0112', '2023-12-01T12:00:00+0900'),
       ('SID01AC-3031', 'テスト業務承認者C', 'company', 'active', '1', '********-0000-4000-0000-********0113', '2023-12-01T12:00:00+0900'),

       -- アカウントなし
       ('SID0100-4001', 'テストアカウント管理者X', 'company_owner', 'active', '0', '********-0000-4000-0000-********0010', '2023-12-01T12:00:00+0900'),
       ('SID9999-9990', 'テストアカウント管理者_アカウント未開設', 'company_owner', 'active', '0', '********-0000-4000-0000-********0010', '2023-12-01T12:00:00+0900');


INSERT INTO dc_user_sign_in_history(sign_in_id, current_signed_in_at)
VALUES -- アカウントA : 通常のデータ
       ('SID01AA-1001', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1011', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1012', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1013', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1021', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1022', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1023', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1031', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1032', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1033', '2023-12-01T12:00:00+0900'),
       -- アカウントA : サインイン停止
       ('SID01AA-1411', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1421', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1422', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1431', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1432', '2023-12-01T12:00:00+0900'),
       -- アカウントA : サインイン一時停止
       ('SID01AA-1521', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1522', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1523', '2023-12-01T12:00:00+0900'),
       -- アカウントA : 初期設定待ち
       ('SID01AA-1711', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1721', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1722', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1731', '2023-12-01T12:00:00+0900'),
       -- アカウントA : ユーザ無効
       ('SID01AA-1911', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1921', '2023-12-01T12:00:00+0900'),
       ('SID01AA-1931', '2023-12-01T12:00:00+0900'),

       -- アカウントB : 通常のデータ
       ('SID01AB-2001', '2023-12-01T12:00:00+0900'),
       ('SID01AB-2011', '2023-12-01T12:00:00+0900'),
       ('SID01AB-2021', '2023-12-01T12:00:00+0900'),
       ('SID01AB-2031', '2023-12-01T12:00:00+0900'),

       -- アカウントC : 通常のデータ
       ('SID01AC-3001', '2023-12-01T12:00:00+0900'),
       ('SID01AC-3011', '2023-12-01T12:00:00+0900'),
       ('SID01AC-3021', '2023-12-01T12:00:00+0900'),
       ('SID01AC-3031', '2023-12-01T12:00:00+0900'),

       -- アカウントなし
       ('SID0100-4001', '2023-12-01T12:00:00+0900'),
       ('SID9999-9990', '2023-12-01T12:00:00+0900');

INSERT INTO dc_user_account(sign_in_id, dc_bank_number, service_id)
VALUES -- アカウントA : 通常のデータ
       ('SID01AA-1001', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1011', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1021', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1031', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1012', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1022', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1032', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1013', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1023', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1033', 'DC001-1234-1234-1', '0'),
       -- アカウントA : サインイン停止
       ('SID01AA-1421', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1422', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1411', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1431', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1432', 'DC001-1234-1234-1', '0'),
       -- アカウントA : サインイン一時停止
       ('SID01AA-1521', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1522', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1523', 'DC001-1234-1234-1', '0'),
       -- アカウントA : 初期設定待ち
       ('SID01AA-1711', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1721', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1722', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1731', 'DC001-1234-1234-1', '0'),
       -- アカウントA : ユーザ無効
       ('SID01AA-1911', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1921', 'DC001-1234-1234-1', '0'),
       ('SID01AA-1931', 'DC001-1234-1234-1', '0'),

       -- アカウントB : 通常のデータ
       ('SID01AB-2001', 'DC001-2222-0102-1', '0'),
       ('SID01AB-2011', 'DC001-2222-0102-1', '0'),
       ('SID01AB-2021', 'DC001-2222-0102-1', '0'),
       ('SID01AB-2031', 'DC001-2222-0102-1', '0'),

       -- アカウントC : 通常のデータ
       ('SID01AC-3001', 'DC001-3333-0103-1', '1'),
       ('SID01AC-3011', 'DC001-3333-0103-1', '1'),
       ('SID01AC-3021', 'DC001-3333-0103-1', '1'),
       ('SID01AC-3031', 'DC001-3333-0103-1', '1');

INSERT INTO dc_user_phone(sign_in_id, phone_number)
VALUES -- アカウントA : 通常のデータ
       ('SID01AA-1001', '07012341001'),
       ('SID01AA-1011', '07012341011'),
       ('SID01AA-1012', '07012341012'),
       ('SID01AA-1013', '07012341013'),
       ('SID01AA-1021', '07012341021'),
       ('SID01AA-1022', '07012341022'),
       ('SID01AA-1023', '07012341023'),
       ('SID01AA-1031', '07012341031'),
       ('SID01AA-1032', '07012341032'),
       ('SID01AA-1033', '07012341033'),
       -- アカウントA : サインイン停止
       ('SID01AA-1411', '07012341411'),
       ('SID01AA-1421', '07012341421'),
       ('SID01AA-1422', '07012341322'),
       ('SID01AA-1431', '07012341431'),
       -- アカウントA : サインイン一時停止
       ('SID01AA-1521', '07012341521'),
       ('SID01AA-1522', '07012341522'),
       ('SID01AA-1523', '07012341523'),
       -- アカウントA : 初期設定待ち
       ('SID01AA-1711', '07012341711'),
       ('SID01AA-1721', '07012341721'),
       ('SID01AA-1722', '07012341722'),
       ('SID01AA-1731', '07012341731'),
       -- アカウントA : ユーザ無効
       ('SID01AA-1911', '07012341911'),
       ('SID01AA-1921', '07012341921'),
       ('SID01AA-1931', '07012341931'),

       -- アカウントB : 通常のデータ
       ('SID01AB-2001', '07012342001'),
       ('SID01AB-2011', '07012342011'),
       ('SID01AB-2021', '07012342021'),
       ('SID01AB-2031', '07012342031'),

       -- アカウントC : 通常のデータ
       ('SID01AC-3001', '07012343001'),
       ('SID01AC-3011', '07012343011'),
       ('SID01AC-3021', '07012343021'),
       ('SID01AC-3031', '07012343031');

INSERT INTO dc_user_totp(sign_in_id, totp_secret)
VALUES -- アカウントA : 通常のデータ
       ('SID01AA-1001', 'AAAAAAAAAAAAAAAA'),
       ('SID01AA-1011', 'BBBBBBBBBBBBBBBB'),
       ('SID01AA-1021', 'CCCCCCCCCCCCCCCC'),
       ('SID01AA-1031', 'DDDDDDDDDDDDDDDD'),
       ('SID01AA-1422', 'DDDDDDDDDDDDDDDD'),
       -- サインイン一時停止
       ('SID01AA-1521', 'EEEEEEEEEEEEEEEE'),

       -- アカウントなし
       ('SID0100-4001', 'AAAAAAAAAAAAAAAA');

INSERT INTO dc_user_reason(sign_in_id, operation_type, reason_code, reason_detail, operated_at)
VALUES -- アカウントA : サインイン停止
       ('SID01AA-1411', 'user_suspended', 'UUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID01AA-1421', 'user_suspended', 'UUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID01AA-1422', 'user_suspended', 'UUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID01AA-1431', 'user_suspended', 'UUSS0101', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID01AA-1432', 'user_suspended', 'UUSS0101', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute');

INSERT INTO dc_user_sign_in_failure(sign_in_id, failure_count, last_failed_at, lock_expires_at)
VALUES --アカウントA : サインイン一時停止
       ('SID01AA-1521', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour'),
       ('SID01AA-1522', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour'),
       ('SID01AA-1523', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour');


-- 銀行/事業者ユーザ 関連

INSERT INTO service_user(sign_in_id, user_name, service_user_type, user_status, service_id, role_id, registered_at)
VALUES -- 通常のデータ
       ('SID11AA-1001', 'テストサービス管理者A', 'owner', 'active', '0', '********-0000-4000-0000-********0020', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1011', 'テストユーザ管理者A', 'normal', 'active', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1021', 'テスト業務担当者A1', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1031', 'テスト業務承認者A1', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       -- サインイン停止
       ('SID11AA-1022', 'テスト業務担当者A2', 'normal', 'suspended', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1101', 'サインイン停止ユーザ管理者', 'normal', 'suspended', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1111', 'サインイン停止事業承認者', 'normal', 'suspended', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       -- サインイン一時停止
       ('SID11AA-1023', 'テスト業務担当者A3', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       -- 初期設定待ち
       ('SID11AA-1071', '初期設定待ちユーザ管理者', 'normal', 'initializing', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1081', '初期設定待ち業務担当者', 'normal', 'initializing', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1091', '初期設定待ち業務承認者', 'normal', 'initializing', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       -- ユーザ無効
       ('SID11AA-1121', 'ユーザ無効ユーザ管理者', 'normal', 'inactive', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1131', 'ユーザ無効業務担当者', 'normal', 'inactive', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1141', 'ユーザ無効業務承認者', 'normal', 'inactive', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),

       -- ステータス変更用ユーザ
       ('SID11AA-1211', '認証リセット用ユーザ管理者', 'normal', 'active', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1212', 'ロール変更用ユーザ管理者', 'normal', 'active', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1213', '状態変更用ユーザ管理者', 'normal', 'active', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1221', '認証リセット用業務担当者', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1222', '認証リセット用業務担当者', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1223', '認証リセット用業務担当者', 'normal', 'initializing', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1224', '認証リセット用業務担当者', 'normal', 'suspended', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1225', '認証リセット用業務担当者(一時停止)', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1226', 'ロール変更用業務担当者', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1227', '状態変更用業務担当者', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1228', '状態変更用業務担当者', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1229', '状態変更用業務担当者(一時停止)', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),

       ('SID11AA-1231', '認証リセット用業務承認者', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1232', '認証リセット用業務承認者', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1233', 'ロール変更用業務承認者', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1234', '状態変更用業務承認者', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1235', '状態変更用業務承認者', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1236', '状態変更用業務承認者(一時停止)', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),

       -- 通常のデータ
       ('SID11AB-2011', 'テストユーザ管理者B', 'normal', 'active', '0', '********-0000-4000-0000-********0021', '2023-12-01T12:00:00+0900'),
       ('SID11AB-2021', 'テスト業務担当者B', 'normal', 'active', '0', '********-0000-4000-0000-********0022', '2023-12-01T12:00:00+0900'),
       ('SID11AB-2031', 'テスト業務承認者B', 'normal', 'active', '0', '********-0000-4000-0000-********0023', '2023-12-01T12:00:00+0900'),

       -- 通常のデータ(別テナント)
       ('SID11AB-3001', 'テストサービス管理者C', 'owner', 'active', '1', '********-0000-4000-0000-************', '2023-12-01T12:00:00+0900'),
       ('SID11AB-3011', 'テストユーザ管理者C', 'normal', 'active', '1', '********-0000-4000-0000-********0221', '2023-12-01T12:00:00+0900'),
       ('SID11AB-3021', 'テスト業務担当者C', 'normal', 'active', '1', '********-0000-4000-0000-********0222', '2023-12-01T12:00:00+0900'),
       ('SID11AB-3031', 'テスト業務承認者C', 'normal', 'active', '1', '********-0000-4000-0000-********0223', '2023-12-01T12:00:00+0900');

INSERT INTO service_user_sign_in_history(sign_in_id, current_signed_in_at)
VALUES -- 通常のデータ
       ('SID11AA-1001', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1011', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1021', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1031', '2023-12-01T12:00:00+0900'),
       -- サインイン停止
       ('SID11AA-1022', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1101', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1111', '2023-12-01T12:00:00+0900'),
       -- サインイン一時停止
       ('SID11AA-1023', '2023-12-01T12:00:00+0900'),
       -- 初期設定待ち
       ('SID11AA-1071', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1081', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1091', '2023-12-01T12:00:00+0900'),
       -- ユーザ無効
       ('SID11AA-1121', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1131', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1141', '2023-12-01T12:00:00+0900'),

       -- ステータス変更用ユーザ
       ('SID11AA-1211', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1212', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1213', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1221', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1222', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1223', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1224', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1225', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1226', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1227', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1228', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1229', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1231', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1232', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1233', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1234', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1235', '2023-12-01T12:00:00+0900'),
       ('SID11AA-1236', '2023-12-01T12:00:00+0900'),

       -- 通常のデータ
       ('SID11AB-2011', '2023-12-01T12:00:00+0900'),
       ('SID11AB-2021', '2023-12-01T12:00:00+0900'),
       ('SID11AB-2031', '2023-12-01T12:00:00+0900'),

       -- 通常のデータ (別テナント)
       ('SID11AB-3001', '2023-12-01T12:00:00+0900'),
       ('SID11AB-3011', '2023-12-01T12:00:00+0900'),
       ('SID11AB-3021', '2023-12-01T12:00:00+0900'),
       ('SID11AB-3031', '2023-12-01T12:00:00+0900');

INSERT INTO service_user_phone(sign_in_id, phone_number)
VALUES -- 通常のデータ
       ('SID11AA-1001', '07012341001'),
       ('SID11AA-1011', '07012341011'),
       ('SID11AA-1021', '07012341021'),
       ('SID11AA-1031', '07012341031'),
       -- サインイン停止
       ('SID11AA-1022', '07012341022'),
       ('SID11AA-1101', '07012341101'),
       ('SID11AA-1111', '07012341111'),
       -- サインイン一時停止
       ('SID11AA-1023', '07012341023'),
       -- 初期設定待ち
       ('SID11AA-1071', '07012341071'),
       ('SID11AA-1081', '07012341081'),
       ('SID11AA-1091', '07012341091'),
       -- ユーザ無効
       ('SID11AA-1121', '07012341121'),
       ('SID11AA-1131', '07012341131'),
       ('SID11AA-1141', '07012341141'),

       -- ステータス変更用ユーザ
       ('SID11AA-1211', '07012341211'),
       ('SID11AA-1212', '07012341212'),
       ('SID11AA-1213', '07012341213'),
       ('SID11AA-1221', '07012341221'),
       ('SID11AA-1222', '07012341222'),
       ('SID11AA-1223', '07012341223'),
       ('SID11AA-1224', '07012341224'),
       ('SID11AA-1225', '07012341225'),
       ('SID11AA-1226', '07012341226'),
       ('SID11AA-1227', '07012341227'),
       ('SID11AA-1228', '07012341228'),
       ('SID11AA-1229', '07012341229'),
       ('SID11AA-1231', '07012341231'),
       ('SID11AA-1232', '07012341232'),
       ('SID11AA-1233', '07012341233'),
       ('SID11AA-1234', '07012341234'),
       ('SID11AA-1235', '07012341235'),
       ('SID11AA-1236', '07012341236'),

       -- 通常のデータ
       ('SID11AB-2011', '07012342011'),
       ('SID11AB-2021', '07012342021'),
       ('SID11AB-2031', '07012342031'),

       -- 通常のデータ (別テナント)
       ('SID11AB-3001', '07012343001'),
       ('SID11AB-3011', '07012343011'),
       ('SID11AB-3021', '07012343021'),
       ('SID11AB-3031', '07012343031');

INSERT INTO service_user_totp(sign_in_id, totp_secret)
VALUES -- 通常のデータ
       ('SID11AA-1001', 'AAAAAAAAAAAAAAAA'),
       ('SID11AA-1011', 'BBBBBBBBBBBBBBBB'),
       ('SID11AA-1021', 'CCCCCCCCCCCCCCCC'),
       ('SID11AA-1031', 'DDDDDDDDDDDDDDDD'),
       -- サインイン一時停止
       ('SID11AA-1023', 'EEEEEEEEEEEEEEEE');

INSERT INTO service_user_reason(sign_in_id, operation_type, reason_code, reason_detail, operated_at)
VALUES -- サインイン停止
       ('SID11AA-1022', 'user_suspended', 'SUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID11AA-1101', 'user_suspended', 'SUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID11AA-1111', 'user_suspended', 'SUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute'),
       ('SID11AA-1224', 'user_suspended', 'SUSS0001', '2023/10/15 本人より連絡あり', CURRENT_TIMESTAMP - INTERVAL '30 minute');

INSERT INTO service_user_sign_in_failure(sign_in_id, failure_count, last_failed_at, lock_expires_at)
VALUES -- サインイン一時停止
       ('SID11AA-1023', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour'),
       ('SID11AA-1225', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour'),
       ('SID11AA-1229', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour'),
       ('SID11AA-1236', 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '1 hour');

INSERT INTO dc_user_oauth_code (oauth_code, sign_in_id, expires_at)
VALUES
    ('test********1', 'SID01AA-1001', CURRENT_TIMESTAMP + INTERVAL '1 hour'),
    ('test********2', 'SID01AB-2001', CURRENT_TIMESTAMP + INTERVAL '1 hour');
insert into dc_user_device(sign_in_id, push_token, os_type) values ('SID01AA-1001', 'xxxxxxxxxxxxxx', 'android');

INSERT INTO dc_account_email (dc_bank_number, service_id, email_address)
VALUES ('DC001-1234-1234-1', '0', '<EMAIL>');