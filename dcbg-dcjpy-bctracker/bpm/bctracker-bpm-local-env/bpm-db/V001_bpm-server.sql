

/* Create Sequences */

CREATE SEQUENCE seq_dc_bank_number INCREMENT 1 MINVALUE ******** START ********;



/* Create Tables */

-- アプリケーションバージョン : 認証アプリのバージョンを扱う
CREATE TABLE app_version
(
	-- バージョンID
	version_id varchar(36) NOT NULL UNIQUE,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- OS種別 : ios : iOS
	-- android : Android
	os_type varchar(8) NOT NULL,
	-- アプリケーションバージョン
	app_version varchar(16) NOT NULL,
	-- 利用可否
	available boolean NOT NULL,
	-- 作成日時
	created_at timestamp with time zone NOT NULL,
	PRIMARY KEY (version_id)
) WITHOUT OIDS;


-- 認証クライアント : CoreAPI の接続情報を扱う
CREATE TABLE auth_client
(
	-- エンティティID
	entity_id varchar(36) NOT NULL,
	-- エンティティ種別 : admin : アドミニストレータ
	-- provider : プロバイダ
	-- validator : バリデータ
	-- issuer : イシュア
	entity_type varchar(16) NOT NULL,
	-- クライアントID
	client_id varchar(64) NOT NULL,
	PRIMARY KEY (entity_id, entity_type)
) WITHOUT OIDS;


-- 銀行預金口座 : DCアカウントに紐づく銀行預金口座情報を扱う。
CREATE TABLE bank_account
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 銀行預金口座ID
	bank_account_id varchar(255) NOT NULL,
	-- 銀行コード
	bank_code varchar(4) NOT NULL,
	-- 銀行名
	bank_name varchar(20) NOT NULL,
	-- 支店コード
	branch_code varchar(3) NOT NULL,
	-- 銀行預金口座種別 : saving : 普通口座
	-- checking : 貯蓄口座
	bank_account_type varchar(16) NOT NULL,
	-- 銀行預金口座番号
	bank_account_number varchar(7) NOT NULL,
	-- 銀行預金口座名義
	bank_account_name varchar(255),
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- 償却履歴 : DCJPY 発行 / 償却の処理の状態を管理する。
CREATE TABLE burn_transaction
(
	-- リクエストID
	request_id varchar(128) NOT NULL,
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- burn取引状態 : initialize : 処理開始前
	-- burned : DC口座からDCJPY償却済み
	-- burn_failed : DC口座からDCJPY償却ができなかった
	-- deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗
	-- completed : DCJPY発行/償却手続きが正常に完了
	burn_status varchar(16) NOT NULL,
	-- 金額
	amount bigint NOT NULL,
	-- 初期登録日時
	initialized_at timestamp with time zone NOT NULL,
	-- CoreAPI処理結果日時
	coreapi_resulted_at timestamp with time zone,
	-- BankGW処理結果日時
	bankgw_resulted_at timestamp with time zone,
	PRIMARY KEY (request_id, dc_bank_number)
) WITHOUT OIDS;


-- DC口座 : DC口座を扱う。
CREATE TABLE dc_account
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- アカウントID
	account_id varchar(64) NOT NULL,
	-- Coreモジュール接続状態 : Coreモジュールにアカウントを作成済みかを示す
	core_linked boolean NOT NULL,
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- DC口座IB接続 : DC口座を扱う。
CREATE TABLE dc_account_bank_linkage
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- IB接続状態 : authenticated : 認証済み
	-- need_auth : 認証が必要
	bank_link_status varchar(16) NOT NULL,
	-- 有効期限
	expires_at timestamp with time zone,
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- DC口座メールアドレス : アカウントに紐づく、取引完了時にメール送信する宛先を扱う
CREATE TABLE dc_account_email
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 取引完了通知先メールアドレス
	email_address varchar(255) NOT NULL,
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- DC口座精算予約設定 : アカウントの支払い精算に関する予約設定を扱う。
-- 月次設定を月中に変更手続きを行い、指定された日付で実設定に反映するため
CREATE TABLE dc_account_future_settlement
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 適用日時
	apply_at timestamp with time zone NOT NULL,
	-- 精算条件 : immediate : 即時実行
	-- monthly_scheduled : 月次指定日指定
	settlement_type varchar(32) NOT NULL,
	-- 精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
	-- 該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される)
	scheduled_day smallint,
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- アカウント理由 : アカウントの状態が変更された際の理由を扱う。
CREATE TABLE dc_account_reason
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 操作種別
	operation_type varchar(32) NOT NULL,
	-- 理由コード
	reason_code varchar(16) NOT NULL,
	-- 理由詳細
	reason_detail varchar(200),
	-- 操作日時
	operated_at timestamp with time zone NOT NULL,
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- DC口座精算設定 : アカウントの支払い精算に関する設定情報を扱う
CREATE TABLE dc_account_settlement
(
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 精算条件 : immediate : 即時実行
	-- monthly_scheduled : 月次指定日指定
	settlement_type varchar(32) NOT NULL,
	-- 精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
	-- 該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される)
	scheduled_day smallint,
	PRIMARY KEY (dc_bank_number, service_id)
) WITHOUT OIDS;


-- DCユーザ : 個人 / 法人ユーザを扱う。
CREATE TABLE dc_user
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- ユーザ名
	user_name varchar(48) NOT NULL,
	-- DCユーザ種別 : individual : 個人ユーザ
	-- company_owner : 法人ユーザ (アカウント管理者)
	-- company : 法人ユーザ (アカウント管理者以外)
	dc_user_type varchar(16) NOT NULL,
	-- ユーザ状態 : initializing : 初期設定待ち
	-- active : アクティブ
	-- suspended : サインイン停止
	-- inactive : ユーザ無効
	-- 
	-- ※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。
	user_status varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- ロールID
	role_id varchar(36) NOT NULL,
	-- 登録日時
	registered_at timestamp with time zone NOT NULL,
	-- 無効日時
	terminated_at timestamp with time zone,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザアカウント : 個人 / 法人ユーザが紐づくDC口座番号を扱う。
CREATE TABLE dc_user_account
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザ属性 : DCユーザの汎用的な属性情報を扱う
CREATE TABLE dc_user_attribute
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 属性詳細
	attribute_detail jsonb,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザ利用端末 : 個人 / 法人ユーザのスマートフォン(iOS / Android)にPush通知を送信するために必要なデバイストークン(push_token)を管理する。
CREATE TABLE dc_user_device
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- プッシュ通知用トークン
	push_token varchar(1024) NOT NULL,
	-- OS種別 : ios : iOS
	-- android : Android
	os_type varchar(8) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザファイルタスク : 個人 / 法人ユーザのファイルタスクを扱う。
CREATE TABLE dc_user_file_task
(
	-- タスクID
	task_id varchar(36) NOT NULL UNIQUE,
	-- タスク種別 : nft_list : NFT一覧ファイル作成
	-- nft_mint : NFT発行
	-- nft_transfer : NFT移転
	task_type varchar(32) NOT NULL,
	-- タスク状態 : initialized : 初回処理待ち
	-- accept : 受付済み
	-- format_failure : フォーマット不備
	-- in_processing : 処理実行中
	-- completed : 処理完了
	-- failed : 処理失敗
	task_status varchar(16) NOT NULL,
	-- 申請日時
	ordered_at timestamp with time zone NOT NULL,
	-- サインインID
	sign_in_id varchar(16) NOT NULL,
	-- アップロードファイル名
	file_name varchar(128),
	-- タスク入力内容詳細
	task_order_detail jsonb NOT NULL,
	-- 処理結果保存先バケット
	result_bucket varchar(64),
	-- 処理結果ファイルパス
	result_file_path varchar(1024),
	-- 処理結果詳細
	task_result_detail jsonb,
	-- 処理完了日時
	completed_at timestamp with time zone,
	PRIMARY KEY (task_id)
) WITHOUT OIDS;


-- DCユーザファイルタスク詳細 : 個人 / 法人ユーザのファイルタスクの詳細を管理する。
CREATE TABLE dc_user_file_task_detail
(
	-- タスクID
	task_id varchar(36) NOT NULL,
	-- 行番号
	line_number int NOT NULL,
	-- タスク詳細状態
	task_detail_status varchar(16) NOT NULL,
	-- タスク入力内容詳細
	task_order_detail jsonb,
	-- 処理結果詳細
	task_result_detail jsonb,
	PRIMARY KEY (task_id, line_number)
) WITHOUT OIDS;


-- DCユーザ通知 : DCユーザの通知を管理するテーブル
CREATE TABLE dc_user_notification
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL,
	-- 通知ID
	notification_id varchar(36) NOT NULL,
	-- 通知種別 : order : 依頼通知
	-- order_completed : 依頼承認通知
	-- order_rejected : 依頼否認通知
	-- transfer : 移転完了通知
	-- account_updated : アカウント情報変更通知
	-- sigin_in : サインイン通知
	-- synchronous : ビジネスゾーンアカウント開設
	-- biz_terminating : ビジネスゾーンアカウント解約
	notification_type varchar(16) NOT NULL,
	-- メッセージ本文
	message text,
	-- 通知詳細 : 各通知タイプごとに固有の情報をjson形式で登録する
	notification_detail jsonb,
	-- 通知日時
	published_at timestamp with time zone NOT NULL,
	PRIMARY KEY (sign_in_id, notification_id)
) WITHOUT OIDS;


-- DCユーザOAuth認可コード : FinZone にて、Signer が BizZone 側に返却した認可コードを管理する
CREATE TABLE dc_user_oauth_code
(
	-- 認可コード
	oauth_code varchar(128) NOT NULL UNIQUE,
	-- サインインID
	sign_in_id varchar(16) NOT NULL,
	-- 有効期限
	expires_at timestamp with time zone NOT NULL,
	PRIMARY KEY (oauth_code)
) WITHOUT OIDS;


-- DCユーザOAuth状態 : 個人 / 法人ユーザの外部システムと OAuth で連携する際の state を管理する。
-- FinZone の場合 : BankGateway との認可で利用する
-- BizZone の場合 : FinZone Signer との認証で利用する
CREATE TABLE dc_user_oauth_state
(
	-- OAuth状態コード
	oauth_state varchar(128) NOT NULL UNIQUE,
	-- サインインID
	sign_in_id varchar(16) NOT NULL,
	-- 有効期限
	expires_at timestamp with time zone NOT NULL,
	PRIMARY KEY (oauth_state)
) WITHOUT OIDS;


-- DCユーザ申請 : 法人ユーザの申請情報を管理する。
CREATE TABLE dc_user_order
(
	-- 申請ID(自動裁判)
	order_id bigserial NOT NULL UNIQUE,
	-- 申請日時
	ordered_at timestamp with time zone NOT NULL,
	-- 申請種別 : FinZoneの場合
	-- - mint : 発行
	-- - burn : 償却
	-- - transfer : 送金
	-- - charge : チャージ
	-- - account_limit_updated : アカウント限度額変更
	-- - account_name_updated : アカウント名変更
	-- 
	-- BizZoneの場合
	-- - transfer：移転
	-- - discharge：ディスチャージ
	-- - account_name_updated : アカウント名変更
	-- - approve_transfer：移転許可
	-- - set_settlement : 精算条件設定
	order_type varchar(32) NOT NULL,
	-- 申請状態 : pending : 手続き待ち
	-- in_approving : 承認手続き中
	-- approval : 承認
	-- rejected : 否認
	order_status varchar(16) NOT NULL,
	-- 申請者サインインID
	order_sign_in_id varchar(16) NOT NULL,
	-- 申請内容詳細
	order_detail jsonb NOT NULL,
	-- 承認/否認者サインインID
	reviewer_sign_in_id varchar(16),
	-- 承認/否認日時
	reviewed_at timestamp with time zone,
	-- 理由コード
	reason_code varchar(16),
	-- 理由詳細
	reason_detail varchar(200),
	-- エラーコード
	error_code varchar(16),
	PRIMARY KEY (order_id)
) WITHOUT OIDS;


-- DCユーザ電話番号 : 個人ユーザのアプリ、もしくは法人ユーザが認証アプリで利用する電話番号を扱う。
-- サービスで認証アプリ利用がない場合、もしくは初期設定待ちの場合はレコードが存在しない。
CREATE TABLE dc_user_phone
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 電話番号
	phone_number varchar(11) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザ理由 : 個人 / 法人ユーザの状態が変更された際の理由を扱う。
CREATE TABLE dc_user_reason
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 操作種別
	operation_type varchar(32) NOT NULL,
	-- 理由コード
	reason_code varchar(16) NOT NULL,
	-- 理由詳細
	reason_detail varchar(200),
	-- 操作日時
	operated_at timestamp with time zone NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザロール : 個人 / 法人ユーザのロールを扱う。
CREATE TABLE dc_user_role
(
	-- ロールID
	role_id varchar(36) NOT NULL UNIQUE,
	-- ロール名
	role_name varchar(20) NOT NULL,
	-- ロール種別 : individual : 個人ユーザロール
	-- account_owner : アカウント管理者
	-- service_owner : サービス管理者
	-- user_owner : ユーザ管理者
	-- operator : 業務担当
	-- reviewer : 業務承認
	role_type varchar(16) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 所属DC口座番号
	belonged_dc_bank_number varchar(20),
	-- 作成日時
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	PRIMARY KEY (role_id)
) WITHOUT OIDS;


-- DCユーザロール権限 : 個人 / 法人ユーザのロールごとの操作権限を扱う。
CREATE TABLE dc_user_role_authority
(
	-- ロールID
	role_id varchar(36) NOT NULL,
	-- 権限名
	authority_key varchar(32) NOT NULL,
	PRIMARY KEY (role_id, authority_key)
) WITHOUT OIDS;


-- DCユーザサインイン失敗 : 個人/法人ユーザの直近のサインイン失敗回数を扱う。具体的には下記法則に従う。
-- 
-- - username/password によるサインインが失敗するごとに、失敗回数が1, 2, 3, ... と増加する。
-- - サインインが成功した場合は、本テーブルの該当レコードを削除する。
-- - 失敗回数が一定回数以上になった場合は、サインイン一時停止状態に移行するために、ロック解除日時にサインイン一時停止が自動解除される日時を保存する。
-- - サインインを試みた際に、ロック解除日時が未来日の場合はサインインできない。
--   - このとき、APIが返却するユーザ状態は別テーブルの user_status 列の値がユーザ無効の場合を除いて、サインイン一時停止 となる。
-- - ロック解除日時が NULL もしくは現在以前の日時の場合は、サインイン可能。
-- - ロック解除日時が現在以前の日時のときにサインインが失敗した場合は、失敗回数を1、ロック解除日時を NULL に更新する。
CREATE TABLE dc_user_sign_in_failure
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 失敗回数
	failure_count int NOT NULL,
	-- 最終失敗日時
	last_failed_at timestamp with time zone NOT NULL,
	-- ロック解除日時
	lock_expires_at timestamp with time zone,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザサインイン履歴 : 個人/法人ユーザの最新のサインイン日時と前回のサインイン日時を扱う。
-- ユーザがサインインすると、以下のように本テーブルのレコードを更新する。
-- - ユーザがサインインした日時が「最新のサインイン日時」に保存される
-- - 上記にあわせ、更新前の「最新のサインイン日時」の値が「前回のサインイン日時」に保存される
-- 
-- 上記により、ユーザ自身が前回のサインイン日時を照会する場合は「前回のサインイン日時」列を参照し、ユーザ管理者がユーザ一一覧にて直近のサインイン日時を紹介する場合は「最新のサインイン日時」列を参照すればよい。
CREATE TABLE dc_user_sign_in_history
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 最新のサインイン日時
	current_signed_in_at timestamp with time zone NOT NULL,
	-- 前回のサインイン日時
	last_signed_in_at timestamp with time zone,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- DCユーザTOTP : 個人 / 法人ユーザのTOTP鍵情報を扱う。
-- 認証アプリを利用しない場合、もしくは初期設定が完了していない場合はレコードが存在しない。
CREATE TABLE dc_user_totp
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- TOTP鍵
	totp_secret varchar(1024) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- インフォメーション : インフォメーションを扱う。
CREATE TABLE information
(
	-- インフォメーションID : 
	-- 
	information_id varchar(36) NOT NULL UNIQUE,
	-- インフォメーションタイトル
	information_title varchar(50) NOT NULL,
	-- インフォメーション詳細
	information_detail text NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 公開開始日時
	from_published_at timestamp with time zone NOT NULL,
	-- 公開終了日時
	to_published_at timestamp with time zone NOT NULL,
	-- 作成日時
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	PRIMARY KEY (information_id)
) WITHOUT OIDS;


-- メッセージテンプレート : 理由コードのマスタ。
CREATE TABLE message_template
(
	-- テンプレートキー : 
	-- 
	template_key varchar(64) NOT NULL UNIQUE,
	-- テンプレート本文
	template_content text NOT NULL,
	PRIMARY KEY (template_key)
) WITHOUT OIDS;


-- 発行履歴 : DCJPY 発行 / 償却の処理の状態を管理する。
CREATE TABLE mint_transaction
(
	-- リクエストID
	request_id varchar(128) NOT NULL,
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	-- mint取引状態 : initialize : 処理開始前
	-- withdrawn : 銀行預金口座から出金済み
	-- withdraw_failed : 銀行預金口座から出金できなかった
	-- minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗
	-- completed : DCJPY 発行/償却手続きが正常に完了
	mint_status varchar(16) NOT NULL,
	-- 金額
	amount bigint NOT NULL,
	-- 初期登録日時
	initialized_at timestamp with time zone NOT NULL,
	-- BankGW処理結果日時
	bankgw_resulted_at timestamp with time zone,
	-- CoreAPI処理結果日時
	coreapi_resulted_at timestamp with time zone,
	PRIMARY KEY (request_id, dc_bank_number)
) WITHOUT OIDS;


-- QR操作 : QRコード読み込みに紐づく対象業務を扱う。対象は以下の３種類。
--  - サインインを行う (qr_flow_type = 'sign_in')
--  - 自身の操作を確定する (qr_flow_type = 'confirmation')
--  - 申請された依頼を承認する際に確定する (qr_flow_type = 'order')
-- 
-- qr_flow_type = 'confirmation' の場合は、operation_detail 列より該当内容が保存されており、qr_flow_type = 'order' の場合は order_id 列に該当する申請IDが保存されている。
-- 個人ユーザ/法人ユーザ に関する操作か、銀行/事業者に関する操作かは owner_type 列で判断する。
CREATE TABLE qr_operation
(
	-- QRキー
	qr_key varchar(64) NOT NULL UNIQUE,
	-- QR手続きフロー種別 : sign_in : サインイン
	-- confirmation : 確認フロー
	-- order : 申請フロー
	qr_flow_type varchar(16) NOT NULL,
	-- 利用元種別 : user : 個人ユーザ/法人ユーザ
	-- service : 銀行/事業者
	owner_type varchar(8) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- サインインID
	sign_in_id varchar(16),
	-- QR処理状態 : qr_flow_type = 'sign_in' の場合
	-- - pending : 認証アプリ認証前
	-- - in_processing : 処理中
	-- - authenticated : 認証済み
	-- - completed : トークン発行済
	-- - failure : 処理失敗
	-- 
	-- qr_flow_type が上記以外
	-- - pending : 手続き待ち
	-- - in_processing : 処理中
	-- - completed : 処理済み
	-- - failure : 処理失敗
	qr_status varchar(16) NOT NULL,
	-- 有効期限
	expires_at timestamp with time zone NOT NULL,
	-- サインイン時の呼び出し元識別情報 : QR読み込みによるサインインにて、認証アプリによる手続きが完了後、フロントにトークン情報を返却してよいか判断するために利用する。
	state_code varchar(128),
	-- 申請ID
	order_id bigint,
	-- 操作内容詳細
	operation_detail jsonb,
	-- エラーコード
	error_code varchar(16),
	PRIMARY KEY (qr_key)
) WITHOUT OIDS;


-- 理由コード : 理由コードのマスタ。
CREATE TABLE reason_code
(
	-- 理由コード
	reason_code varchar(16) NOT NULL,
	-- 利用元種別 : user : 個人ユーザ/法人ユーザ
	-- service : 銀行/事業者
	owner_type varchar(8) NOT NULL,
	-- 操作種別
	operation_type varchar(32) NOT NULL,
	-- 理由タイトル
	reason_title varchar(32) NOT NULL,
	PRIMARY KEY (reason_code)
) WITHOUT OIDS;


-- サービスアドミン
CREATE TABLE service_admin
(
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL UNIQUE,
	-- アドミンID
	admin_id varchar(36) NOT NULL,
	PRIMARY KEY (service_id)
) WITHOUT OIDS;


-- サービスデバイス : 認証アプリのPush通知用トークンの登録先を管理する
CREATE TABLE service_device
(
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- OS種別 : ios : iOS
	-- android : Android
	os_type varchar(8) NOT NULL,
	-- プラットフォームARN : 認証アプリプッシュ通知トークン用の登録先
	platform_arn varchar(1024) NOT NULL,
	PRIMARY KEY (service_id, os_type)
) WITHOUT OIDS;


-- サービスオーナ
CREATE TABLE service_owner
(
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL UNIQUE,
	-- サービス名
	service_name varchar(32),
	-- ソーンID
	zone_id smallint NOT NULL,
	-- バリデータID
	validator_id varchar(36) NOT NULL,
	-- イシュアID
	issuer_id varchar(36),
	PRIMARY KEY (service_id)
) WITHOUT OIDS;


-- サービスユーザ : 銀行 / 事業者ユーザを扱う。
CREATE TABLE service_user
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- ユーザ名
	user_name varchar(48) NOT NULL,
	-- サービスユーザ種別 : owner : 管理者
	-- normal : 通常ユーザ
	service_user_type varchar(8) NOT NULL,
	-- ユーザ状態 : initializing : 初期設定待ち
	-- active : アクティブ
	-- suspended : サインイン停止
	-- inactive : ユーザ無効
	-- 
	-- ※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。
	user_status varchar(20) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- ロールID
	role_id varchar(36) NOT NULL,
	-- 登録日時
	registered_at timestamp with time zone NOT NULL,
	-- 無効日時
	terminated_at timestamp with time zone,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- サービスユーザ利用端末 : 銀行 / 事業者ユーザのスマートフォン(iOS / Android)にPush通知を送信するために必要なデバイストークン(push_token)を管理する。
CREATE TABLE service_user_device
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- プッシュ通知用トークン
	push_token varchar(1024) NOT NULL,
	-- OS種別 : ios : iOS
	-- android : Android
	os_type varchar(8) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- サービスユーザファイルタスク : 銀行 / 事業者ユーザのファイルタスクを扱う。
CREATE TABLE service_user_file_task
(
	-- タスクID
	task_id varchar(36) NOT NULL UNIQUE,
	-- タスク種別 : nft_list : NFT一覧ファイル作成
	-- nft_mint : NFT発行
	-- nft_transfer : NFT移転
	task_type varchar(32) NOT NULL,
	-- タスク状態 : initialized : 初回処理待ち
	-- accept : 受付済み
	-- format_failure : フォーマット不備
	-- in_processing : 処理実行中
	-- completed : 処理完了
	-- failed : 処理失敗
	task_status varchar(16) NOT NULL,
	-- 申請日時
	ordered_at timestamp with time zone NOT NULL,
	-- サインインID
	sign_in_id varchar(16) NOT NULL,
	-- アップロードファイル名
	file_name varchar(128),
	-- タスク入力内容詳細
	task_order_detail jsonb NOT NULL,
	-- 処理結果保存先バケット
	result_bucket varchar(64),
	-- 処理結果ファイルパス
	result_file_path varchar(1024),
	-- 処理結果詳細
	task_result_detail jsonb,
	-- 処理完了日時
	completed_at timestamp with time zone,
	PRIMARY KEY (task_id)
) WITHOUT OIDS;


-- サービスユーザファイルタスク詳細 : 銀行 / 事業者ユーザのファイルタスクの詳細を管理する。
CREATE TABLE service_user_file_task_detail
(
	-- タスクID
	task_id varchar(36) NOT NULL,
	-- 行番号
	line_number int NOT NULL,
	-- タスク詳細状態
	task_detail_status varchar(16) NOT NULL,
	-- タスク入力内容詳細
	task_order_detail jsonb,
	-- 処理結果詳細
	task_result_detail jsonb,
	PRIMARY KEY (task_id, line_number)
) WITHOUT OIDS;


-- サービスユーザ申請 : 銀行 / 事業者ユーザの申請情報を管理する。
CREATE TABLE service_user_order
(
	-- 申請ID(自動裁判)
	order_id bigserial NOT NULL UNIQUE,
	-- 申請日時
	ordered_at timestamp with time zone NOT NULL,
	-- 申請種別 : account_updated : アカウント情報変更
	-- 
	-- user_suspended : サインイン停止
	-- user_activated : サインイン停止解除（アクティブ）
	-- user_updated : ユーザ情報変更
	-- user_reset : ユーザ認証リセット
	-- 
	-- account_frozen : アカウント凍結
	-- account_activated : アカウント凍結解除（アクティブ）
	-- account_force_burned : 強制償却
	-- account_force_terminated : 強制解約
	-- 
	-- info_updated : インフォメーション編集
	service_order_type varchar(32) NOT NULL,
	-- 申請状態 : pending : 手続き待ち
	-- in_approving : 承認手続き中
	-- approval : 承認
	-- rejected : 否認
	order_status varchar(16) NOT NULL,
	-- DC口座番号
	dc_bank_number varchar(20),
	-- 申請者サインインID
	order_sign_in_id varchar(16) NOT NULL,
	-- 申請内容詳細
	order_detail jsonb NOT NULL,
	-- 承認/否認者サインインID
	reviewer_sign_in_id varchar(16),
	-- 承認/否認日時
	reviewed_at timestamp with time zone,
	-- 理由コード
	reason_code varchar(16),
	-- 理由詳細
	reason_detail varchar(200),
	-- エラーコード
	error_code varchar(16),
	PRIMARY KEY (order_id)
) WITHOUT OIDS;


-- サービスユーザ電話番号 : 銀行 / 事業者ユーザが認証アプリで利用する電話番号を扱う。
-- サービスで認証アプリ利用がない場合、もしくは初期設定待ちの場合はレコードが存在しない。
CREATE TABLE service_user_phone
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 電話番号
	phone_number varchar(11) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- サービスユーザ理由 : 銀行 / 事業者ユーザの状態が変更された際の理由を扱う。
CREATE TABLE service_user_reason
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 操作種別
	operation_type varchar(32) NOT NULL,
	-- 理由コード
	reason_code varchar(16) NOT NULL,
	-- 理由詳細
	reason_detail varchar(200),
	-- 操作日時
	operated_at timestamp with time zone NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- サービスユーザロール : 銀行 / 事業者ユーザのロールを扱う。
CREATE TABLE service_user_role
(
	-- ロールID
	role_id varchar(36) NOT NULL UNIQUE,
	-- ロール名
	role_name varchar(20) NOT NULL,
	-- ロール種別 : individual : 個人ユーザロール
	-- account_owner : アカウント管理者
	-- service_owner : サービス管理者
	-- user_owner : ユーザ管理者
	-- operator : 業務担当
	-- reviewer : 業務承認
	role_type varchar(16) NOT NULL,
	-- サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
	-- 通常の値は '0' とする
	service_id varchar(8) NOT NULL,
	-- 作成日時
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	PRIMARY KEY (role_id)
) WITHOUT OIDS;


-- サービスユーザロール権限 : 銀行 / 事業者ユーザのロールごとの操作権限を扱う。
CREATE TABLE service_user_role_authority
(
	-- ロールID
	role_id varchar(36) NOT NULL,
	-- 権限名
	authority_key varchar(32) NOT NULL,
	PRIMARY KEY (role_id, authority_key)
) WITHOUT OIDS;


-- サービスユーザサインイン失敗 : 銀行/事業者ユーザの直近のサインイン失敗回数を扱う。具体的には下記法則に従う。
-- 
-- - username/password によるサインインが失敗するごとに、失敗回数が1, 2, 3, ... と増加する。
-- - サインインが成功した場合は、本テーブルの該当レコードを削除する。
-- - 失敗回数が一定回数以上になった場合は、サインイン一時停止状態に移行するために、ロック解除日時にサインイン一時停止が自動解除される日時を保存する。
-- - サインインを試みた際に、ロック解除日時が未来日の場合はサインインできない。
--   - このとき、APIが返却するユーザ状態は別テーブルの user_status 列の値がユーザ無効の場合を除いて、サインイン一時停止 となる。
-- - ロック解除日時が NULL もしくは現在以前の日時の場合は、サインイン可能。
-- - ロック解除日時が現在以前の日時のときにサインインが失敗した場合は、失敗回数を1、ロック解除日時を NULL に更新する。
CREATE TABLE service_user_sign_in_failure
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 失敗回数
	failure_count int NOT NULL,
	-- 最終失敗日時
	last_failed_at timestamp with time zone NOT NULL,
	-- ロック解除日時
	lock_expires_at timestamp with time zone,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- サービスユーザサインイン履歴 : 銀行/事業者ユーザの最新のサインイン日時と前回のサインイン日時を扱う。
-- ユーザがサインインすると、以下のように本テーブルのレコードを更新する。
-- - ユーザがサインインした日時が「最新のサインイン日時」に保存される
-- - 上記にあわせ、更新前の「最新のサインイン日時」の値が「前回のサインイン日時」に保存される
-- 
-- 上記により、ユーザ自身が前回のサインイン日時を照会する場合は「前回のサインイン日時」列を参照し、ユーザ管理者がユーザ一一覧にて直近のサインイン日時を紹介する場合は「最新のサインイン日時」列を参照すればよい。
CREATE TABLE service_user_sign_in_history
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- 最新のサインイン日時
	current_signed_in_at timestamp with time zone NOT NULL,
	-- 前回のサインイン日時
	last_signed_in_at timestamp with time zone,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- サービスユーザTOTP : 銀行 / 事業者ユーザのTOTP鍵情報を扱う。
-- 認証アプリを利用しない場合、もしくは初期設定が完了していない場合はレコードが存在しない。
CREATE TABLE service_user_totp
(
	-- サインインID
	sign_in_id varchar(16) NOT NULL UNIQUE,
	-- TOTP鍵
	totp_secret varchar(1024) NOT NULL,
	PRIMARY KEY (sign_in_id)
) WITHOUT OIDS;


-- バリデータアカウント
CREATE TABLE validator_account
(
	-- バリデータID
	validator_id varchar(36) NOT NULL UNIQUE,
	-- DC口座番号
	dc_bank_number varchar(20) NOT NULL,
	PRIMARY KEY (validator_id)
) WITHOUT OIDS;



/* Create Foreign Keys */

ALTER TABLE bank_account
	ADD FOREIGN KEY (dc_bank_number, service_id)
	REFERENCES dc_account (dc_bank_number, service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_account_email
	ADD FOREIGN KEY (dc_bank_number, service_id)
	REFERENCES dc_account (dc_bank_number, service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_account_reason
	ADD FOREIGN KEY (dc_bank_number, service_id)
	REFERENCES dc_account (dc_bank_number, service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_account_settlement
	ADD FOREIGN KEY (dc_bank_number, service_id)
	REFERENCES dc_account (dc_bank_number, service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_account
	ADD FOREIGN KEY (dc_bank_number, service_id)
	REFERENCES dc_account (dc_bank_number, service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_account_future_settlement
	ADD FOREIGN KEY (dc_bank_number, service_id)
	REFERENCES dc_account_settlement (dc_bank_number, service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_account
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE NO ACTION
	ON DELETE CASCADE
;


ALTER TABLE dc_user_attribute
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_device
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_file_task
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_notification
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_order
	ADD FOREIGN KEY (reviewer_sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_order
	ADD FOREIGN KEY (order_sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_phone
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_reason
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_sign_in_failure
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_sign_in_history
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_totp
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES dc_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_file_task_detail
	ADD FOREIGN KEY (task_id)
	REFERENCES dc_user_file_task (task_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user
	ADD FOREIGN KEY (role_id)
	REFERENCES dc_user_role (role_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user_role_authority
	ADD FOREIGN KEY (role_id)
	REFERENCES dc_user_role (role_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_account
	ADD FOREIGN KEY (service_id)
	REFERENCES service_owner (service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE dc_user
	ADD FOREIGN KEY (service_id)
	REFERENCES service_owner (service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user
	ADD FOREIGN KEY (service_id)
	REFERENCES service_owner (service_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_device
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_file_task
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE NO ACTION
	ON DELETE NO ACTION
;


ALTER TABLE service_user_order
	ADD FOREIGN KEY (order_sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_order
	ADD FOREIGN KEY (reviewer_sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_phone
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_reason
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_sign_in_failure
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_sign_in_history
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_totp
	ADD FOREIGN KEY (sign_in_id)
	REFERENCES service_user (sign_in_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_file_task_detail
	ADD FOREIGN KEY (task_id)
	REFERENCES service_user_file_task (task_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user
	ADD FOREIGN KEY (role_id)
	REFERENCES service_user_role (role_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;


ALTER TABLE service_user_role_authority
	ADD FOREIGN KEY (role_id)
	REFERENCES service_user_role (role_id)
	ON UPDATE CASCADE
	ON DELETE CASCADE
;



/* Create Indexes */

CREATE INDEX index_app_version__app_version ON app_version (app_version);
CREATE INDEX index_bank_account__bank_account_number ON bank_account (bank_account_number);
CREATE INDEX index_account__account_id ON dc_account (account_id);
CREATE INDEX dc_account_bank_linkage__expires_at ON dc_account_bank_linkage (expires_at);
CREATE INDEX index_dc_user__user_name ON dc_user (user_name);
CREATE INDEX index_dc_user_account__dc_bank_number ON dc_user_account (dc_bank_number);
CREATE INDEX index_dc_user_file_task__sign_in_id ON dc_user_file_task (sign_in_id);
CREATE INDEX index_dc_user_order__ordered_at ON dc_user_order (ordered_at);
CREATE INDEX index_dc_user_order__reviewed_at ON dc_user_order (reviewed_at);
CREATE INDEX index_dc_user_order__order_sigin_in_id ON dc_user_order (order_sign_in_id);
CREATE INDEX index_dc_user_order__reviewer_sign_in_id ON dc_user_order (reviewer_sign_in_id);
CREATE INDEX index_dc_user_phone__phone_number ON dc_user_phone (phone_number);
CREATE INDEX index_reason_code__operation_type_owner_type ON reason_code (operation_type, owner_type);
CREATE INDEX index_service_user__user_name ON service_user (user_name);
CREATE INDEX index_service_user_file_task__sign_in_id ON service_user_file_task (sign_in_id);
CREATE INDEX index_service_user_order__ordered_at ON service_user_order (ordered_at);
CREATE INDEX index_service_user_order__reviewed_at ON service_user_order (reviewed_at);
CREATE INDEX index_service_user_order__order_sign_in_id ON service_user_order (order_sign_in_id, ordered_at);
CREATE INDEX index_service_user_order__review_sign_in_id ON service_user_order (reviewer_sign_in_id, ordered_at);
CREATE INDEX index_service_user_order__dc_bank_number ON service_user_order (dc_bank_number);
CREATE INDEX index_service_user_phone__phone_number ON service_user_phone (phone_number);



/* Comments */

COMMENT ON TABLE app_version IS 'アプリケーションバージョン : 認証アプリのバージョンを扱う';
COMMENT ON COLUMN app_version.version_id IS 'バージョンID';
COMMENT ON COLUMN app_version.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN app_version.os_type IS 'OS種別 : ios : iOS
android : Android';
COMMENT ON COLUMN app_version.app_version IS 'アプリケーションバージョン';
COMMENT ON COLUMN app_version.available IS '利用可否';
COMMENT ON COLUMN app_version.created_at IS '作成日時';
COMMENT ON TABLE auth_client IS '認証クライアント : CoreAPI の接続情報を扱う';
COMMENT ON COLUMN auth_client.entity_id IS 'エンティティID';
COMMENT ON COLUMN auth_client.entity_type IS 'エンティティ種別 : admin : アドミニストレータ
provider : プロバイダ
validator : バリデータ
issuer : イシュア';
COMMENT ON COLUMN auth_client.client_id IS 'クライアントID';
COMMENT ON TABLE bank_account IS '銀行預金口座 : DCアカウントに紐づく銀行預金口座情報を扱う。';
COMMENT ON COLUMN bank_account.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN bank_account.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN bank_account.bank_account_id IS '銀行預金口座ID';
COMMENT ON COLUMN bank_account.bank_code IS '銀行コード';
COMMENT ON COLUMN bank_account.bank_name IS '銀行名';
COMMENT ON COLUMN bank_account.branch_code IS '支店コード';
COMMENT ON COLUMN bank_account.bank_account_type IS '銀行預金口座種別 : saving : 普通口座
checking : 貯蓄口座';
COMMENT ON COLUMN bank_account.bank_account_number IS '銀行預金口座番号';
COMMENT ON COLUMN bank_account.bank_account_name IS '銀行預金口座名義';
COMMENT ON TABLE burn_transaction IS '償却履歴 : DCJPY 発行 / 償却の処理の状態を管理する。';
COMMENT ON COLUMN burn_transaction.request_id IS 'リクエストID';
COMMENT ON COLUMN burn_transaction.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN burn_transaction.burn_status IS 'burn取引状態 : initialize : 処理開始前
burned : DC口座からDCJPY償却済み
burn_failed : DC口座からDCJPY償却ができなかった
deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗
completed : DCJPY発行/償却手続きが正常に完了';
COMMENT ON COLUMN burn_transaction.amount IS '金額';
COMMENT ON COLUMN burn_transaction.initialized_at IS '初期登録日時';
COMMENT ON COLUMN burn_transaction.coreapi_resulted_at IS 'CoreAPI処理結果日時';
COMMENT ON COLUMN burn_transaction.bankgw_resulted_at IS 'BankGW処理結果日時';
COMMENT ON TABLE dc_account IS 'DC口座 : DC口座を扱う。';
COMMENT ON COLUMN dc_account.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_account.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_account.account_id IS 'アカウントID';
COMMENT ON COLUMN dc_account.core_linked IS 'Coreモジュール接続状態 : Coreモジュールにアカウントを作成済みかを示す';
COMMENT ON TABLE dc_account_bank_linkage IS 'DC口座IB接続 : DC口座を扱う。';
COMMENT ON COLUMN dc_account_bank_linkage.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_account_bank_linkage.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_account_bank_linkage.bank_link_status IS 'IB接続状態 : authenticated : 認証済み
need_auth : 認証が必要';
COMMENT ON COLUMN dc_account_bank_linkage.expires_at IS '有効期限';
COMMENT ON TABLE dc_account_email IS 'DC口座メールアドレス : アカウントに紐づく、取引完了時にメール送信する宛先を扱う';
COMMENT ON COLUMN dc_account_email.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_account_email.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_account_email.email_address IS '取引完了通知先メールアドレス';
COMMENT ON TABLE dc_account_future_settlement IS 'DC口座精算予約設定 : アカウントの支払い精算に関する予約設定を扱う。
月次設定を月中に変更手続きを行い、指定された日付で実設定に反映するため';
COMMENT ON COLUMN dc_account_future_settlement.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_account_future_settlement.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_account_future_settlement.apply_at IS '適用日時';
COMMENT ON COLUMN dc_account_future_settlement.settlement_type IS '精算条件 : immediate : 即時実行
monthly_scheduled : 月次指定日指定';
COMMENT ON COLUMN dc_account_future_settlement.scheduled_day IS '精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される)';
COMMENT ON TABLE dc_account_reason IS 'アカウント理由 : アカウントの状態が変更された際の理由を扱う。';
COMMENT ON COLUMN dc_account_reason.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_account_reason.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_account_reason.operation_type IS '操作種別';
COMMENT ON COLUMN dc_account_reason.reason_code IS '理由コード';
COMMENT ON COLUMN dc_account_reason.reason_detail IS '理由詳細';
COMMENT ON COLUMN dc_account_reason.operated_at IS '操作日時';
COMMENT ON TABLE dc_account_settlement IS 'DC口座精算設定 : アカウントの支払い精算に関する設定情報を扱う';
COMMENT ON COLUMN dc_account_settlement.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_account_settlement.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_account_settlement.settlement_type IS '精算条件 : immediate : 即時実行
monthly_scheduled : 月次指定日指定';
COMMENT ON COLUMN dc_account_settlement.scheduled_day IS '精算指定日付 : 精算条件が指定日実行の場合に、この日付で精算される。
該当月に指定された日付が存在しない場合、該当月の末日に精算処理が行われる (例えば、31日を指定している場合、4月は30日に精算される)';
COMMENT ON TABLE dc_user IS 'DCユーザ : 個人 / 法人ユーザを扱う。';
COMMENT ON COLUMN dc_user.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user.user_name IS 'ユーザ名';
COMMENT ON COLUMN dc_user.dc_user_type IS 'DCユーザ種別 : individual : 個人ユーザ
company_owner : 法人ユーザ (アカウント管理者)
company : 法人ユーザ (アカウント管理者以外)';
COMMENT ON COLUMN dc_user.user_status IS 'ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。';
COMMENT ON COLUMN dc_user.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_user.role_id IS 'ロールID';
COMMENT ON COLUMN dc_user.registered_at IS '登録日時';
COMMENT ON COLUMN dc_user.terminated_at IS '無効日時';
COMMENT ON TABLE dc_user_account IS 'DCユーザアカウント : 個人 / 法人ユーザが紐づくDC口座番号を扱う。';
COMMENT ON COLUMN dc_user_account.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_account.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN dc_user_account.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON TABLE dc_user_attribute IS 'DCユーザ属性 : DCユーザの汎用的な属性情報を扱う';
COMMENT ON COLUMN dc_user_attribute.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_attribute.attribute_detail IS '属性詳細';
COMMENT ON TABLE dc_user_device IS 'DCユーザ利用端末 : 個人 / 法人ユーザのスマートフォン(iOS / Android)にPush通知を送信するために必要なデバイストークン(push_token)を管理する。';
COMMENT ON COLUMN dc_user_device.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_device.push_token IS 'プッシュ通知用トークン';
COMMENT ON COLUMN dc_user_device.os_type IS 'OS種別 : ios : iOS
android : Android';
COMMENT ON TABLE dc_user_file_task IS 'DCユーザファイルタスク : 個人 / 法人ユーザのファイルタスクを扱う。';
COMMENT ON COLUMN dc_user_file_task.task_id IS 'タスクID';
COMMENT ON COLUMN dc_user_file_task.task_type IS 'タスク種別 : nft_list : NFT一覧ファイル作成
nft_mint : NFT発行
nft_transfer : NFT移転';
COMMENT ON COLUMN dc_user_file_task.task_status IS 'タスク状態 : initialized : 初回処理待ち
accept : 受付済み
format_failure : フォーマット不備
in_processing : 処理実行中
completed : 処理完了
failed : 処理失敗';
COMMENT ON COLUMN dc_user_file_task.ordered_at IS '申請日時';
COMMENT ON COLUMN dc_user_file_task.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_file_task.file_name IS 'アップロードファイル名';
COMMENT ON COLUMN dc_user_file_task.task_order_detail IS 'タスク入力内容詳細';
COMMENT ON COLUMN dc_user_file_task.result_bucket IS '処理結果保存先バケット';
COMMENT ON COLUMN dc_user_file_task.result_file_path IS '処理結果ファイルパス';
COMMENT ON COLUMN dc_user_file_task.task_result_detail IS '処理結果詳細';
COMMENT ON COLUMN dc_user_file_task.completed_at IS '処理完了日時';
COMMENT ON TABLE dc_user_file_task_detail IS 'DCユーザファイルタスク詳細 : 個人 / 法人ユーザのファイルタスクの詳細を管理する。';
COMMENT ON COLUMN dc_user_file_task_detail.task_id IS 'タスクID';
COMMENT ON COLUMN dc_user_file_task_detail.line_number IS '行番号';
COMMENT ON COLUMN dc_user_file_task_detail.task_detail_status IS 'タスク詳細状態';
COMMENT ON COLUMN dc_user_file_task_detail.task_order_detail IS 'タスク入力内容詳細';
COMMENT ON COLUMN dc_user_file_task_detail.task_result_detail IS '処理結果詳細';
COMMENT ON TABLE dc_user_notification IS 'DCユーザ通知 : DCユーザの通知を管理するテーブル';
COMMENT ON COLUMN dc_user_notification.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_notification.notification_id IS '通知ID';
COMMENT ON COLUMN dc_user_notification.notification_type IS '通知種別 : order : 依頼通知
order_completed : 依頼承認通知
order_rejected : 依頼否認通知
transfer : 移転完了通知
account_updated : アカウント情報変更通知
sigin_in : サインイン通知
synchronous : ビジネスゾーンアカウント開設
biz_terminating : ビジネスゾーンアカウント解約';
COMMENT ON COLUMN dc_user_notification.message IS 'メッセージ本文';
COMMENT ON COLUMN dc_user_notification.notification_detail IS '通知詳細 : 各通知タイプごとに固有の情報をjson形式で登録する';
COMMENT ON COLUMN dc_user_notification.published_at IS '通知日時';
COMMENT ON TABLE dc_user_oauth_code IS 'DCユーザOAuth認可コード : FinZone にて、Signer が BizZone 側に返却した認可コードを管理する';
COMMENT ON COLUMN dc_user_oauth_code.oauth_code IS '認可コード';
COMMENT ON COLUMN dc_user_oauth_code.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_oauth_code.expires_at IS '有効期限';
COMMENT ON TABLE dc_user_oauth_state IS 'DCユーザOAuth状態 : 個人 / 法人ユーザの外部システムと OAuth で連携する際の state を管理する。
FinZone の場合 : BankGateway との認可で利用する
BizZone の場合 : FinZone Signer との認証で利用する';
COMMENT ON COLUMN dc_user_oauth_state.oauth_state IS 'OAuth状態コード';
COMMENT ON COLUMN dc_user_oauth_state.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_oauth_state.expires_at IS '有効期限';
COMMENT ON TABLE dc_user_order IS 'DCユーザ申請 : 法人ユーザの申請情報を管理する。';
COMMENT ON COLUMN dc_user_order.order_id IS '申請ID(自動裁判)';
COMMENT ON COLUMN dc_user_order.ordered_at IS '申請日時';
COMMENT ON COLUMN dc_user_order.order_type IS '申請種別 : FinZoneの場合
- mint : 発行
- burn : 償却
- transfer : 送金
- charge : チャージ
- account_limit_updated : アカウント限度額変更
- account_name_updated : アカウント名変更

BizZoneの場合
- transfer：移転
- discharge：ディスチャージ
- account_name_updated : アカウント名変更
- approve_transfer：移転許可
- set_settlement : 精算条件設定';
COMMENT ON COLUMN dc_user_order.order_status IS '申請状態 : pending : 手続き待ち
in_approving : 承認手続き中
approval : 承認
rejected : 否認';
COMMENT ON COLUMN dc_user_order.order_sign_in_id IS '申請者サインインID';
COMMENT ON COLUMN dc_user_order.order_detail IS '申請内容詳細';
COMMENT ON COLUMN dc_user_order.reviewer_sign_in_id IS '承認/否認者サインインID';
COMMENT ON COLUMN dc_user_order.reviewed_at IS '承認/否認日時';
COMMENT ON COLUMN dc_user_order.reason_code IS '理由コード';
COMMENT ON COLUMN dc_user_order.reason_detail IS '理由詳細';
COMMENT ON COLUMN dc_user_order.error_code IS 'エラーコード';
COMMENT ON TABLE dc_user_phone IS 'DCユーザ電話番号 : 個人ユーザのアプリ、もしくは法人ユーザが認証アプリで利用する電話番号を扱う。
サービスで認証アプリ利用がない場合、もしくは初期設定待ちの場合はレコードが存在しない。';
COMMENT ON COLUMN dc_user_phone.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_phone.phone_number IS '電話番号';
COMMENT ON TABLE dc_user_reason IS 'DCユーザ理由 : 個人 / 法人ユーザの状態が変更された際の理由を扱う。';
COMMENT ON COLUMN dc_user_reason.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_reason.operation_type IS '操作種別';
COMMENT ON COLUMN dc_user_reason.reason_code IS '理由コード';
COMMENT ON COLUMN dc_user_reason.reason_detail IS '理由詳細';
COMMENT ON COLUMN dc_user_reason.operated_at IS '操作日時';
COMMENT ON TABLE dc_user_role IS 'DCユーザロール : 個人 / 法人ユーザのロールを扱う。';
COMMENT ON COLUMN dc_user_role.role_id IS 'ロールID';
COMMENT ON COLUMN dc_user_role.role_name IS 'ロール名';
COMMENT ON COLUMN dc_user_role.role_type IS 'ロール種別 : individual : 個人ユーザロール
account_owner : アカウント管理者
service_owner : サービス管理者
user_owner : ユーザ管理者
operator : 業務担当
reviewer : 業務承認';
COMMENT ON COLUMN dc_user_role.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN dc_user_role.belonged_dc_bank_number IS '所属DC口座番号';
COMMENT ON COLUMN dc_user_role.created_at IS '作成日時';
COMMENT ON TABLE dc_user_role_authority IS 'DCユーザロール権限 : 個人 / 法人ユーザのロールごとの操作権限を扱う。';
COMMENT ON COLUMN dc_user_role_authority.role_id IS 'ロールID';
COMMENT ON COLUMN dc_user_role_authority.authority_key IS '権限名';
COMMENT ON TABLE dc_user_sign_in_failure IS 'DCユーザサインイン失敗 : 個人/法人ユーザの直近のサインイン失敗回数を扱う。具体的には下記法則に従う。

- username/password によるサインインが失敗するごとに、失敗回数が1, 2, 3, ... と増加する。
- サインインが成功した場合は、本テーブルの該当レコードを削除する。
- 失敗回数が一定回数以上になった場合は、サインイン一時停止状態に移行するために、ロック解除日時にサインイン一時停止が自動解除される日時を保存する。
- サインインを試みた際に、ロック解除日時が未来日の場合はサインインできない。
  - このとき、APIが返却するユーザ状態は別テーブルの user_status 列の値がユーザ無効の場合を除いて、サインイン一時停止 となる。
- ロック解除日時が NULL もしくは現在以前の日時の場合は、サインイン可能。
- ロック解除日時が現在以前の日時のときにサインインが失敗した場合は、失敗回数を1、ロック解除日時を NULL に更新する。';
COMMENT ON COLUMN dc_user_sign_in_failure.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_sign_in_failure.failure_count IS '失敗回数';
COMMENT ON COLUMN dc_user_sign_in_failure.last_failed_at IS '最終失敗日時';
COMMENT ON COLUMN dc_user_sign_in_failure.lock_expires_at IS 'ロック解除日時';
COMMENT ON TABLE dc_user_sign_in_history IS 'DCユーザサインイン履歴 : 個人/法人ユーザの最新のサインイン日時と前回のサインイン日時を扱う。
ユーザがサインインすると、以下のように本テーブルのレコードを更新する。
- ユーザがサインインした日時が「最新のサインイン日時」に保存される
- 上記にあわせ、更新前の「最新のサインイン日時」の値が「前回のサインイン日時」に保存される

上記により、ユーザ自身が前回のサインイン日時を照会する場合は「前回のサインイン日時」列を参照し、ユーザ管理者がユーザ一一覧にて直近のサインイン日時を紹介する場合は「最新のサインイン日時」列を参照すればよい。';
COMMENT ON COLUMN dc_user_sign_in_history.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_sign_in_history.current_signed_in_at IS '最新のサインイン日時';
COMMENT ON COLUMN dc_user_sign_in_history.last_signed_in_at IS '前回のサインイン日時';
COMMENT ON TABLE dc_user_totp IS 'DCユーザTOTP : 個人 / 法人ユーザのTOTP鍵情報を扱う。
認証アプリを利用しない場合、もしくは初期設定が完了していない場合はレコードが存在しない。';
COMMENT ON COLUMN dc_user_totp.sign_in_id IS 'サインインID';
COMMENT ON COLUMN dc_user_totp.totp_secret IS 'TOTP鍵';
COMMENT ON TABLE information IS 'インフォメーション : インフォメーションを扱う。';
COMMENT ON COLUMN information.information_id IS 'インフォメーションID : 
';
COMMENT ON COLUMN information.information_title IS 'インフォメーションタイトル';
COMMENT ON COLUMN information.information_detail IS 'インフォメーション詳細';
COMMENT ON COLUMN information.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN information.from_published_at IS '公開開始日時';
COMMENT ON COLUMN information.to_published_at IS '公開終了日時';
COMMENT ON COLUMN information.created_at IS '作成日時';
COMMENT ON TABLE message_template IS 'メッセージテンプレート : 理由コードのマスタ。';
COMMENT ON COLUMN message_template.template_key IS 'テンプレートキー : 
';
COMMENT ON COLUMN message_template.template_content IS 'テンプレート本文';
COMMENT ON TABLE mint_transaction IS '発行履歴 : DCJPY 発行 / 償却の処理の状態を管理する。';
COMMENT ON COLUMN mint_transaction.request_id IS 'リクエストID';
COMMENT ON COLUMN mint_transaction.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN mint_transaction.mint_status IS 'mint取引状態 : initialize : 処理開始前
withdrawn : 銀行預金口座から出金済み
withdraw_failed : 銀行預金口座から出金できなかった
minting_failed : 銀行預金口座から出金後、DCJPY 発行が失敗
completed : DCJPY 発行/償却手続きが正常に完了';
COMMENT ON COLUMN mint_transaction.amount IS '金額';
COMMENT ON COLUMN mint_transaction.initialized_at IS '初期登録日時';
COMMENT ON COLUMN mint_transaction.bankgw_resulted_at IS 'BankGW処理結果日時';
COMMENT ON COLUMN mint_transaction.coreapi_resulted_at IS 'CoreAPI処理結果日時';
COMMENT ON TABLE qr_operation IS 'QR操作 : QRコード読み込みに紐づく対象業務を扱う。対象は以下の３種類。
 - サインインを行う (qr_flow_type = ''sign_in'')
 - 自身の操作を確定する (qr_flow_type = ''confirmation'')
 - 申請された依頼を承認する際に確定する (qr_flow_type = ''order'')

qr_flow_type = ''confirmation'' の場合は、operation_detail 列より該当内容が保存されており、qr_flow_type = ''order'' の場合は order_id 列に該当する申請IDが保存されている。
個人ユーザ/法人ユーザ に関する操作か、銀行/事業者に関する操作かは owner_type 列で判断する。';
COMMENT ON COLUMN qr_operation.qr_key IS 'QRキー';
COMMENT ON COLUMN qr_operation.qr_flow_type IS 'QR手続きフロー種別 : sign_in : サインイン
confirmation : 確認フロー
order : 申請フロー';
COMMENT ON COLUMN qr_operation.owner_type IS '利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者';
COMMENT ON COLUMN qr_operation.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN qr_operation.sign_in_id IS 'サインインID';
COMMENT ON COLUMN qr_operation.qr_status IS 'QR処理状態 : qr_flow_type = ''sign_in'' の場合
- pending : 認証アプリ認証前
- in_processing : 処理中
- authenticated : 認証済み
- completed : トークン発行済
- failure : 処理失敗

qr_flow_type が上記以外
- pending : 手続き待ち
- in_processing : 処理中
- completed : 処理済み
- failure : 処理失敗';
COMMENT ON COLUMN qr_operation.expires_at IS '有効期限';
COMMENT ON COLUMN qr_operation.state_code IS 'サインイン時の呼び出し元識別情報 : QR読み込みによるサインインにて、認証アプリによる手続きが完了後、フロントにトークン情報を返却してよいか判断するために利用する。';
COMMENT ON COLUMN qr_operation.order_id IS '申請ID';
COMMENT ON COLUMN qr_operation.operation_detail IS '操作内容詳細';
COMMENT ON COLUMN qr_operation.error_code IS 'エラーコード';
COMMENT ON TABLE reason_code IS '理由コード : 理由コードのマスタ。';
COMMENT ON COLUMN reason_code.reason_code IS '理由コード';
COMMENT ON COLUMN reason_code.owner_type IS '利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者';
COMMENT ON COLUMN reason_code.operation_type IS '操作種別';
COMMENT ON COLUMN reason_code.reason_title IS '理由タイトル';
COMMENT ON TABLE service_admin IS 'サービスアドミン';
COMMENT ON COLUMN service_admin.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN service_admin.admin_id IS 'アドミンID';
COMMENT ON TABLE service_device IS 'サービスデバイス : 認証アプリのPush通知用トークンの登録先を管理する';
COMMENT ON COLUMN service_device.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN service_device.os_type IS 'OS種別 : ios : iOS
android : Android';
COMMENT ON COLUMN service_device.platform_arn IS 'プラットフォームARN : 認証アプリプッシュ通知トークン用の登録先';
COMMENT ON TABLE service_owner IS 'サービスオーナ';
COMMENT ON COLUMN service_owner.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN service_owner.service_name IS 'サービス名';
COMMENT ON COLUMN service_owner.zone_id IS 'ソーンID';
COMMENT ON COLUMN service_owner.validator_id IS 'バリデータID';
COMMENT ON COLUMN service_owner.issuer_id IS 'イシュアID';
COMMENT ON TABLE service_user IS 'サービスユーザ : 銀行 / 事業者ユーザを扱う。';
COMMENT ON COLUMN service_user.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user.user_name IS 'ユーザ名';
COMMENT ON COLUMN service_user.service_user_type IS 'サービスユーザ種別 : owner : 管理者
normal : 通常ユーザ';
COMMENT ON COLUMN service_user.user_status IS 'ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。';
COMMENT ON COLUMN service_user.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN service_user.role_id IS 'ロールID';
COMMENT ON COLUMN service_user.registered_at IS '登録日時';
COMMENT ON COLUMN service_user.terminated_at IS '無効日時';
COMMENT ON TABLE service_user_device IS 'サービスユーザ利用端末 : 銀行 / 事業者ユーザのスマートフォン(iOS / Android)にPush通知を送信するために必要なデバイストークン(push_token)を管理する。';
COMMENT ON COLUMN service_user_device.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_device.push_token IS 'プッシュ通知用トークン';
COMMENT ON COLUMN service_user_device.os_type IS 'OS種別 : ios : iOS
android : Android';
COMMENT ON TABLE service_user_file_task IS 'サービスユーザファイルタスク : 銀行 / 事業者ユーザのファイルタスクを扱う。';
COMMENT ON COLUMN service_user_file_task.task_id IS 'タスクID';
COMMENT ON COLUMN service_user_file_task.task_type IS 'タスク種別 : nft_list : NFT一覧ファイル作成
nft_mint : NFT発行
nft_transfer : NFT移転';
COMMENT ON COLUMN service_user_file_task.task_status IS 'タスク状態 : initialized : 初回処理待ち
accept : 受付済み
format_failure : フォーマット不備
in_processing : 処理実行中
completed : 処理完了
failed : 処理失敗';
COMMENT ON COLUMN service_user_file_task.ordered_at IS '申請日時';
COMMENT ON COLUMN service_user_file_task.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_file_task.file_name IS 'アップロードファイル名';
COMMENT ON COLUMN service_user_file_task.task_order_detail IS 'タスク入力内容詳細';
COMMENT ON COLUMN service_user_file_task.result_bucket IS '処理結果保存先バケット';
COMMENT ON COLUMN service_user_file_task.result_file_path IS '処理結果ファイルパス';
COMMENT ON COLUMN service_user_file_task.task_result_detail IS '処理結果詳細';
COMMENT ON COLUMN service_user_file_task.completed_at IS '処理完了日時';
COMMENT ON TABLE service_user_file_task_detail IS 'サービスユーザファイルタスク詳細 : 銀行 / 事業者ユーザのファイルタスクの詳細を管理する。';
COMMENT ON COLUMN service_user_file_task_detail.task_id IS 'タスクID';
COMMENT ON COLUMN service_user_file_task_detail.line_number IS '行番号';
COMMENT ON COLUMN service_user_file_task_detail.task_detail_status IS 'タスク詳細状態';
COMMENT ON COLUMN service_user_file_task_detail.task_order_detail IS 'タスク入力内容詳細';
COMMENT ON COLUMN service_user_file_task_detail.task_result_detail IS '処理結果詳細';
COMMENT ON TABLE service_user_order IS 'サービスユーザ申請 : 銀行 / 事業者ユーザの申請情報を管理する。';
COMMENT ON COLUMN service_user_order.order_id IS '申請ID(自動裁判)';
COMMENT ON COLUMN service_user_order.ordered_at IS '申請日時';
COMMENT ON COLUMN service_user_order.service_order_type IS '申請種別 : account_updated : アカウント情報変更

user_suspended : サインイン停止
user_activated : サインイン停止解除（アクティブ）
user_updated : ユーザ情報変更
user_reset : ユーザ認証リセット

account_frozen : アカウント凍結
account_activated : アカウント凍結解除（アクティブ）
account_force_burned : 強制償却
account_force_terminated : 強制解約

info_updated : インフォメーション編集';
COMMENT ON COLUMN service_user_order.order_status IS '申請状態 : pending : 手続き待ち
in_approving : 承認手続き中
approval : 承認
rejected : 否認';
COMMENT ON COLUMN service_user_order.dc_bank_number IS 'DC口座番号';
COMMENT ON COLUMN service_user_order.order_sign_in_id IS '申請者サインインID';
COMMENT ON COLUMN service_user_order.order_detail IS '申請内容詳細';
COMMENT ON COLUMN service_user_order.reviewer_sign_in_id IS '承認/否認者サインインID';
COMMENT ON COLUMN service_user_order.reviewed_at IS '承認/否認日時';
COMMENT ON COLUMN service_user_order.reason_code IS '理由コード';
COMMENT ON COLUMN service_user_order.reason_detail IS '理由詳細';
COMMENT ON COLUMN service_user_order.error_code IS 'エラーコード';
COMMENT ON TABLE service_user_phone IS 'サービスユーザ電話番号 : 銀行 / 事業者ユーザが認証アプリで利用する電話番号を扱う。
サービスで認証アプリ利用がない場合、もしくは初期設定待ちの場合はレコードが存在しない。';
COMMENT ON COLUMN service_user_phone.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_phone.phone_number IS '電話番号';
COMMENT ON TABLE service_user_reason IS 'サービスユーザ理由 : 銀行 / 事業者ユーザの状態が変更された際の理由を扱う。';
COMMENT ON COLUMN service_user_reason.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_reason.operation_type IS '操作種別';
COMMENT ON COLUMN service_user_reason.reason_code IS '理由コード';
COMMENT ON COLUMN service_user_reason.reason_detail IS '理由詳細';
COMMENT ON COLUMN service_user_reason.operated_at IS '操作日時';
COMMENT ON TABLE service_user_role IS 'サービスユーザロール : 銀行 / 事業者ユーザのロールを扱う。';
COMMENT ON COLUMN service_user_role.role_id IS 'ロールID';
COMMENT ON COLUMN service_user_role.role_name IS 'ロール名';
COMMENT ON COLUMN service_user_role.role_type IS 'ロール種別 : individual : 個人ユーザロール
account_owner : アカウント管理者
service_owner : サービス管理者
user_owner : ユーザ管理者
operator : 業務担当
reviewer : 業務承認';
COMMENT ON COLUMN service_user_role.service_id IS 'サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は ''0'' とする';
COMMENT ON COLUMN service_user_role.created_at IS '作成日時';
COMMENT ON TABLE service_user_role_authority IS 'サービスユーザロール権限 : 銀行 / 事業者ユーザのロールごとの操作権限を扱う。';
COMMENT ON COLUMN service_user_role_authority.role_id IS 'ロールID';
COMMENT ON COLUMN service_user_role_authority.authority_key IS '権限名';
COMMENT ON TABLE service_user_sign_in_failure IS 'サービスユーザサインイン失敗 : 銀行/事業者ユーザの直近のサインイン失敗回数を扱う。具体的には下記法則に従う。

- username/password によるサインインが失敗するごとに、失敗回数が1, 2, 3, ... と増加する。
- サインインが成功した場合は、本テーブルの該当レコードを削除する。
- 失敗回数が一定回数以上になった場合は、サインイン一時停止状態に移行するために、ロック解除日時にサインイン一時停止が自動解除される日時を保存する。
- サインインを試みた際に、ロック解除日時が未来日の場合はサインインできない。
  - このとき、APIが返却するユーザ状態は別テーブルの user_status 列の値がユーザ無効の場合を除いて、サインイン一時停止 となる。
- ロック解除日時が NULL もしくは現在以前の日時の場合は、サインイン可能。
- ロック解除日時が現在以前の日時のときにサインインが失敗した場合は、失敗回数を1、ロック解除日時を NULL に更新する。';
COMMENT ON COLUMN service_user_sign_in_failure.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_sign_in_failure.failure_count IS '失敗回数';
COMMENT ON COLUMN service_user_sign_in_failure.last_failed_at IS '最終失敗日時';
COMMENT ON COLUMN service_user_sign_in_failure.lock_expires_at IS 'ロック解除日時';
COMMENT ON TABLE service_user_sign_in_history IS 'サービスユーザサインイン履歴 : 銀行/事業者ユーザの最新のサインイン日時と前回のサインイン日時を扱う。
ユーザがサインインすると、以下のように本テーブルのレコードを更新する。
- ユーザがサインインした日時が「最新のサインイン日時」に保存される
- 上記にあわせ、更新前の「最新のサインイン日時」の値が「前回のサインイン日時」に保存される

上記により、ユーザ自身が前回のサインイン日時を照会する場合は「前回のサインイン日時」列を参照し、ユーザ管理者がユーザ一一覧にて直近のサインイン日時を紹介する場合は「最新のサインイン日時」列を参照すればよい。';
COMMENT ON COLUMN service_user_sign_in_history.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_sign_in_history.current_signed_in_at IS '最新のサインイン日時';
COMMENT ON COLUMN service_user_sign_in_history.last_signed_in_at IS '前回のサインイン日時';
COMMENT ON TABLE service_user_totp IS 'サービスユーザTOTP : 銀行 / 事業者ユーザのTOTP鍵情報を扱う。
認証アプリを利用しない場合、もしくは初期設定が完了していない場合はレコードが存在しない。';
COMMENT ON COLUMN service_user_totp.sign_in_id IS 'サインインID';
COMMENT ON COLUMN service_user_totp.totp_secret IS 'TOTP鍵';
COMMENT ON TABLE validator_account IS 'バリデータアカウント';
COMMENT ON COLUMN validator_account.validator_id IS 'バリデータID';
COMMENT ON COLUMN validator_account.dc_bank_number IS 'DC口座番号';



