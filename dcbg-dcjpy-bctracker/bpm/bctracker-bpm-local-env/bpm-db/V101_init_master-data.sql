
INSERT INTO dc_user_role (role_id, role_name, role_type, service_id, created_at)
VALUES ('********-0000-4000-0000-********0000', '個人ユーザ', 'individual', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0010', '口座管理者', 'account_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0011', 'ユーザ管理者', 'user_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0012', '業務担当者', 'operator', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0013', '業務承認者', 'reviewer', '0', CURRENT_TIMESTAMP);

INSERT INTO service_user_role (role_id, role_name, role_type, service_id, created_at)
VALUES ('********-0000-4000-0000-********0020', 'サービス管理者', 'service_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0021', 'ユーザ管理者', 'user_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-************', '業務担当者', 'operator', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0023', '業務承認者', 'reviewer', '0', CURRENT_TIMESTAMP);

INSERT INTO dc_user_role_authority (role_id, authority_key)
VALUES ('********-0000-4000-0000-********0010', 'mint'),
       ('********-0000-4000-0000-********0010', 'burn'),
       ('********-0000-4000-0000-********0010', 'transfer'),
       ('********-0000-4000-0000-********0010', 'charge'),
       ('********-0000-4000-0000-********0010', 'discharge'),
       ('********-0000-4000-0000-********0010', 'view_account'),
       ('********-0000-4000-0000-********0010', 'view_transactions'),
       ('********-0000-4000-0000-********0010', 'change_account'),
       ('********-0000-4000-0000-********0012', 'mint'),
       ('********-0000-4000-0000-********0012', 'burn'),
       ('********-0000-4000-0000-********0012', 'transfer'),
       ('********-0000-4000-0000-********0012', 'charge'),
       ('********-0000-4000-0000-********0012', 'discharge'),
       ('********-0000-4000-0000-********0012', 'view_account'),
       ('********-0000-4000-0000-********0012', 'view_transactions'),
       ('********-0000-4000-0000-********0012', 'change_account'),
       ('********-0000-4000-0000-********0013', 'mint'),
       ('********-0000-4000-0000-********0013', 'burn'),
       ('********-0000-4000-0000-********0013', 'transfer'),
       ('********-0000-4000-0000-********0013', 'charge'),
       ('********-0000-4000-0000-********0013', 'discharge'),
       ('********-0000-4000-0000-********0013', 'view_account'),
       ('********-0000-4000-0000-********0013', 'view_transactions'),
       ('********-0000-4000-0000-********0013', 'change_account');

INSERT INTO service_user_role_authority (role_id, authority_key)
VALUES ('********-0000-4000-0000-********0020', 'view_accounts'),
       ('********-0000-4000-0000-********0020', 'view_account_detail'),
       ('********-0000-4000-0000-********0020', 'view_transactions'),
       ('********-0000-4000-0000-********0020', 'change_account'),
       ('********-0000-4000-0000-********0020', 'reset_authentication'),
       ('********-0000-4000-0000-********0020', 'user_suspended'),
       ('********-0000-4000-0000-********0020', 'user_activated'),
       ('********-0000-4000-0000-********0020', 'account_frozen'),
       ('********-0000-4000-0000-********0020', 'account_activated'),
       ('********-0000-4000-0000-********0020', 'account_force_burned'),
       ('********-0000-4000-0000-********0020', 'account_force_terminated'),
       ('********-0000-4000-0000-********0020', 'view_information'),
       ('********-0000-4000-0000-********0020', 'change_information'),
       ('********-0000-4000-0000-********0020', 'mint_nft'),
       ('********-0000-4000-0000-********0020', 'transfer_nft'),
       ('********-0000-4000-0000-********0020', 'view_nft'),
       ('********-0000-4000-0000-************', 'view_accounts'),
       ('********-0000-4000-0000-************', 'view_account_detail'),
       ('********-0000-4000-0000-************', 'view_transactions'),
       ('********-0000-4000-0000-************', 'change_account'),
       ('********-0000-4000-0000-************', 'reset_authentication'),
       ('********-0000-4000-0000-************', 'user_suspended'),
       ('********-0000-4000-0000-************', 'user_activated'),
       ('********-0000-4000-0000-************', 'account_frozen'),
       ('********-0000-4000-0000-************', 'account_activated'),
       ('********-0000-4000-0000-************', 'account_force_burned'),
       ('********-0000-4000-0000-************', 'account_force_terminated'),
       ('********-0000-4000-0000-************', 'view_information'),
       ('********-0000-4000-0000-************', 'change_information'),
       ('********-0000-4000-0000-************', 'mint_nft'),
       ('********-0000-4000-0000-************', 'transfer_nft'),
       ('********-0000-4000-0000-************', 'view_nft'),
       ('********-0000-4000-0000-********0023', 'view_accounts'),
       ('********-0000-4000-0000-********0023', 'view_account_detail'),
       ('********-0000-4000-0000-********0023', 'view_transactions'),
       ('********-0000-4000-0000-********0023', 'change_account'),
       ('********-0000-4000-0000-********0023', 'reset_authentication'),
       ('********-0000-4000-0000-********0023', 'user_suspended'),
       ('********-0000-4000-0000-********0023', 'user_activated'),
       ('********-0000-4000-0000-********0023', 'account_frozen'),
       ('********-0000-4000-0000-********0023', 'account_activated'),
       ('********-0000-4000-0000-********0023', 'account_force_burned'),
       ('********-0000-4000-0000-********0023', 'account_force_terminated'),
       ('********-0000-4000-0000-********0023', 'view_information'),
       ('********-0000-4000-0000-********0023', 'change_information'),
       ('********-0000-4000-0000-********0023', 'mint_nft'),
       ('********-0000-4000-0000-********0023', 'transfer_nft'),
       ('********-0000-4000-0000-********0023', 'view_nft');

INSERT INTO message_template(template_key, template_content)
VALUES ('order', e'承認依頼を受けました。\n依頼番号 : ${order_id}\n業務担当者 : ${order_user_name}\n依頼内容 : ${order_type}\n登録完了日時 : ${completed_at}'),
       ('order_completed', e'依頼が承認されました。\n依頼番号 : ${order_id}\n業務承認者 : ${reviewer_user_name}\n依頼内容 : ${order_type}\n登録完了日時 : ${completed_at}'),
       ('order_rejected', e'依頼が否認されました。\n依頼番号 : ${order_id}\n業務承認者 : ${reviewer_user_name}\n依頼内容 : ${order_type}\n否認理由分類 : ${reason_type}\n否認理由詳細 : ${reason_detail}\n登録完了日時 : ${completed_at}'),
       ('transfer', e'DCJPY 移転が完了しました。\n移転完了日時 : ${completed_at}'),
       ('account_updated', e'アカウント情報変更が完了しました。\nアカウント情報変更完了日時 : ${completed_at}'),
       ('sign_in', e'サインインが完了しました。'),
       ('biz_applying', e'アカウント開設受付が完了しました。\nアカウント開設を確定してください。\nビジネスゾーン名 : ${zone_name}\nアカウント開設受付完了日時 : ${operated_at}'),
       ('biz_terminating', e'アカウント解約受付が完了しました。\nアカウント解約を確定してください。\nビジネスゾーン名 : ${zone_name}\nアカウント解約受付完了日時 : ${operated_at}'),
       ('email_transfer', e'${transfer_at} に、お客さまから振込依頼を受け付けました。\nこのメールにお心当たりのない場合や、ご不明な点がある場合は、以下までお問い合わせください。\n${contact_email_address}\n本メールは送信専用のため、返信できません。');

INSERT INTO reason_code(reason_code, owner_type, operation_type, reason_title)
VALUES -- 個人/法人ユーザ アカウント解約
       ('UAST0003', 'user', 'account_terminated', '不正利用'),
       ('UAST0004', 'user', 'account_terminated', '本人死亡/法人廃業'),
       ('UAST0005', 'user', 'account_terminated', '相続/譲渡'),
       ('UAST0007', 'user', 'account_terminated', 'その他'),
       -- 銀行ユーザ アカウント凍結
       ('SASF0003', 'service', 'account_frozen', '不正利用'),
       ('SASF0004', 'service', 'account_frozen', '本人死亡/法人廃業'),
       ('SASF0005', 'service', 'account_frozen', '相続/譲渡'),
       ('SASF0007', 'service', 'account_frozen', 'その他'),
       -- 銀行ユーザ アカウント凍結解除
       ('SASA0006', 'service', 'account_activated', '原因解消'),
       ('SASA0007', 'service', 'account_activated', 'その他'),
       -- 銀行ユーザ アカウント強制償却
       ('SASB0003', 'service', 'account_force_burned', '不正利用'),
       ('SASB0004', 'service', 'account_force_burned', '本人死亡/法人廃業'),
       ('SASB0005', 'service', 'account_force_burned', '相続/譲渡'),
       ('SASB0007', 'service', 'account_force_burned', 'その他'),
       -- 銀行ユーザ アカウント強制解約
       ('SAST0003', 'service', 'account_force_terminated', '不正利用'),
       ('SAST0004', 'service', 'account_force_terminated', '本人死亡/法人廃業'),
       ('SAST0005', 'service', 'account_force_terminated', '相続/譲渡'),
       ('SAST0007', 'service', 'account_force_terminated', 'その他'),
       -- 個人/法人ユーザ サインイン停止
       ('UUSS0001', 'user', 'user_suspended', '端末紛失'),
       ('UUSS0003', 'user', 'user_suspended', '不正利用'),
       ('UUSS0006', 'user', 'user_suspended', 'その他'),
       -- 個人/法人ユーザ ユーザ無効
       ('UUST0004', 'user', 'user_terminated', '使用予定無し'),
       ('UUST0006', 'user', 'user_terminated', 'その他'),
       -- 個人/法人ユーザ サインイン停止解除
       ('UUSA0005', 'user', 'user_activated', '原因解消'),
       ('UUSA0006', 'user', 'user_activated', 'その他'),
       -- 銀行/事業者ユーザ サインイン停止
       ('SUSS0001', 'service', 'user_suspended', '端末紛失'),
       ('SUSS0003', 'service', 'user_suspended', '不正利用'),
       ('SUSS0006', 'service', 'user_suspended', 'その他'),
       -- 銀行/事業者ユーザ ユーザ無効
       ('SUST0004', 'service', 'user_terminated', '使用予定無し'),
       ('SUST0006', 'service', 'user_terminated', 'その他'),
       -- 銀行/事業者ユーザ サインイン停止解除
       ('SUSA0005', 'service', 'user_activated', '原因解消'),
       ('SUSA0006', 'service', 'user_activated', 'その他'),
       -- 銀行/事業者ユーザ 法人ユーザ（アカウント管理者）のサインイン停止
       ('SAUS0001', 'service', 'account_owner_suspended', '端末紛失'),
       ('SAUS0003', 'service', 'account_owner_suspended', '不正利用'),
       ('SAUS0007', 'service', 'account_owner_suspended', 'その他'),
       -- 銀行/事業者ユーザ 法人ユーザ（アカウント管理者）のサインイン停止解除
       ('SAUA0006', 'service', 'account_owner_activated', '原因解消'),
       ('SAUA0007', 'service', 'account_owner_activated', 'その他'),

       -- 個人/法人ユーザ 承認依頼の否認理由
       -- DCJPY発行 承認
       ('UMOR0001', 'user', 'reject_mint', '金額誤り'),
       ('UMOR0006', 'user', 'reject_mint', 'その他'),
       -- DCJPY償却 承認
       ('UBOR0001', 'user', 'reject_burn', '金額誤り'),
       ('UBOR0006', 'user', 'reject_burn', 'その他'),
       -- DCJPY送金 承認/承認受付
       ('UTOR0001', 'user', 'reject_transfer', '金額誤り'),
       ('UTOR0002', 'user', 'reject_transfer', '移転先誤り'),
       ('UTOR0006', 'user', 'reject_transfer', 'その他'),
       -- DCJPYチャージ 承認
       ('UCOR0001', 'user', 'reject_charge', '金額誤り'),
       ('UCOR0002', 'user', 'reject_charge', 'チャージ先誤り'),
       ('UCOR0006', 'user', 'reject_charge', 'その他'),
       -- DCJPYディスチャージ 承認
       ('UDOR0001', 'user', 'reject_discharge', '金額誤り'),
       ('UDOR0006', 'user', 'reject_discharge', 'その他'),
       -- アカウント限度額変更 承認/承認受付
       ('UALR0001', 'user', 'reject_change_account_limit', '変更内容誤り'),
       ('UALR0006', 'user', 'reject_change_account_limit', 'その他'),
       -- DC口座表示名変更 承認/承認受付
       ('UANR0001', 'user', 'reject_change_account_name', '変更内容誤り'),
       ('UANR0006', 'user', 'reject_change_account_name', 'その他'),
       -- 移転許可設定 承認
       ('UATR0001','user','reject_approve_transfer','移転額誤り'),
       ('UATR0006','user','reject_approve_transfer','その他'),
       -- 精算条件設定 承認
       ('USSR0001', 'user', 'reject_change_account_settlement', '精算種別誤り'),
       ('USSR0002', 'user', 'reject_change_account_settlement', '指定日誤り'),
       ('USSR0006', 'user', 'reject_change_account_settlement', 'その他'),

       -- 銀行/事業者ユーザ 承認依頼の否認理由
       -- アカウント限度額変更 承認/承認受付
       ('SALR0001', 'service', 'reject_change_account_limit', '変更内容誤り'),
       ('SALR0002', 'service', 'reject_change_account_limit', '変更対象誤り'),
       ('SALR0006', 'service', 'reject_change_account_limit', 'その他'),
       -- DC口座表示名変更 承認/承認受付
       ('SANR0001', 'service', 'reject_change_account_name', '変更内容誤り'),
       ('SANR0002', 'service', 'reject_change_account_name', '変更対象誤り'),
       ('SANR0006', 'service', 'reject_change_account_name', 'その他'),
       -- アカウント 凍結 承認/承認受付
       ('SAFR0001', 'service', 'reject_account_frozen', '変更内容誤り'),
       ('SAFR0006', 'service', 'reject_account_frozen', 'その他'),
       -- アカウント 凍結解除 承認/承認受付
       ('SAAR0001', 'service', 'reject_account_activated', '変更内容誤り'),
       ('SAAR0006', 'service', 'reject_account_activated', 'その他'),
       -- アカウント 強制償却 承認/承認受付
       ('SABR0001', 'service', 'reject_account_force_burned', '変更内容誤り'),
       ('SABR0006', 'service', 'reject_account_force_burned', 'その他'),
       -- アカウント 強制解約 承認/承認受付
       ('SATR0001', 'service', 'reject_account_force_terminated', '変更内容誤り'),
       ('SATR0006', 'service', 'reject_account_force_terminated', 'その他'),
       -- ユーザ 情報変更 承認/承認受付
       ('SUNR0001', 'service', 'reject_change_user_name', '変更内容誤り'),
       ('SUNR0002', 'service', 'reject_change_user_name', '変更対象誤り'),
       ('SUNR0006', 'service', 'reject_change_user_name', 'その他'),
       -- ユーザ 認証リセット 承認/承認受付
       ('SARR0001', 'service', 'reject_reset_auth', '変更内容誤り'),
       ('SARR0006', 'service', 'reject_reset_auth', 'その他'),
       -- サインイン停止 承認/承認受付
       ('SUSR0001', 'service', 'reject_user_suspended', '変更内容誤り'),
       ('SUSR0006', 'service', 'reject_user_suspended', 'その他'),
       -- サインイン停止解除 承認/承認受付
       ('SUAR0001', 'service', 'reject_user_activated', '変更内容誤り'),
       ('SUAR0006', 'service', 'reject_user_activated', 'その他'),
       -- インフォメーション 編集 承認/承認受付
       ('SIER0001', 'service', 'reject_change_information', '変更内容誤り'),
       ('SIER0006', 'service', 'reject_change_information', 'その他'),

       -- 承認処理 システムエラー
       ('USYR0001', 'user', 'system_failure', 'エラー'),
       ('SSYR0001', 'service', 'system_failure', 'エラー');
