SELECT
    *
FROM dc_user
WHERE EXISTS(
    SELECT
        1
    FROM dc_user_account
        INNER JOIN dc_account
            ON dc_user_account.dc_bank_number = dc_account.dc_bank_number
                AND dc_user_account.service_id = dc_account.service_id
                AND dc_account.account_id = /* accountId */''
        INNER JOIN service_owner
            ON dc_account.service_id = service_owner.service_id
                AND service_owner.validator_id = /* validatorId */''
    WHERE dc_user.sign_in_id = dc_user_account.sign_in_id
) AND dc_user.dc_user_type IN ('company_owner', 'individual')