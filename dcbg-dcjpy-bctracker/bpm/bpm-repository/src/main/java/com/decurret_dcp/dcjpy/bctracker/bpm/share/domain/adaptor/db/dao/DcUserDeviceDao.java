package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;

@ConfigAutowireable
@Dao
public interface DcUserDeviceDao {

    @Select
    public DcUserDeviceEntity selectById(SignInId signInId);
}
