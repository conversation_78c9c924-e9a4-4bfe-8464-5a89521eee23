/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.IssuerId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスオーナ.
 */
@Entity(immutable = true)
@Table(name = "service_owner")
public class ServiceOwnerEntity {

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** サービス名. */
    @Column(name = "service_name")
    public final String serviceName;

    /** ソーンID. */
    @Column(name = "zone_id")
    public final ZoneId zoneId;

    /** バリデータID. */
    @Column(name = "validator_id")
    public final ValidatorId validatorId;

    /** イシュアID. */
    @Column(name = "issuer_id")
    public final IssuerId issuerId;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param serviceName サービス名
     * @param zoneId ソーンID
     * @param validatorId バリデータID
     * @param issuerId イシュアID
     */
    ServiceOwnerEntity(
        ServiceId serviceId,
        String serviceName,
        ZoneId zoneId,
        ValidatorId validatorId,
        IssuerId issuerId
    ) {
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.zoneId = zoneId;
        this.validatorId = validatorId;
        this.issuerId = issuerId;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceOwnerEntity オブジェクト
     */
    protected ServiceOwnerEntity(ServiceOwnerEntity org) {
        this.serviceId = org.serviceId;
        this.serviceName = org.serviceName;
        this.zoneId = org.zoneId;
        this.validatorId = org.validatorId;
        this.issuerId = org.issuerId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceOwnerEntity [")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("serviceName=").append(this.serviceName).append(", ")
                .append("zoneId=").append(this.zoneId).append(", ")
                .append("validatorId=").append(this.validatorId).append(", ")
                .append("issuerId=").append(this.issuerId)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.serviceId, 
                this.serviceName, 
                this.zoneId, 
                this.validatorId, 
                this.issuerId
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceOwnerEntity other = (ServiceOwnerEntity) obj;
        return true
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.serviceName, other.serviceName)
                && Objects.equals(this.zoneId, other.zoneId)
                && Objects.equals(this.validatorId, other.validatorId)
                && Objects.equals(this.issuerId, other.issuerId)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceOwnerEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** サービス名. */
        private String serviceName;

        /** ソーンID. */
        private ZoneId zoneId;

        /** バリデータID. */
        private ValidatorId validatorId;

        /** イシュアID. */
        private IssuerId issuerId;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceOwnerEntity object
         */
        public ServiceOwnerEntity build() {
            return new ServiceOwnerEntity(
                    this.serviceId, 
                    this.serviceName, 
                    this.zoneId, 
                    this.validatorId, 
                    this.issuerId
            );
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set serviceName.
         *
         * @param serviceName サービス名
         * @return this builder
         */
        public Builder serviceName(String serviceName) {
            this.serviceName = serviceName;
            return this;
        }

        /**
         * Set zoneId.
         *
         * @param zoneId ソーンID
         * @return this builder
         */
        public Builder zoneId(ZoneId zoneId) {
            this.zoneId = zoneId;
            return this;
        }

        /**
         * Set validatorId.
         *
         * @param validatorId バリデータID
         * @return this builder
         */
        public Builder validatorId(ValidatorId validatorId) {
            this.validatorId = validatorId;
            return this;
        }

        /**
         * Set issuerId.
         *
         * @param issuerId イシュアID
         * @return this builder
         */
        public Builder issuerId(IssuerId issuerId) {
            this.issuerId = issuerId;
            return this;
        }
    }
}
// @formatter:on
