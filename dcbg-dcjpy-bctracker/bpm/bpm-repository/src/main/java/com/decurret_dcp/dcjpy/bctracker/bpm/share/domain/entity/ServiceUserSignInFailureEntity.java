/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザサインイン失敗 : 銀行/事業者ユーザの直近のサインイン失敗回数を扱う。具体的には下記法則に従う。

- username/password によるサインインが失敗するごとに、失敗回数が1, 2, 3, ... と増加する。
- サインインが成功した場合は、本テーブルの該当レコードを削除する。
- 失敗回数が一定回数以上になった場合は、サインイン一時停止状態に移行するために、ロック解除日時にサインイン一時停止が自動解除される日時を保存する。
- サインインを試みた際に、ロック解除日時が未来日の場合はサインインできない。
  - このとき、APIが返却するユーザ状態は別テーブルの user_status 列の値がユーザ無効の場合を除いて、サインイン一時停止 となる。
- ロック解除日時が NULL もしくは現在以前の日時の場合は、サインイン可能。
- ロック解除日時が現在以前の日時のときにサインインが失敗した場合は、失敗回数を1、ロック解除日時を NULL に更新する。.
 */
@Entity(immutable = true)
@Table(name = "service_user_sign_in_failure")
public class ServiceUserSignInFailureEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 失敗回数. */
    @Column(name = "failure_count")
    public final Integer failureCount;

    /** 最終失敗日時. */
    @Column(name = "last_failed_at")
    public final AppTimeStamp lastFailedAt;

    /** ロック解除日時. */
    @Column(name = "lock_expires_at")
    public final AppTimeStamp lockExpiresAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param failureCount 失敗回数
     * @param lastFailedAt 最終失敗日時
     * @param lockExpiresAt ロック解除日時
     */
    ServiceUserSignInFailureEntity(
        SignInId signInId,
        Integer failureCount,
        AppTimeStamp lastFailedAt,
        AppTimeStamp lockExpiresAt
    ) {
        this.signInId = signInId;
        this.failureCount = failureCount;
        this.lastFailedAt = lastFailedAt;
        this.lockExpiresAt = lockExpiresAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserSignInFailureEntity オブジェクト
     */
    protected ServiceUserSignInFailureEntity(ServiceUserSignInFailureEntity org) {
        this.signInId = org.signInId;
        this.failureCount = org.failureCount;
        this.lastFailedAt = org.lastFailedAt;
        this.lockExpiresAt = org.lockExpiresAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserSignInFailureEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("failureCount=").append(this.failureCount).append(", ")
                .append("lastFailedAt=").append(this.lastFailedAt).append(", ")
                .append("lockExpiresAt=").append(this.lockExpiresAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.failureCount, 
                this.lastFailedAt, 
                this.lockExpiresAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserSignInFailureEntity other = (ServiceUserSignInFailureEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.failureCount, other.failureCount)
                && Objects.equals(this.lastFailedAt, other.lastFailedAt)
                && Objects.equals(this.lockExpiresAt, other.lockExpiresAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserSignInFailureEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 失敗回数. */
        private Integer failureCount;

        /** 最終失敗日時. */
        private AppTimeStamp lastFailedAt;

        /** ロック解除日時. */
        private AppTimeStamp lockExpiresAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserSignInFailureEntity object
         */
        public ServiceUserSignInFailureEntity build() {
            return new ServiceUserSignInFailureEntity(
                    this.signInId, 
                    this.failureCount, 
                    this.lastFailedAt, 
                    this.lockExpiresAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set failureCount.
         *
         * @param failureCount 失敗回数
         * @return this builder
         */
        public Builder failureCount(Integer failureCount) {
            this.failureCount = failureCount;
            return this;
        }

        /**
         * Set lastFailedAt.
         *
         * @param lastFailedAt 最終失敗日時
         * @return this builder
         */
        public Builder lastFailedAt(AppTimeStamp lastFailedAt) {
            this.lastFailedAt = lastFailedAt;
            return this;
        }

        /**
         * Set lockExpiresAt.
         *
         * @param lockExpiresAt ロック解除日時
         * @return this builder
         */
        public Builder lockExpiresAt(AppTimeStamp lockExpiresAt) {
            this.lockExpiresAt = lockExpiresAt;
            return this;
        }
    }
}
// @formatter:on
