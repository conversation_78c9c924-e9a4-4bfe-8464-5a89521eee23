package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserEntity;

@ConfigAutowireable
@Dao
public interface DcUserDao {

    @Select
    public DcUserEntity selectAccountOwner(AccountId accountId, ValidatorId validatorId);
}
