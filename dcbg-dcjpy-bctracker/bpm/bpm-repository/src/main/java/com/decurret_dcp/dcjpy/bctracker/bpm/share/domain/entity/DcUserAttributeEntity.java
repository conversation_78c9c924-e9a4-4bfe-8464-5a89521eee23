/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AttributeDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ属性 : DCユーザの汎用的な属性情報を扱う.
 */
@Entity(immutable = true)
@Table(name = "dc_user_attribute")
public class DcUserAttributeEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 属性詳細. */
    @Column(name = "attribute_detail")
    public final AttributeDetail attributeDetail;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param attributeDetail 属性詳細
     */
    DcUserAttributeEntity(
        SignInId signInId,
        AttributeDetail attributeDetail
    ) {
        this.signInId = signInId;
        this.attributeDetail = attributeDetail;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserAttributeEntity オブジェクト
     */
    protected DcUserAttributeEntity(DcUserAttributeEntity org) {
        this.signInId = org.signInId;
        this.attributeDetail = org.attributeDetail;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserAttributeEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("attributeDetail=").append(this.attributeDetail)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.attributeDetail
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserAttributeEntity other = (DcUserAttributeEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.attributeDetail, other.attributeDetail)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserAttributeEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 属性詳細. */
        private AttributeDetail attributeDetail;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserAttributeEntity object
         */
        public DcUserAttributeEntity build() {
            return new DcUserAttributeEntity(
                    this.signInId, 
                    this.attributeDetail
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set attributeDetail.
         *
         * @param attributeDetail 属性詳細
         * @return this builder
         */
        public Builder attributeDetail(AttributeDetail attributeDetail) {
            this.attributeDetail = attributeDetail;
            return this;
        }
    }
}
// @formatter:on
