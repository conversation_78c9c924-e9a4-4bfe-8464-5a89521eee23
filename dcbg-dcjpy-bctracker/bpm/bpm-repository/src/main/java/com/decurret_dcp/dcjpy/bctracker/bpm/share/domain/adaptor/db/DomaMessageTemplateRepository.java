package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao.MessageTemplateDao;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.MessageTemplateEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository.MessageTemplateRepository;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

@AllArgsConstructor
@Repository
public class DomaMessageTemplateRepository implements MessageTemplateRepository {

    private final MessageTemplateDao messageTemplateDao;

    @Override
    public MessageTemplateEntity findTemplate(TemplateKey key) {
        return this.messageTemplateDao.selectById(key);
    }
}
