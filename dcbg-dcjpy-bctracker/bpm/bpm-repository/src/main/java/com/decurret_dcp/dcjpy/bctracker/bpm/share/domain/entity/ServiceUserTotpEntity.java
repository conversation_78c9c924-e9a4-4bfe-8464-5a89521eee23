/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TotpSecret;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザTOTP : 銀行 / 事業者ユーザのTOTP鍵情報を扱う。
認証アプリを利用しない場合、もしくは初期設定が完了していない場合はレコードが存在しない。.
 */
@Entity(immutable = true)
@Table(name = "service_user_totp")
public class ServiceUserTotpEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** TOTP鍵. */
    @Column(name = "totp_secret")
    public final TotpSecret totpSecret;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param totpSecret TOTP鍵
     */
    ServiceUserTotpEntity(
        SignInId signInId,
        TotpSecret totpSecret
    ) {
        this.signInId = signInId;
        this.totpSecret = totpSecret;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserTotpEntity オブジェクト
     */
    protected ServiceUserTotpEntity(ServiceUserTotpEntity org) {
        this.signInId = org.signInId;
        this.totpSecret = org.totpSecret;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserTotpEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("totpSecret=").append(this.totpSecret)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.totpSecret
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserTotpEntity other = (ServiceUserTotpEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.totpSecret, other.totpSecret)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserTotpEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** TOTP鍵. */
        private TotpSecret totpSecret;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserTotpEntity object
         */
        public ServiceUserTotpEntity build() {
            return new ServiceUserTotpEntity(
                    this.signInId, 
                    this.totpSecret
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set totpSecret.
         *
         * @param totpSecret TOTP鍵
         * @return this builder
         */
        public Builder totpSecret(TotpSecret totpSecret) {
            this.totpSecret = totpSecret;
            return this;
        }
    }
}
// @formatter:on
