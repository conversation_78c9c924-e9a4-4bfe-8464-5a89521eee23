/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OwnerType;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * 理由コード : 理由コードのマスタ。.
 */
@Entity(immutable = true)
@Table(name = "reason_code")
public class ReasonCodeEntity {

    /** 理由コード. */
    @Id
    @Column(name = "reason_code")
    public final String reasonCode;

    /** 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者. */
    @Column(name = "owner_type")
    public final OwnerType ownerType;

    /** 操作種別. */
    @Column(name = "operation_type")
    public final OperationType operationType;

    /** 理由タイトル. */
    @Column(name = "reason_title")
    public final String reasonTitle;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param reasonCode 理由コード
     * @param ownerType 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者
     * @param operationType 操作種別
     * @param reasonTitle 理由タイトル
     */
    ReasonCodeEntity(
        String reasonCode,
        OwnerType ownerType,
        OperationType operationType,
        String reasonTitle
    ) {
        this.reasonCode = reasonCode;
        this.ownerType = ownerType;
        this.operationType = operationType;
        this.reasonTitle = reasonTitle;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ReasonCodeEntity オブジェクト
     */
    protected ReasonCodeEntity(ReasonCodeEntity org) {
        this.reasonCode = org.reasonCode;
        this.ownerType = org.ownerType;
        this.operationType = org.operationType;
        this.reasonTitle = org.reasonTitle;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ReasonCodeEntity [")
                .append("reasonCode=").append(this.reasonCode).append(", ")
                .append("ownerType=").append(this.ownerType).append(", ")
                .append("operationType=").append(this.operationType).append(", ")
                .append("reasonTitle=").append(this.reasonTitle)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.reasonCode, 
                this.ownerType, 
                this.operationType, 
                this.reasonTitle
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ReasonCodeEntity other = (ReasonCodeEntity) obj;
        return true
                && Objects.equals(this.reasonCode, other.reasonCode)
                && Objects.equals(this.ownerType, other.ownerType)
                && Objects.equals(this.operationType, other.operationType)
                && Objects.equals(this.reasonTitle, other.reasonTitle)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ReasonCodeEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** 理由コード. */
        private String reasonCode;

        /** 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者. */
        private OwnerType ownerType;

        /** 操作種別. */
        private OperationType operationType;

        /** 理由タイトル. */
        private String reasonTitle;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ReasonCodeEntity object
         */
        public ReasonCodeEntity build() {
            return new ReasonCodeEntity(
                    this.reasonCode, 
                    this.ownerType, 
                    this.operationType, 
                    this.reasonTitle
            );
        }

        /**
         * Set reasonCode.
         *
         * @param reasonCode 理由コード
         * @return this builder
         */
        public Builder reasonCode(String reasonCode) {
            this.reasonCode = reasonCode;
            return this;
        }

        /**
         * Set ownerType.
         *
         * @param ownerType 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者
         * @return this builder
         */
        public Builder ownerType(OwnerType ownerType) {
            this.ownerType = ownerType;
            return this;
        }

        /**
         * Set operationType.
         *
         * @param operationType 操作種別
         * @return this builder
         */
        public Builder operationType(OperationType operationType) {
            this.operationType = operationType;
            return this;
        }

        /**
         * Set reasonTitle.
         *
         * @param reasonTitle 理由タイトル
         * @return this builder
         */
        public Builder reasonTitle(String reasonTitle) {
            this.reasonTitle = reasonTitle;
            return this;
        }
    }
}
// @formatter:on
