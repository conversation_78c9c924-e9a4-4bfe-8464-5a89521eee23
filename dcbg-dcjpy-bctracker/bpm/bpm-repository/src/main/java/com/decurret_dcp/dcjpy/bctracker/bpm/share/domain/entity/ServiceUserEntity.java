/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RoleId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceUserType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.UserStatus;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザ : 銀行 / 事業者ユーザを扱う。.
 */
@Entity(immutable = true)
@Table(name = "service_user")
public class ServiceUserEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** ユーザ名. */
    @Column(name = "user_name")
    public final String userName;

    /** サービスユーザ種別 : owner : 管理者
normal : 通常ユーザ. */
    @Column(name = "service_user_type")
    public final ServiceUserType serviceUserType;

    /** ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。. */
    @Column(name = "user_status")
    public final UserStatus userStatus;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** ロールID. */
    @Column(name = "role_id")
    public final RoleId roleId;

    /** 登録日時. */
    @Column(name = "registered_at")
    public final AppTimeStamp registeredAt;

    /** 無効日時. */
    @Column(name = "terminated_at")
    public final AppTimeStamp terminatedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param userName ユーザ名
     * @param serviceUserType サービスユーザ種別 : owner : 管理者
normal : 通常ユーザ
     * @param userStatus ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param roleId ロールID
     * @param registeredAt 登録日時
     * @param terminatedAt 無効日時
     */
    ServiceUserEntity(
        SignInId signInId,
        String userName,
        ServiceUserType serviceUserType,
        UserStatus userStatus,
        ServiceId serviceId,
        RoleId roleId,
        AppTimeStamp registeredAt,
        AppTimeStamp terminatedAt
    ) {
        this.signInId = signInId;
        this.userName = userName;
        this.serviceUserType = serviceUserType;
        this.userStatus = userStatus;
        this.serviceId = serviceId;
        this.roleId = roleId;
        this.registeredAt = registeredAt;
        this.terminatedAt = terminatedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserEntity オブジェクト
     */
    protected ServiceUserEntity(ServiceUserEntity org) {
        this.signInId = org.signInId;
        this.userName = org.userName;
        this.serviceUserType = org.serviceUserType;
        this.userStatus = org.userStatus;
        this.serviceId = org.serviceId;
        this.roleId = org.roleId;
        this.registeredAt = org.registeredAt;
        this.terminatedAt = org.terminatedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("userName=").append(this.userName).append(", ")
                .append("serviceUserType=").append(this.serviceUserType).append(", ")
                .append("userStatus=").append(this.userStatus).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("roleId=").append(this.roleId).append(", ")
                .append("registeredAt=").append(this.registeredAt).append(", ")
                .append("terminatedAt=").append(this.terminatedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.userName, 
                this.serviceUserType, 
                this.userStatus, 
                this.serviceId, 
                this.roleId, 
                this.registeredAt, 
                this.terminatedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserEntity other = (ServiceUserEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.userName, other.userName)
                && Objects.equals(this.serviceUserType, other.serviceUserType)
                && Objects.equals(this.userStatus, other.userStatus)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.roleId, other.roleId)
                && Objects.equals(this.registeredAt, other.registeredAt)
                && Objects.equals(this.terminatedAt, other.terminatedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** ユーザ名. */
        private String userName;

        /** サービスユーザ種別 : owner : 管理者
normal : 通常ユーザ. */
        private ServiceUserType serviceUserType;

        /** ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。. */
        private UserStatus userStatus;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** ロールID. */
        private RoleId roleId;

        /** 登録日時. */
        private AppTimeStamp registeredAt;

        /** 無効日時. */
        private AppTimeStamp terminatedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserEntity object
         */
        public ServiceUserEntity build() {
            return new ServiceUserEntity(
                    this.signInId, 
                    this.userName, 
                    this.serviceUserType, 
                    this.userStatus, 
                    this.serviceId, 
                    this.roleId, 
                    this.registeredAt, 
                    this.terminatedAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set userName.
         *
         * @param userName ユーザ名
         * @return this builder
         */
        public Builder userName(String userName) {
            this.userName = userName;
            return this;
        }

        /**
         * Set serviceUserType.
         *
         * @param serviceUserType サービスユーザ種別 : owner : 管理者
normal : 通常ユーザ
         * @return this builder
         */
        public Builder serviceUserType(ServiceUserType serviceUserType) {
            this.serviceUserType = serviceUserType;
            return this;
        }

        /**
         * Set userStatus.
         *
         * @param userStatus ユーザ状態 : initializing : 初期設定待ち
active : アクティブ
suspended : サインイン停止
inactive : ユーザ無効

※ サインイン一時停止 状態はこのカラムではなく、「サインイン失敗」テーブルのロック解除日時が未来時間が設定されているか否かで判定する。
         * @return this builder
         */
        public Builder userStatus(UserStatus userStatus) {
            this.userStatus = userStatus;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set roleId.
         *
         * @param roleId ロールID
         * @return this builder
         */
        public Builder roleId(RoleId roleId) {
            this.roleId = roleId;
            return this;
        }

        /**
         * Set registeredAt.
         *
         * @param registeredAt 登録日時
         * @return this builder
         */
        public Builder registeredAt(AppTimeStamp registeredAt) {
            this.registeredAt = registeredAt;
            return this;
        }

        /**
         * Set terminatedAt.
         *
         * @param terminatedAt 無効日時
         * @return this builder
         */
        public Builder terminatedAt(AppTimeStamp terminatedAt) {
            this.terminatedAt = terminatedAt;
            return this;
        }
    }
}
// @formatter:on
