/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcUserRoleType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RoleId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザロール : 個人 / 法人ユーザのロールを扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_role")
public class DcUserRoleEntity {

    /** ロールID. */
    @Id
    @Column(name = "role_id")
    public final RoleId roleId;

    /** ロール名. */
    @Column(name = "role_name")
    public final String roleName;

    /** ロール種別 : individual : 個人ユーザロール
account_owner : アカウント管理者
service_owner : サービス管理者
user_owner : ユーザ管理者
operator : 業務担当
reviewer : 業務承認. */
    @Column(name = "role_type")
    public final DcUserRoleType roleType;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** 所属DC口座番号. */
    @Column(name = "belonged_dc_bank_number")
    public final DcBankNumber belongedDcBankNumber;

    /** 作成日時. */
    @Column(name = "created_at")
    public final AppTimeStamp createdAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param roleId ロールID
     * @param roleName ロール名
     * @param roleType ロール種別 : individual : 個人ユーザロール
account_owner : アカウント管理者
service_owner : サービス管理者
user_owner : ユーザ管理者
operator : 業務担当
reviewer : 業務承認
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param belongedDcBankNumber 所属DC口座番号
     * @param createdAt 作成日時
     */
    DcUserRoleEntity(
        RoleId roleId,
        String roleName,
        DcUserRoleType roleType,
        ServiceId serviceId,
        DcBankNumber belongedDcBankNumber,
        AppTimeStamp createdAt
    ) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.roleType = roleType;
        this.serviceId = serviceId;
        this.belongedDcBankNumber = belongedDcBankNumber;
        this.createdAt = createdAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserRoleEntity オブジェクト
     */
    protected DcUserRoleEntity(DcUserRoleEntity org) {
        this.roleId = org.roleId;
        this.roleName = org.roleName;
        this.roleType = org.roleType;
        this.serviceId = org.serviceId;
        this.belongedDcBankNumber = org.belongedDcBankNumber;
        this.createdAt = org.createdAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserRoleEntity [")
                .append("roleId=").append(this.roleId).append(", ")
                .append("roleName=").append(this.roleName).append(", ")
                .append("roleType=").append(this.roleType).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("belongedDcBankNumber=").append(this.belongedDcBankNumber).append(", ")
                .append("createdAt=").append(this.createdAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.roleId, 
                this.roleName, 
                this.roleType, 
                this.serviceId, 
                this.belongedDcBankNumber, 
                this.createdAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserRoleEntity other = (DcUserRoleEntity) obj;
        return true
                && Objects.equals(this.roleId, other.roleId)
                && Objects.equals(this.roleName, other.roleName)
                && Objects.equals(this.roleType, other.roleType)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.belongedDcBankNumber, other.belongedDcBankNumber)
                && Objects.equals(this.createdAt, other.createdAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserRoleEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** ロールID. */
        private RoleId roleId;

        /** ロール名. */
        private String roleName;

        /** ロール種別 : individual : 個人ユーザロール
account_owner : アカウント管理者
service_owner : サービス管理者
user_owner : ユーザ管理者
operator : 業務担当
reviewer : 業務承認. */
        private DcUserRoleType roleType;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** 所属DC口座番号. */
        private DcBankNumber belongedDcBankNumber;

        /** 作成日時. */
        private AppTimeStamp createdAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserRoleEntity object
         */
        public DcUserRoleEntity build() {
            return new DcUserRoleEntity(
                    this.roleId, 
                    this.roleName, 
                    this.roleType, 
                    this.serviceId, 
                    this.belongedDcBankNumber, 
                    this.createdAt
            );
        }

        /**
         * Set roleId.
         *
         * @param roleId ロールID
         * @return this builder
         */
        public Builder roleId(RoleId roleId) {
            this.roleId = roleId;
            return this;
        }

        /**
         * Set roleName.
         *
         * @param roleName ロール名
         * @return this builder
         */
        public Builder roleName(String roleName) {
            this.roleName = roleName;
            return this;
        }

        /**
         * Set roleType.
         *
         * @param roleType ロール種別 : individual : 個人ユーザロール
account_owner : アカウント管理者
service_owner : サービス管理者
user_owner : ユーザ管理者
operator : 業務担当
reviewer : 業務承認
         * @return this builder
         */
        public Builder roleType(DcUserRoleType roleType) {
            this.roleType = roleType;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set belongedDcBankNumber.
         *
         * @param belongedDcBankNumber 所属DC口座番号
         * @return this builder
         */
        public Builder belongedDcBankNumber(DcBankNumber belongedDcBankNumber) {
            this.belongedDcBankNumber = belongedDcBankNumber;
            return this;
        }

        /**
         * Set createdAt.
         *
         * @param createdAt 作成日時
         * @return this builder
         */
        public Builder createdAt(AppTimeStamp createdAt) {
            this.createdAt = createdAt;
            return this;
        }
    }
}
// @formatter:on
