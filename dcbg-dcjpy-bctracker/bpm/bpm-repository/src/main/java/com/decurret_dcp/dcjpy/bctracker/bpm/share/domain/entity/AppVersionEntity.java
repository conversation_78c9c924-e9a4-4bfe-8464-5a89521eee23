/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OsType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.VersionId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * アプリケーションバージョン : 認証アプリのバージョンを扱う.
 */
@Entity(immutable = true)
@Table(name = "app_version")
public class AppVersionEntity {

    /** バージョンID. */
    @Id
    @Column(name = "version_id")
    public final VersionId versionId;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** OS種別 : ios : iOS
android : Android. */
    @Column(name = "os_type")
    public final OsType osType;

    /** アプリケーションバージョン. */
    @Column(name = "app_version")
    public final String appVersion;

    /** 利用可否. */
    @Column(name = "available")
    public final Boolean available;

    /** 作成日時. */
    @Column(name = "created_at")
    public final AppTimeStamp createdAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param versionId バージョンID
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param osType OS種別 : ios : iOS
android : Android
     * @param appVersion アプリケーションバージョン
     * @param available 利用可否
     * @param createdAt 作成日時
     */
    AppVersionEntity(
        VersionId versionId,
        ServiceId serviceId,
        OsType osType,
        String appVersion,
        Boolean available,
        AppTimeStamp createdAt
    ) {
        this.versionId = versionId;
        this.serviceId = serviceId;
        this.osType = osType;
        this.appVersion = appVersion;
        this.available = available;
        this.createdAt = createdAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org AppVersionEntity オブジェクト
     */
    protected AppVersionEntity(AppVersionEntity org) {
        this.versionId = org.versionId;
        this.serviceId = org.serviceId;
        this.osType = org.osType;
        this.appVersion = org.appVersion;
        this.available = org.available;
        this.createdAt = org.createdAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("AppVersionEntity [")
                .append("versionId=").append(this.versionId).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("osType=").append(this.osType).append(", ")
                .append("appVersion=").append(this.appVersion).append(", ")
                .append("available=").append(this.available).append(", ")
                .append("createdAt=").append(this.createdAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.versionId, 
                this.serviceId, 
                this.osType, 
                this.appVersion, 
                this.available, 
                this.createdAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        AppVersionEntity other = (AppVersionEntity) obj;
        return true
                && Objects.equals(this.versionId, other.versionId)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.osType, other.osType)
                && Objects.equals(this.appVersion, other.appVersion)
                && Objects.equals(this.available, other.available)
                && Objects.equals(this.createdAt, other.createdAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * AppVersionEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** バージョンID. */
        private VersionId versionId;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** OS種別 : ios : iOS
android : Android. */
        private OsType osType;

        /** アプリケーションバージョン. */
        private String appVersion;

        /** 利用可否. */
        private Boolean available;

        /** 作成日時. */
        private AppTimeStamp createdAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the AppVersionEntity object
         */
        public AppVersionEntity build() {
            return new AppVersionEntity(
                    this.versionId, 
                    this.serviceId, 
                    this.osType, 
                    this.appVersion, 
                    this.available, 
                    this.createdAt
            );
        }

        /**
         * Set versionId.
         *
         * @param versionId バージョンID
         * @return this builder
         */
        public Builder versionId(VersionId versionId) {
            this.versionId = versionId;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set osType.
         *
         * @param osType OS種別 : ios : iOS
android : Android
         * @return this builder
         */
        public Builder osType(OsType osType) {
            this.osType = osType;
            return this;
        }

        /**
         * Set appVersion.
         *
         * @param appVersion アプリケーションバージョン
         * @return this builder
         */
        public Builder appVersion(String appVersion) {
            this.appVersion = appVersion;
            return this;
        }

        /**
         * Set available.
         *
         * @param available 利用可否
         * @return this builder
         */
        public Builder available(Boolean available) {
            this.available = available;
            return this;
        }

        /**
         * Set createdAt.
         *
         * @param createdAt 作成日時
         * @return this builder
         */
        public Builder createdAt(AppTimeStamp createdAt) {
            this.createdAt = createdAt;
            return this;
        }
    }
}
// @formatter:on
