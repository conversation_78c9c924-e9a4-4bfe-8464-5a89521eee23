package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcAccountEmailEntity;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;

import java.util.List;

@ConfigAutowireable
@Dao
public interface DcAccountDao {

    @Select
    public DcAccountEmailEntity selectEmailAddressByAccountId(AccountId accountId, ValidatorId validatorId);
}
