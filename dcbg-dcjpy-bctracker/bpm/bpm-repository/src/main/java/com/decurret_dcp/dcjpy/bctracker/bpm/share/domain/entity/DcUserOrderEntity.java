/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OrderDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OrderId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OrderStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OrderType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.GeneratedValue;
import org.seasar.doma.GenerationType;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ申請 : 法人ユーザの申請情報を管理する。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_order")
public class DcUserOrderEntity {

    /** 申請ID(自動裁判). */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "order_id")
    public final OrderId orderId;

    /** 申請日時. */
    @Column(name = "ordered_at")
    public final AppTimeStamp orderedAt;

    /** 申請種別 : FinZoneの場合
- mint : 発行
- burn : 償却
- transfer : 送金
- charge : チャージ
- account_limit_updated : アカウント限度額変更
- account_name_updated : アカウント名変更

BizZoneの場合
- transfer：移転
- discharge：ディスチャージ
- account_name_updated : アカウント名変更
- approve_transfer：移転許可
- set_settlement : 精算条件設定. */
    @Column(name = "order_type")
    public final OrderType orderType;

    /** 申請状態 : pending : 手続き待ち
in_approving : 承認手続き中
approval : 承認
rejected : 否認. */
    @Column(name = "order_status")
    public final OrderStatus orderStatus;

    /** 申請者サインインID. */
    @Column(name = "order_sign_in_id")
    public final SignInId orderSignInId;

    /** 申請内容詳細. */
    @Column(name = "order_detail")
    public final OrderDetail orderDetail;

    /** 承認/否認者サインインID. */
    @Column(name = "reviewer_sign_in_id")
    public final SignInId reviewerSignInId;

    /** 承認/否認日時. */
    @Column(name = "reviewed_at")
    public final AppTimeStamp reviewedAt;

    /** 理由コード. */
    @Column(name = "reason_code")
    public final String reasonCode;

    /** 理由詳細. */
    @Column(name = "reason_detail")
    public final String reasonDetail;

    /** エラーコード. */
    @Column(name = "error_code")
    public final String errorCode;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param orderId 申請ID(自動裁判)
     * @param orderedAt 申請日時
     * @param orderType 申請種別 : FinZoneの場合
- mint : 発行
- burn : 償却
- transfer : 送金
- charge : チャージ
- account_limit_updated : アカウント限度額変更
- account_name_updated : アカウント名変更

BizZoneの場合
- transfer：移転
- discharge：ディスチャージ
- account_name_updated : アカウント名変更
- approve_transfer：移転許可
- set_settlement : 精算条件設定
     * @param orderStatus 申請状態 : pending : 手続き待ち
in_approving : 承認手続き中
approval : 承認
rejected : 否認
     * @param orderSignInId 申請者サインインID
     * @param orderDetail 申請内容詳細
     * @param reviewerSignInId 承認/否認者サインインID
     * @param reviewedAt 承認/否認日時
     * @param reasonCode 理由コード
     * @param reasonDetail 理由詳細
     * @param errorCode エラーコード
     */
    DcUserOrderEntity(
        OrderId orderId,
        AppTimeStamp orderedAt,
        OrderType orderType,
        OrderStatus orderStatus,
        SignInId orderSignInId,
        OrderDetail orderDetail,
        SignInId reviewerSignInId,
        AppTimeStamp reviewedAt,
        String reasonCode,
        String reasonDetail,
        String errorCode
    ) {
        this.orderId = orderId;
        this.orderedAt = orderedAt;
        this.orderType = orderType;
        this.orderStatus = orderStatus;
        this.orderSignInId = orderSignInId;
        this.orderDetail = orderDetail;
        this.reviewerSignInId = reviewerSignInId;
        this.reviewedAt = reviewedAt;
        this.reasonCode = reasonCode;
        this.reasonDetail = reasonDetail;
        this.errorCode = errorCode;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserOrderEntity オブジェクト
     */
    protected DcUserOrderEntity(DcUserOrderEntity org) {
        this.orderId = org.orderId;
        this.orderedAt = org.orderedAt;
        this.orderType = org.orderType;
        this.orderStatus = org.orderStatus;
        this.orderSignInId = org.orderSignInId;
        this.orderDetail = org.orderDetail;
        this.reviewerSignInId = org.reviewerSignInId;
        this.reviewedAt = org.reviewedAt;
        this.reasonCode = org.reasonCode;
        this.reasonDetail = org.reasonDetail;
        this.errorCode = org.errorCode;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserOrderEntity [")
                .append("orderId=").append(this.orderId).append(", ")
                .append("orderedAt=").append(this.orderedAt).append(", ")
                .append("orderType=").append(this.orderType).append(", ")
                .append("orderStatus=").append(this.orderStatus).append(", ")
                .append("orderSignInId=").append(this.orderSignInId).append(", ")
                .append("orderDetail=").append(this.orderDetail).append(", ")
                .append("reviewerSignInId=").append(this.reviewerSignInId).append(", ")
                .append("reviewedAt=").append(this.reviewedAt).append(", ")
                .append("reasonCode=").append(this.reasonCode).append(", ")
                .append("reasonDetail=").append(this.reasonDetail).append(", ")
                .append("errorCode=").append(this.errorCode)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.orderId, 
                this.orderedAt, 
                this.orderType, 
                this.orderStatus, 
                this.orderSignInId, 
                this.orderDetail, 
                this.reviewerSignInId, 
                this.reviewedAt, 
                this.reasonCode, 
                this.reasonDetail, 
                this.errorCode
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserOrderEntity other = (DcUserOrderEntity) obj;
        return true
                && Objects.equals(this.orderId, other.orderId)
                && Objects.equals(this.orderedAt, other.orderedAt)
                && Objects.equals(this.orderType, other.orderType)
                && Objects.equals(this.orderStatus, other.orderStatus)
                && Objects.equals(this.orderSignInId, other.orderSignInId)
                && Objects.equals(this.orderDetail, other.orderDetail)
                && Objects.equals(this.reviewerSignInId, other.reviewerSignInId)
                && Objects.equals(this.reviewedAt, other.reviewedAt)
                && Objects.equals(this.reasonCode, other.reasonCode)
                && Objects.equals(this.reasonDetail, other.reasonDetail)
                && Objects.equals(this.errorCode, other.errorCode)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserOrderEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** 申請ID(自動裁判). */
        private OrderId orderId;

        /** 申請日時. */
        private AppTimeStamp orderedAt;

        /** 申請種別 : FinZoneの場合
- mint : 発行
- burn : 償却
- transfer : 送金
- charge : チャージ
- account_limit_updated : アカウント限度額変更
- account_name_updated : アカウント名変更

BizZoneの場合
- transfer：移転
- discharge：ディスチャージ
- account_name_updated : アカウント名変更
- approve_transfer：移転許可
- set_settlement : 精算条件設定. */
        private OrderType orderType;

        /** 申請状態 : pending : 手続き待ち
in_approving : 承認手続き中
approval : 承認
rejected : 否認. */
        private OrderStatus orderStatus;

        /** 申請者サインインID. */
        private SignInId orderSignInId;

        /** 申請内容詳細. */
        private OrderDetail orderDetail;

        /** 承認/否認者サインインID. */
        private SignInId reviewerSignInId;

        /** 承認/否認日時. */
        private AppTimeStamp reviewedAt;

        /** 理由コード. */
        private String reasonCode;

        /** 理由詳細. */
        private String reasonDetail;

        /** エラーコード. */
        private String errorCode;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserOrderEntity object
         */
        public DcUserOrderEntity build() {
            return new DcUserOrderEntity(
                    this.orderId, 
                    this.orderedAt, 
                    this.orderType, 
                    this.orderStatus, 
                    this.orderSignInId, 
                    this.orderDetail, 
                    this.reviewerSignInId, 
                    this.reviewedAt, 
                    this.reasonCode, 
                    this.reasonDetail, 
                    this.errorCode
            );
        }

        /**
         * Set orderId.
         *
         * @param orderId 申請ID(自動裁判)
         * @return this builder
         */
        public Builder orderId(OrderId orderId) {
            this.orderId = orderId;
            return this;
        }

        /**
         * Set orderedAt.
         *
         * @param orderedAt 申請日時
         * @return this builder
         */
        public Builder orderedAt(AppTimeStamp orderedAt) {
            this.orderedAt = orderedAt;
            return this;
        }

        /**
         * Set orderType.
         *
         * @param orderType 申請種別 : FinZoneの場合
- mint : 発行
- burn : 償却
- transfer : 送金
- charge : チャージ
- account_limit_updated : アカウント限度額変更
- account_name_updated : アカウント名変更

BizZoneの場合
- transfer：移転
- discharge：ディスチャージ
- account_name_updated : アカウント名変更
- approve_transfer：移転許可
- set_settlement : 精算条件設定
         * @return this builder
         */
        public Builder orderType(OrderType orderType) {
            this.orderType = orderType;
            return this;
        }

        /**
         * Set orderStatus.
         *
         * @param orderStatus 申請状態 : pending : 手続き待ち
in_approving : 承認手続き中
approval : 承認
rejected : 否認
         * @return this builder
         */
        public Builder orderStatus(OrderStatus orderStatus) {
            this.orderStatus = orderStatus;
            return this;
        }

        /**
         * Set orderSignInId.
         *
         * @param orderSignInId 申請者サインインID
         * @return this builder
         */
        public Builder orderSignInId(SignInId orderSignInId) {
            this.orderSignInId = orderSignInId;
            return this;
        }

        /**
         * Set orderDetail.
         *
         * @param orderDetail 申請内容詳細
         * @return this builder
         */
        public Builder orderDetail(OrderDetail orderDetail) {
            this.orderDetail = orderDetail;
            return this;
        }

        /**
         * Set reviewerSignInId.
         *
         * @param reviewerSignInId 承認/否認者サインインID
         * @return this builder
         */
        public Builder reviewerSignInId(SignInId reviewerSignInId) {
            this.reviewerSignInId = reviewerSignInId;
            return this;
        }

        /**
         * Set reviewedAt.
         *
         * @param reviewedAt 承認/否認日時
         * @return this builder
         */
        public Builder reviewedAt(AppTimeStamp reviewedAt) {
            this.reviewedAt = reviewedAt;
            return this;
        }

        /**
         * Set reasonCode.
         *
         * @param reasonCode 理由コード
         * @return this builder
         */
        public Builder reasonCode(String reasonCode) {
            this.reasonCode = reasonCode;
            return this;
        }

        /**
         * Set reasonDetail.
         *
         * @param reasonDetail 理由詳細
         * @return this builder
         */
        public Builder reasonDetail(String reasonDetail) {
            this.reasonDetail = reasonDetail;
            return this;
        }

        /**
         * Set errorCode.
         *
         * @param errorCode エラーコード
         * @return this builder
         */
        public Builder errorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }
    }
}
// @formatter:on
