/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザ電話番号 : 銀行 / 事業者ユーザが認証アプリで利用する電話番号を扱う。
サービスで認証アプリ利用がない場合、もしくは初期設定待ちの場合はレコードが存在しない。.
 */
@Entity(immutable = true)
@Table(name = "service_user_phone")
public class ServiceUserPhoneEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 電話番号. */
    @Column(name = "phone_number")
    public final String phoneNumber;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param phoneNumber 電話番号
     */
    ServiceUserPhoneEntity(
        SignInId signInId,
        String phoneNumber
    ) {
        this.signInId = signInId;
        this.phoneNumber = phoneNumber;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserPhoneEntity オブジェクト
     */
    protected ServiceUserPhoneEntity(ServiceUserPhoneEntity org) {
        this.signInId = org.signInId;
        this.phoneNumber = org.phoneNumber;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserPhoneEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("phoneNumber=").append(this.phoneNumber)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.phoneNumber
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserPhoneEntity other = (ServiceUserPhoneEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.phoneNumber, other.phoneNumber)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserPhoneEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 電話番号. */
        private String phoneNumber;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserPhoneEntity object
         */
        public ServiceUserPhoneEntity build() {
            return new ServiceUserPhoneEntity(
                    this.signInId, 
                    this.phoneNumber
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set phoneNumber.
         *
         * @param phoneNumber 電話番号
         * @return this builder
         */
        public Builder phoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }
    }
}
// @formatter:on
