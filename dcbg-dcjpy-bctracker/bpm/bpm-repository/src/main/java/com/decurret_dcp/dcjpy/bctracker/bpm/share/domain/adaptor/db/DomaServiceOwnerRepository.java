package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao.ServiceOwnerDao;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository.ServiceOwnerRepository;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaServiceOwnerRepository implements ServiceOwnerRepository {

    private final ServiceOwnerDao serviceOwnerDao;

    @Override
    public boolean isExists(ValidatorId validatorId) {
        return this.serviceOwnerDao.existsForValidatorId(validatorId);
    }
}
