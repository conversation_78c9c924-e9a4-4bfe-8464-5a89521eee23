/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BankLinkStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DC口座IB接続 : DC口座を扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_account_bank_linkage")
public class DcAccountBankLinkageEntity {

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** IB接続状態 : authenticated : 認証済み
need_auth : 認証が必要. */
    @Column(name = "bank_link_status")
    public final BankLinkStatus bankLinkStatus;

    /** 有効期限. */
    @Column(name = "expires_at")
    public final AppTimeStamp expiresAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param bankLinkStatus IB接続状態 : authenticated : 認証済み
need_auth : 認証が必要
     * @param expiresAt 有効期限
     */
    DcAccountBankLinkageEntity(
        DcBankNumber dcBankNumber,
        ServiceId serviceId,
        BankLinkStatus bankLinkStatus,
        AppTimeStamp expiresAt
    ) {
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
        this.bankLinkStatus = bankLinkStatus;
        this.expiresAt = expiresAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcAccountBankLinkageEntity オブジェクト
     */
    protected DcAccountBankLinkageEntity(DcAccountBankLinkageEntity org) {
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
        this.bankLinkStatus = org.bankLinkStatus;
        this.expiresAt = org.expiresAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcAccountBankLinkageEntity [")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("bankLinkStatus=").append(this.bankLinkStatus).append(", ")
                .append("expiresAt=").append(this.expiresAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.dcBankNumber, 
                this.serviceId, 
                this.bankLinkStatus, 
                this.expiresAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcAccountBankLinkageEntity other = (DcAccountBankLinkageEntity) obj;
        return true
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.bankLinkStatus, other.bankLinkStatus)
                && Objects.equals(this.expiresAt, other.expiresAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcAccountBankLinkageEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** IB接続状態 : authenticated : 認証済み
need_auth : 認証が必要. */
        private BankLinkStatus bankLinkStatus;

        /** 有効期限. */
        private AppTimeStamp expiresAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcAccountBankLinkageEntity object
         */
        public DcAccountBankLinkageEntity build() {
            return new DcAccountBankLinkageEntity(
                    this.dcBankNumber, 
                    this.serviceId, 
                    this.bankLinkStatus, 
                    this.expiresAt
            );
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set bankLinkStatus.
         *
         * @param bankLinkStatus IB接続状態 : authenticated : 認証済み
need_auth : 認証が必要
         * @return this builder
         */
        public Builder bankLinkStatus(BankLinkStatus bankLinkStatus) {
            this.bankLinkStatus = bankLinkStatus;
            return this;
        }

        /**
         * Set expiresAt.
         *
         * @param expiresAt 有効期限
         * @return this builder
         */
        public Builder expiresAt(AppTimeStamp expiresAt) {
            this.expiresAt = expiresAt;
            return this;
        }
    }
}
// @formatter:on
