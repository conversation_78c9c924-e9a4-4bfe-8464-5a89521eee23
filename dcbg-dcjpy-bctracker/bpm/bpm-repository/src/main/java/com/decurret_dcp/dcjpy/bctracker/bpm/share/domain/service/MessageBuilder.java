package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.service;

import java.util.List;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.MessageTemplateEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.repository.MessageTemplateRepository;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class MessageBuilder {

    private static final Object[] EMPTY_ARRAY = {};

    private final MessageTemplateRepository messageTemplateRepository;

    public String buildMessage(TemplateKey key, List<MessageKeywordPair> keywordPairs) {
        MessageTemplateEntity messageTemplate = this.messageTemplateRepository.findTemplate(key);
        if (messageTemplate == null) {
            throw new IllegalStateException("MessageTemplate is not found. key = " + key.getValue());
        }

        String message = messageTemplate.templateContent;
        for (MessageKeywordPair pair : keywordPairs) {
            message = message.replace(pair.keyword.parameter(), pair.value);
        }

        return message;
    }
}
