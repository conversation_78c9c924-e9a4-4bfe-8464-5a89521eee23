/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityType;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * 認証クライアント : CoreAPI の接続情報を扱う.
 */
@Entity(immutable = true)
@Table(name = "auth_client")
public class AuthClientEntity {

    /** エンティティID. */
    @Id
    @Column(name = "entity_id")
    public final EntityId entityId;

    /** エンティティ種別 : admin : アドミニストレータ
provider : プロバイダ
validator : バリデータ
issuer : イシュア. */
    @Id
    @Column(name = "entity_type")
    public final EntityType entityType;

    /** クライアントID. */
    @Column(name = "client_id")
    public final String clientId;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param entityId エンティティID
     * @param entityType エンティティ種別 : admin : アドミニストレータ
provider : プロバイダ
validator : バリデータ
issuer : イシュア
     * @param clientId クライアントID
     */
    AuthClientEntity(
        EntityId entityId,
        EntityType entityType,
        String clientId
    ) {
        this.entityId = entityId;
        this.entityType = entityType;
        this.clientId = clientId;
    }

    /**
     * コンストラクタ。
     * 
     * @param org AuthClientEntity オブジェクト
     */
    protected AuthClientEntity(AuthClientEntity org) {
        this.entityId = org.entityId;
        this.entityType = org.entityType;
        this.clientId = org.clientId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("AuthClientEntity [")
                .append("entityId=").append(this.entityId).append(", ")
                .append("entityType=").append(this.entityType).append(", ")
                .append("clientId=").append(this.clientId)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.entityId, 
                this.entityType, 
                this.clientId
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        AuthClientEntity other = (AuthClientEntity) obj;
        return true
                && Objects.equals(this.entityId, other.entityId)
                && Objects.equals(this.entityType, other.entityType)
                && Objects.equals(this.clientId, other.clientId)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * AuthClientEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** エンティティID. */
        private EntityId entityId;

        /** エンティティ種別 : admin : アドミニストレータ
provider : プロバイダ
validator : バリデータ
issuer : イシュア. */
        private EntityType entityType;

        /** クライアントID. */
        private String clientId;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the AuthClientEntity object
         */
        public AuthClientEntity build() {
            return new AuthClientEntity(
                    this.entityId, 
                    this.entityType, 
                    this.clientId
            );
        }

        /**
         * Set entityId.
         *
         * @param entityId エンティティID
         * @return this builder
         */
        public Builder entityId(EntityId entityId) {
            this.entityId = entityId;
            return this;
        }

        /**
         * Set entityType.
         *
         * @param entityType エンティティ種別 : admin : アドミニストレータ
provider : プロバイダ
validator : バリデータ
issuer : イシュア
         * @return this builder
         */
        public Builder entityType(EntityType entityType) {
            this.entityType = entityType;
            return this;
        }

        /**
         * Set clientId.
         *
         * @param clientId クライアントID
         * @return this builder
         */
        public Builder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }
    }
}
// @formatter:on
