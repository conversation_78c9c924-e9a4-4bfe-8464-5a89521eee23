/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OrderId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OwnerType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.QrFlowType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.QrKey;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.QrStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * QR操作 : QRコード読み込みに紐づく対象業務を扱う。対象は以下の３種類。
 - サインインを行う (qr_flow_type = 'sign_in')
 - 自身の操作を確定する (qr_flow_type = 'confirmation')
 - 申請された依頼を承認する際に確定する (qr_flow_type = 'order')

qr_flow_type = 'confirmation' の場合は、operation_detail 列より該当内容が保存されており、qr_flow_type = 'order' の場合は order_id 列に該当する申請IDが保存されている。
個人ユーザ/法人ユーザ に関する操作か、銀行/事業者に関する操作かは owner_type 列で判断する。.
 */
@Entity(immutable = true)
@Table(name = "qr_operation")
public class QrOperationEntity {

    /** QRキー. */
    @Id
    @Column(name = "qr_key")
    public final QrKey qrKey;

    /** QR手続きフロー種別 : sign_in : サインイン
confirmation : 確認フロー
order : 申請フロー. */
    @Column(name = "qr_flow_type")
    public final QrFlowType qrFlowType;

    /** 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者. */
    @Column(name = "owner_type")
    public final OwnerType ownerType;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** サインインID. */
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** QR処理状態 : qr_flow_type = 'sign_in' の場合
- pending : 認証アプリ認証前
- in_processing : 処理中
- authenticated : 認証済み
- completed : トークン発行済
- failure : 処理失敗

qr_flow_type が上記以外
- pending : 手続き待ち
- in_processing : 処理中
- completed : 処理済み
- failure : 処理失敗. */
    @Column(name = "qr_status")
    public final QrStatus qrStatus;

    /** 有効期限. */
    @Column(name = "expires_at")
    public final AppTimeStamp expiresAt;

    /** サインイン時の呼び出し元識別情報 : QR読み込みによるサインインにて、認証アプリによる手続きが完了後、フロントにトークン情報を返却してよいか判断するために利用する。. */
    @Column(name = "state_code")
    public final String stateCode;

    /** 申請ID. */
    @Column(name = "order_id")
    public final OrderId orderId;

    /** 操作内容詳細. */
    @Column(name = "operation_detail")
    public final OperationDetail operationDetail;

    /** エラーコード. */
    @Column(name = "error_code")
    public final String errorCode;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param qrKey QRキー
     * @param qrFlowType QR手続きフロー種別 : sign_in : サインイン
confirmation : 確認フロー
order : 申請フロー
     * @param ownerType 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param signInId サインインID
     * @param qrStatus QR処理状態 : qr_flow_type = 'sign_in' の場合
- pending : 認証アプリ認証前
- in_processing : 処理中
- authenticated : 認証済み
- completed : トークン発行済
- failure : 処理失敗

qr_flow_type が上記以外
- pending : 手続き待ち
- in_processing : 処理中
- completed : 処理済み
- failure : 処理失敗
     * @param expiresAt 有効期限
     * @param stateCode サインイン時の呼び出し元識別情報 : QR読み込みによるサインインにて、認証アプリによる手続きが完了後、フロントにトークン情報を返却してよいか判断するために利用する。
     * @param orderId 申請ID
     * @param operationDetail 操作内容詳細
     * @param errorCode エラーコード
     */
    QrOperationEntity(
        QrKey qrKey,
        QrFlowType qrFlowType,
        OwnerType ownerType,
        ServiceId serviceId,
        SignInId signInId,
        QrStatus qrStatus,
        AppTimeStamp expiresAt,
        String stateCode,
        OrderId orderId,
        OperationDetail operationDetail,
        String errorCode
    ) {
        this.qrKey = qrKey;
        this.qrFlowType = qrFlowType;
        this.ownerType = ownerType;
        this.serviceId = serviceId;
        this.signInId = signInId;
        this.qrStatus = qrStatus;
        this.expiresAt = expiresAt;
        this.stateCode = stateCode;
        this.orderId = orderId;
        this.operationDetail = operationDetail;
        this.errorCode = errorCode;
    }

    /**
     * コンストラクタ。
     * 
     * @param org QrOperationEntity オブジェクト
     */
    protected QrOperationEntity(QrOperationEntity org) {
        this.qrKey = org.qrKey;
        this.qrFlowType = org.qrFlowType;
        this.ownerType = org.ownerType;
        this.serviceId = org.serviceId;
        this.signInId = org.signInId;
        this.qrStatus = org.qrStatus;
        this.expiresAt = org.expiresAt;
        this.stateCode = org.stateCode;
        this.orderId = org.orderId;
        this.operationDetail = org.operationDetail;
        this.errorCode = org.errorCode;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("QrOperationEntity [")
                .append("qrKey=").append(this.qrKey).append(", ")
                .append("qrFlowType=").append(this.qrFlowType).append(", ")
                .append("ownerType=").append(this.ownerType).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("signInId=").append(this.signInId).append(", ")
                .append("qrStatus=").append(this.qrStatus).append(", ")
                .append("expiresAt=").append(this.expiresAt).append(", ")
                .append("stateCode=").append(this.stateCode).append(", ")
                .append("orderId=").append(this.orderId).append(", ")
                .append("operationDetail=").append(this.operationDetail).append(", ")
                .append("errorCode=").append(this.errorCode)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.qrKey, 
                this.qrFlowType, 
                this.ownerType, 
                this.serviceId, 
                this.signInId, 
                this.qrStatus, 
                this.expiresAt, 
                this.stateCode, 
                this.orderId, 
                this.operationDetail, 
                this.errorCode
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        QrOperationEntity other = (QrOperationEntity) obj;
        return true
                && Objects.equals(this.qrKey, other.qrKey)
                && Objects.equals(this.qrFlowType, other.qrFlowType)
                && Objects.equals(this.ownerType, other.ownerType)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.qrStatus, other.qrStatus)
                && Objects.equals(this.expiresAt, other.expiresAt)
                && Objects.equals(this.stateCode, other.stateCode)
                && Objects.equals(this.orderId, other.orderId)
                && Objects.equals(this.operationDetail, other.operationDetail)
                && Objects.equals(this.errorCode, other.errorCode)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * QrOperationEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** QRキー. */
        private QrKey qrKey;

        /** QR手続きフロー種別 : sign_in : サインイン
confirmation : 確認フロー
order : 申請フロー. */
        private QrFlowType qrFlowType;

        /** 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者. */
        private OwnerType ownerType;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** サインインID. */
        private SignInId signInId;

        /** QR処理状態 : qr_flow_type = 'sign_in' の場合
- pending : 認証アプリ認証前
- in_processing : 処理中
- authenticated : 認証済み
- completed : トークン発行済
- failure : 処理失敗

qr_flow_type が上記以外
- pending : 手続き待ち
- in_processing : 処理中
- completed : 処理済み
- failure : 処理失敗. */
        private QrStatus qrStatus;

        /** 有効期限. */
        private AppTimeStamp expiresAt;

        /** サインイン時の呼び出し元識別情報 : QR読み込みによるサインインにて、認証アプリによる手続きが完了後、フロントにトークン情報を返却してよいか判断するために利用する。. */
        private String stateCode;

        /** 申請ID. */
        private OrderId orderId;

        /** 操作内容詳細. */
        private OperationDetail operationDetail;

        /** エラーコード. */
        private String errorCode;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the QrOperationEntity object
         */
        public QrOperationEntity build() {
            return new QrOperationEntity(
                    this.qrKey, 
                    this.qrFlowType, 
                    this.ownerType, 
                    this.serviceId, 
                    this.signInId, 
                    this.qrStatus, 
                    this.expiresAt, 
                    this.stateCode, 
                    this.orderId, 
                    this.operationDetail, 
                    this.errorCode
            );
        }

        /**
         * Set qrKey.
         *
         * @param qrKey QRキー
         * @return this builder
         */
        public Builder qrKey(QrKey qrKey) {
            this.qrKey = qrKey;
            return this;
        }

        /**
         * Set qrFlowType.
         *
         * @param qrFlowType QR手続きフロー種別 : sign_in : サインイン
confirmation : 確認フロー
order : 申請フロー
         * @return this builder
         */
        public Builder qrFlowType(QrFlowType qrFlowType) {
            this.qrFlowType = qrFlowType;
            return this;
        }

        /**
         * Set ownerType.
         *
         * @param ownerType 利用元種別 : user : 個人ユーザ/法人ユーザ
service : 銀行/事業者
         * @return this builder
         */
        public Builder ownerType(OwnerType ownerType) {
            this.ownerType = ownerType;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set qrStatus.
         *
         * @param qrStatus QR処理状態 : qr_flow_type = 'sign_in' の場合
- pending : 認証アプリ認証前
- in_processing : 処理中
- authenticated : 認証済み
- completed : トークン発行済
- failure : 処理失敗

qr_flow_type が上記以外
- pending : 手続き待ち
- in_processing : 処理中
- completed : 処理済み
- failure : 処理失敗
         * @return this builder
         */
        public Builder qrStatus(QrStatus qrStatus) {
            this.qrStatus = qrStatus;
            return this;
        }

        /**
         * Set expiresAt.
         *
         * @param expiresAt 有効期限
         * @return this builder
         */
        public Builder expiresAt(AppTimeStamp expiresAt) {
            this.expiresAt = expiresAt;
            return this;
        }

        /**
         * Set stateCode.
         *
         * @param stateCode サインイン時の呼び出し元識別情報 : QR読み込みによるサインインにて、認証アプリによる手続きが完了後、フロントにトークン情報を返却してよいか判断するために利用する。
         * @return this builder
         */
        public Builder stateCode(String stateCode) {
            this.stateCode = stateCode;
            return this;
        }

        /**
         * Set orderId.
         *
         * @param orderId 申請ID
         * @return this builder
         */
        public Builder orderId(OrderId orderId) {
            this.orderId = orderId;
            return this;
        }

        /**
         * Set operationDetail.
         *
         * @param operationDetail 操作内容詳細
         * @return this builder
         */
        public Builder operationDetail(OperationDetail operationDetail) {
            this.operationDetail = operationDetail;
            return this;
        }

        /**
         * Set errorCode.
         *
         * @param errorCode エラーコード
         * @return this builder
         */
        public Builder errorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }
    }
}
// @formatter:on
