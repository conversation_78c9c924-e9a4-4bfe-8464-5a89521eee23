/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcUserFileTaskType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskOrderDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskResultDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.FileTaskStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザファイルタスク : 個人 / 法人ユーザのファイルタスクを扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_file_task")
public class DcUserFileTaskEntity {

    /** タスクID. */
    @Id
    @Column(name = "task_id")
    public final FileTaskId taskId;

    /** タスク種別 : nft_list : NFT一覧ファイル作成
nft_mint : NFT発行
nft_transfer : NFT移転. */
    @Column(name = "task_type")
    public final DcUserFileTaskType taskType;

    /** タスク状態 : initialized : 初回処理待ち
accept : 受付済み
format_failure : フォーマット不備
in_processing : 処理実行中
completed : 処理完了
failed : 処理失敗. */
    @Column(name = "task_status")
    public final FileTaskStatus taskStatus;

    /** 申請日時. */
    @Column(name = "ordered_at")
    public final AppTimeStamp orderedAt;

    /** サインインID. */
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** アップロードファイル名. */
    @Column(name = "file_name")
    public final String fileName;

    /** タスク入力内容詳細. */
    @Column(name = "task_order_detail")
    public final FileTaskOrderDetail taskOrderDetail;

    /** 処理結果保存先バケット. */
    @Column(name = "result_bucket")
    public final String resultBucket;

    /** 処理結果ファイルパス. */
    @Column(name = "result_file_path")
    public final String resultFilePath;

    /** 処理結果詳細. */
    @Column(name = "task_result_detail")
    public final FileTaskResultDetail taskResultDetail;

    /** 処理完了日時. */
    @Column(name = "completed_at")
    public final AppTimeStamp completedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param taskId タスクID
     * @param taskType タスク種別 : nft_list : NFT一覧ファイル作成
nft_mint : NFT発行
nft_transfer : NFT移転
     * @param taskStatus タスク状態 : initialized : 初回処理待ち
accept : 受付済み
format_failure : フォーマット不備
in_processing : 処理実行中
completed : 処理完了
failed : 処理失敗
     * @param orderedAt 申請日時
     * @param signInId サインインID
     * @param fileName アップロードファイル名
     * @param taskOrderDetail タスク入力内容詳細
     * @param resultBucket 処理結果保存先バケット
     * @param resultFilePath 処理結果ファイルパス
     * @param taskResultDetail 処理結果詳細
     * @param completedAt 処理完了日時
     */
    DcUserFileTaskEntity(
        FileTaskId taskId,
        DcUserFileTaskType taskType,
        FileTaskStatus taskStatus,
        AppTimeStamp orderedAt,
        SignInId signInId,
        String fileName,
        FileTaskOrderDetail taskOrderDetail,
        String resultBucket,
        String resultFilePath,
        FileTaskResultDetail taskResultDetail,
        AppTimeStamp completedAt
    ) {
        this.taskId = taskId;
        this.taskType = taskType;
        this.taskStatus = taskStatus;
        this.orderedAt = orderedAt;
        this.signInId = signInId;
        this.fileName = fileName;
        this.taskOrderDetail = taskOrderDetail;
        this.resultBucket = resultBucket;
        this.resultFilePath = resultFilePath;
        this.taskResultDetail = taskResultDetail;
        this.completedAt = completedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserFileTaskEntity オブジェクト
     */
    protected DcUserFileTaskEntity(DcUserFileTaskEntity org) {
        this.taskId = org.taskId;
        this.taskType = org.taskType;
        this.taskStatus = org.taskStatus;
        this.orderedAt = org.orderedAt;
        this.signInId = org.signInId;
        this.fileName = org.fileName;
        this.taskOrderDetail = org.taskOrderDetail;
        this.resultBucket = org.resultBucket;
        this.resultFilePath = org.resultFilePath;
        this.taskResultDetail = org.taskResultDetail;
        this.completedAt = org.completedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserFileTaskEntity [")
                .append("taskId=").append(this.taskId).append(", ")
                .append("taskType=").append(this.taskType).append(", ")
                .append("taskStatus=").append(this.taskStatus).append(", ")
                .append("orderedAt=").append(this.orderedAt).append(", ")
                .append("signInId=").append(this.signInId).append(", ")
                .append("fileName=").append(this.fileName).append(", ")
                .append("taskOrderDetail=").append(this.taskOrderDetail).append(", ")
                .append("resultBucket=").append(this.resultBucket).append(", ")
                .append("resultFilePath=").append(this.resultFilePath).append(", ")
                .append("taskResultDetail=").append(this.taskResultDetail).append(", ")
                .append("completedAt=").append(this.completedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.taskId, 
                this.taskType, 
                this.taskStatus, 
                this.orderedAt, 
                this.signInId, 
                this.fileName, 
                this.taskOrderDetail, 
                this.resultBucket, 
                this.resultFilePath, 
                this.taskResultDetail, 
                this.completedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserFileTaskEntity other = (DcUserFileTaskEntity) obj;
        return true
                && Objects.equals(this.taskId, other.taskId)
                && Objects.equals(this.taskType, other.taskType)
                && Objects.equals(this.taskStatus, other.taskStatus)
                && Objects.equals(this.orderedAt, other.orderedAt)
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.fileName, other.fileName)
                && Objects.equals(this.taskOrderDetail, other.taskOrderDetail)
                && Objects.equals(this.resultBucket, other.resultBucket)
                && Objects.equals(this.resultFilePath, other.resultFilePath)
                && Objects.equals(this.taskResultDetail, other.taskResultDetail)
                && Objects.equals(this.completedAt, other.completedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserFileTaskEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** タスクID. */
        private FileTaskId taskId;

        /** タスク種別 : nft_list : NFT一覧ファイル作成
nft_mint : NFT発行
nft_transfer : NFT移転. */
        private DcUserFileTaskType taskType;

        /** タスク状態 : initialized : 初回処理待ち
accept : 受付済み
format_failure : フォーマット不備
in_processing : 処理実行中
completed : 処理完了
failed : 処理失敗. */
        private FileTaskStatus taskStatus;

        /** 申請日時. */
        private AppTimeStamp orderedAt;

        /** サインインID. */
        private SignInId signInId;

        /** アップロードファイル名. */
        private String fileName;

        /** タスク入力内容詳細. */
        private FileTaskOrderDetail taskOrderDetail;

        /** 処理結果保存先バケット. */
        private String resultBucket;

        /** 処理結果ファイルパス. */
        private String resultFilePath;

        /** 処理結果詳細. */
        private FileTaskResultDetail taskResultDetail;

        /** 処理完了日時. */
        private AppTimeStamp completedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserFileTaskEntity object
         */
        public DcUserFileTaskEntity build() {
            return new DcUserFileTaskEntity(
                    this.taskId, 
                    this.taskType, 
                    this.taskStatus, 
                    this.orderedAt, 
                    this.signInId, 
                    this.fileName, 
                    this.taskOrderDetail, 
                    this.resultBucket, 
                    this.resultFilePath, 
                    this.taskResultDetail, 
                    this.completedAt
            );
        }

        /**
         * Set taskId.
         *
         * @param taskId タスクID
         * @return this builder
         */
        public Builder taskId(FileTaskId taskId) {
            this.taskId = taskId;
            return this;
        }

        /**
         * Set taskType.
         *
         * @param taskType タスク種別 : nft_list : NFT一覧ファイル作成
nft_mint : NFT発行
nft_transfer : NFT移転
         * @return this builder
         */
        public Builder taskType(DcUserFileTaskType taskType) {
            this.taskType = taskType;
            return this;
        }

        /**
         * Set taskStatus.
         *
         * @param taskStatus タスク状態 : initialized : 初回処理待ち
accept : 受付済み
format_failure : フォーマット不備
in_processing : 処理実行中
completed : 処理完了
failed : 処理失敗
         * @return this builder
         */
        public Builder taskStatus(FileTaskStatus taskStatus) {
            this.taskStatus = taskStatus;
            return this;
        }

        /**
         * Set orderedAt.
         *
         * @param orderedAt 申請日時
         * @return this builder
         */
        public Builder orderedAt(AppTimeStamp orderedAt) {
            this.orderedAt = orderedAt;
            return this;
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set fileName.
         *
         * @param fileName アップロードファイル名
         * @return this builder
         */
        public Builder fileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        /**
         * Set taskOrderDetail.
         *
         * @param taskOrderDetail タスク入力内容詳細
         * @return this builder
         */
        public Builder taskOrderDetail(FileTaskOrderDetail taskOrderDetail) {
            this.taskOrderDetail = taskOrderDetail;
            return this;
        }

        /**
         * Set resultBucket.
         *
         * @param resultBucket 処理結果保存先バケット
         * @return this builder
         */
        public Builder resultBucket(String resultBucket) {
            this.resultBucket = resultBucket;
            return this;
        }

        /**
         * Set resultFilePath.
         *
         * @param resultFilePath 処理結果ファイルパス
         * @return this builder
         */
        public Builder resultFilePath(String resultFilePath) {
            this.resultFilePath = resultFilePath;
            return this;
        }

        /**
         * Set taskResultDetail.
         *
         * @param taskResultDetail 処理結果詳細
         * @return this builder
         */
        public Builder taskResultDetail(FileTaskResultDetail taskResultDetail) {
            this.taskResultDetail = taskResultDetail;
            return this;
        }

        /**
         * Set completedAt.
         *
         * @param completedAt 処理完了日時
         * @return this builder
         */
        public Builder completedAt(AppTimeStamp completedAt) {
            this.completedAt = completedAt;
            return this;
        }
    }
}
// @formatter:on
