/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BurnStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * 償却履歴 : DCJPY 発行 / 償却の処理の状態を管理する。.
 */
@Entity(immutable = true)
@Table(name = "burn_transaction")
public class BurnTransactionEntity {

    /** リクエストID. */
    @Id
    @Column(name = "request_id")
    public final RequestId requestId;

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** burn取引状態 : initialize : 処理開始前
burned : DC口座からDCJPY償却済み
burn_failed : DC口座からDCJPY償却ができなかった
deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗
completed : DCJPY発行/償却手続きが正常に完了. */
    @Column(name = "burn_status")
    public final BurnStatus burnStatus;

    /** 金額. */
    @Column(name = "amount")
    public final Amount amount;

    /** 初期登録日時. */
    @Column(name = "initialized_at")
    public final AppTimeStamp initializedAt;

    /** CoreAPI処理結果日時. */
    @Column(name = "coreapi_resulted_at")
    public final AppTimeStamp coreapiResultedAt;

    /** BankGW処理結果日時. */
    @Column(name = "bankgw_resulted_at")
    public final AppTimeStamp bankgwResultedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param requestId リクエストID
     * @param dcBankNumber DC口座番号
     * @param burnStatus burn取引状態 : initialize : 処理開始前
burned : DC口座からDCJPY償却済み
burn_failed : DC口座からDCJPY償却ができなかった
deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗
completed : DCJPY発行/償却手続きが正常に完了
     * @param amount 金額
     * @param initializedAt 初期登録日時
     * @param coreapiResultedAt CoreAPI処理結果日時
     * @param bankgwResultedAt BankGW処理結果日時
     */
    BurnTransactionEntity(
        RequestId requestId,
        DcBankNumber dcBankNumber,
        BurnStatus burnStatus,
        Amount amount,
        AppTimeStamp initializedAt,
        AppTimeStamp coreapiResultedAt,
        AppTimeStamp bankgwResultedAt
    ) {
        this.requestId = requestId;
        this.dcBankNumber = dcBankNumber;
        this.burnStatus = burnStatus;
        this.amount = amount;
        this.initializedAt = initializedAt;
        this.coreapiResultedAt = coreapiResultedAt;
        this.bankgwResultedAt = bankgwResultedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org BurnTransactionEntity オブジェクト
     */
    protected BurnTransactionEntity(BurnTransactionEntity org) {
        this.requestId = org.requestId;
        this.dcBankNumber = org.dcBankNumber;
        this.burnStatus = org.burnStatus;
        this.amount = org.amount;
        this.initializedAt = org.initializedAt;
        this.coreapiResultedAt = org.coreapiResultedAt;
        this.bankgwResultedAt = org.bankgwResultedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("BurnTransactionEntity [")
                .append("requestId=").append(this.requestId).append(", ")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("burnStatus=").append(this.burnStatus).append(", ")
                .append("amount=").append(this.amount).append(", ")
                .append("initializedAt=").append(this.initializedAt).append(", ")
                .append("coreapiResultedAt=").append(this.coreapiResultedAt).append(", ")
                .append("bankgwResultedAt=").append(this.bankgwResultedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.requestId, 
                this.dcBankNumber, 
                this.burnStatus, 
                this.amount, 
                this.initializedAt, 
                this.coreapiResultedAt, 
                this.bankgwResultedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        BurnTransactionEntity other = (BurnTransactionEntity) obj;
        return true
                && Objects.equals(this.requestId, other.requestId)
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.burnStatus, other.burnStatus)
                && Objects.equals(this.amount, other.amount)
                && Objects.equals(this.initializedAt, other.initializedAt)
                && Objects.equals(this.coreapiResultedAt, other.coreapiResultedAt)
                && Objects.equals(this.bankgwResultedAt, other.bankgwResultedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * BurnTransactionEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** リクエストID. */
        private RequestId requestId;

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** burn取引状態 : initialize : 処理開始前
burned : DC口座からDCJPY償却済み
burn_failed : DC口座からDCJPY償却ができなかった
deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗
completed : DCJPY発行/償却手続きが正常に完了. */
        private BurnStatus burnStatus;

        /** 金額. */
        private Amount amount;

        /** 初期登録日時. */
        private AppTimeStamp initializedAt;

        /** CoreAPI処理結果日時. */
        private AppTimeStamp coreapiResultedAt;

        /** BankGW処理結果日時. */
        private AppTimeStamp bankgwResultedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the BurnTransactionEntity object
         */
        public BurnTransactionEntity build() {
            return new BurnTransactionEntity(
                    this.requestId, 
                    this.dcBankNumber, 
                    this.burnStatus, 
                    this.amount, 
                    this.initializedAt, 
                    this.coreapiResultedAt, 
                    this.bankgwResultedAt
            );
        }

        /**
         * Set requestId.
         *
         * @param requestId リクエストID
         * @return this builder
         */
        public Builder requestId(RequestId requestId) {
            this.requestId = requestId;
            return this;
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set burnStatus.
         *
         * @param burnStatus burn取引状態 : initialize : 処理開始前
burned : DC口座からDCJPY償却済み
burn_failed : DC口座からDCJPY償却ができなかった
deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗
completed : DCJPY発行/償却手続きが正常に完了
         * @return this builder
         */
        public Builder burnStatus(BurnStatus burnStatus) {
            this.burnStatus = burnStatus;
            return this;
        }

        /**
         * Set amount.
         *
         * @param amount 金額
         * @return this builder
         */
        public Builder amount(Amount amount) {
            this.amount = amount;
            return this;
        }

        /**
         * Set initializedAt.
         *
         * @param initializedAt 初期登録日時
         * @return this builder
         */
        public Builder initializedAt(AppTimeStamp initializedAt) {
            this.initializedAt = initializedAt;
            return this;
        }

        /**
         * Set coreapiResultedAt.
         *
         * @param coreapiResultedAt CoreAPI処理結果日時
         * @return this builder
         */
        public Builder coreapiResultedAt(AppTimeStamp coreapiResultedAt) {
            this.coreapiResultedAt = coreapiResultedAt;
            return this;
        }

        /**
         * Set bankgwResultedAt.
         *
         * @param bankgwResultedAt BankGW処理結果日時
         * @return this builder
         */
        public Builder bankgwResultedAt(AppTimeStamp bankgwResultedAt) {
            this.bankgwResultedAt = bankgwResultedAt;
            return this;
        }
    }
}
// @formatter:on
