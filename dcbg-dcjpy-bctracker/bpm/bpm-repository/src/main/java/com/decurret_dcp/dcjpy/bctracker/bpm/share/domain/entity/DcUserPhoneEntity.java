/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ電話番号 : 個人ユーザのアプリ、もしくは法人ユーザが認証アプリで利用する電話番号を扱う。
サービスで認証アプリ利用がない場合、もしくは初期設定待ちの場合はレコードが存在しない。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_phone")
public class DcUserPhoneEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 電話番号. */
    @Column(name = "phone_number")
    public final String phoneNumber;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param phoneNumber 電話番号
     */
    DcUserPhoneEntity(
        SignInId signInId,
        String phoneNumber
    ) {
        this.signInId = signInId;
        this.phoneNumber = phoneNumber;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserPhoneEntity オブジェクト
     */
    protected DcUserPhoneEntity(DcUserPhoneEntity org) {
        this.signInId = org.signInId;
        this.phoneNumber = org.phoneNumber;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserPhoneEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("phoneNumber=").append(this.phoneNumber)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.phoneNumber
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserPhoneEntity other = (DcUserPhoneEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.phoneNumber, other.phoneNumber)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserPhoneEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 電話番号. */
        private String phoneNumber;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserPhoneEntity object
         */
        public DcUserPhoneEntity build() {
            return new DcUserPhoneEntity(
                    this.signInId, 
                    this.phoneNumber
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set phoneNumber.
         *
         * @param phoneNumber 電話番号
         * @return this builder
         */
        public Builder phoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
            return this;
        }
    }
}
// @formatter:on
