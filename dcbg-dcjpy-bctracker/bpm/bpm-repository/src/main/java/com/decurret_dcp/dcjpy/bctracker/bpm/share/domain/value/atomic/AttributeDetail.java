package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.atomic;

import org.seasar.doma.Domain;

import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.attribute.AttributeDetailContent;
import com.fasterxml.jackson.core.type.TypeReference;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class AttributeDetail {

    private final String jsonString;

    private AttributeDetail(String jsonString) {
        this.jsonString = jsonString;
    }

    public static AttributeDetail of(String jsonString) {
        return new AttributeDetail(jsonString);
    }

    public static AttributeDetail of(Object jsonValue) {
        String jsonString = JsonConverter.toStringValue(jsonValue);
        return new AttributeDetail(jsonString);
    }

    public <TYPE extends AttributeDetailContent> TYPE getContent(TypeReference<TYPE> typeReference) {
        return JsonConverter.toJsonValue(this.jsonString, typeReference);
    }

    @Deprecated
    public String getValue() {
        return this.jsonString;
    }
}
