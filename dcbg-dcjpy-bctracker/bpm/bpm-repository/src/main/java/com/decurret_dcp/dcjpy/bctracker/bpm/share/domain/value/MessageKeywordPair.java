package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class MessageKeywordPair {

    public final Keyword keyword;

    public final String value;

    public static enum Keyword {

        /** 依頼番号 (申請ID). */
        ORDER_ID("order_id"),

        /** 業務担当者 (申請者名). */
        ORDER_USER_NAME("order_user_name"),

        /** 依頼内容 (申請種別). */
        ORDER_TYPE("order_type"),

        /** 処理完了日時。 */
        COMPLETED_AT("completed_at"),

        /** 操作額。 */
        AMOUNT("amount"),

        /** 残高。 */
        BALANCE("balance"),

        /** 発行限度額。 */
        MINT_LIMIT("mint_limit"),

        /** 償却限度額。 */
        BURN_LIMIT("burn_limit"),

        /** 移転限度額。 */
        TRANSFER_LIMIT("transfer_limit"),

        /** チャージおよびディスチャージ限度額。 */
        CHARGE_LIMIT("charge_limit"),

        /** 1日あたりの合計累積限度額。 */
        CUMULATIVE_LIMIT("cumulative_limit"),

        /** 問い合わせ先のメールアドレス */
        CONTACT_EMAIL_ADDRESS("contact_email_address"),

        /** 移転日時 */
        TRANSFER_AT("transfer_at"),

        /** 処理日時。 */
        OPERATED_AT("operated_at"),

        /** ゾーン名 */
        ZONE_NAME("zone_name");

        private final String value;

        private Keyword(String value) {
            this.value = value;
        }

        public String parameter() {
            return "${" + this.value + "}";
        }

        public MessageKeywordPair with(String value) {
            return new MessageKeywordPair(this, value);
        }
    }
}
