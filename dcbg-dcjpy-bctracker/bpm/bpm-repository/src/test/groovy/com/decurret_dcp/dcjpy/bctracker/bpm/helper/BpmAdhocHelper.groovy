package com.decurret_dcp.dcjpy.bctracker.bpm.helper

import groovy.sql.Sql
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper

class BpmAdhocHelper {

    private static final int DB_POOL_CONNECTION_IDLE_SIZE = 2

    private static final ObjectMapper mapper = new ObjectMapper()

    private static dbPort

    private static localStackPort

    private static DockerComposeContainer composeContainer

    private static connectionCount = 0

    static Sql sql

    static String getDbPort() { return dbPort }

    static String getLocalStackPort() { return localStackPort }

    private static void startContainer() {
        composeContainer = new DockerComposeContainer(new File("bpm/docker-compose_cicd.yml"))
                .withExposedService("db", 5432)
                .withExposedService("localstack", 4566)
                .waitingFor("db", Wait.forListeningPort())
                .waitingFor("localstack", Wait.forListeningPort())
                .waitingFor("db", Wait.forLogMessage(".*database system is ready to accept connections.*\\n", 1))

        composeContainer.start()
        dbPort = String.valueOf(composeContainer.getServicePort("db", 5432))
        localStackPort = String.valueOf(composeContainer.getServicePort("localstack", 4566))
    }

    static Sql initAdhoc(DynamicPropertyRegistry registry) {
        var jdbcUrl = "***************************:${dbPort}/bpm_db"
        registry.add("spring.datasource.url", () -> jdbcUrl)

        // テストではコネクションを多く用意する必要はないので、少なめにする
        registry.add("spring.datasource.hikari.minimum-idle", () -> DB_POOL_CONNECTION_IDLE_SIZE)

        sql = Sql.newInstance(jdbcUrl, "bpm_user", "bpm_password", "org.postgresql.Driver")

        sql.execute("DROP SCHEMA IF EXISTS public CASCADE")
        sql.execute("CREATE SCHEMA public")

        // DDL およびマスタデータの投入
        sql.execute(new File("./bpm/bctracker-bpm-local-env/bpm-db/V001_bpm-server.sql").text)
        sql.execute(new File("./bpm/bctracker-bpm-local-env/bpm-db/V101_init_master-data.sql").text)
        sql.execute(new File("./bpm/bctracker-bpm-local-env/bpm-db/V201_init_functions.sql").text)
        // テスト用データの投入
        sql.execute(new File("./bpm/bctracker-bpm-local-env/bpm-db/V901_init_test-data.sql").text)

        return sql
    }

    static void cleanupSpec() {
        sql.close()

        // テストクラスごとにSpringBootを起動しているにも関わらず、
        // なぜか以前のテストケースでのDBコネクションが残ったままなので、
        // デフォルトのDBコネクションプールのサイズを超えたら、コンテナを再起動する
        connectionCount += DB_POOL_CONNECTION_IDLE_SIZE
        if (connectionCount >= 100) {
            composeContainer.stop()
            connectionCount = 0

            startContainer()
        }
    }
}
