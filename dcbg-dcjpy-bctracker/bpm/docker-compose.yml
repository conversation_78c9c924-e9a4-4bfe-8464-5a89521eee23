version: '3.8'
services:
  db:
    image: postgres:11.10
    ports:
      - "15432:5432"
    environment:
      POSTGRES_USER: bpm_user
      POSTGRES_PASSWORD: bpm_password
      POSTGRES_DB: bpm_db
    volumes:
      - ./bctracker-bpm-local-env/bpm-db:/docker-entrypoint-initdb.d

  localstack:
    image: localstack/localstack:3.0.2
    environment:
      - SERVICES=sqs,sns,ses
    ports:
      - "14566-14599:4566-4599"
    volumes:
      - ./bctracker-bpm-local-env/localstack/init-localstack.sh:/etc/localstack/init/ready.d/init-aws.sh
