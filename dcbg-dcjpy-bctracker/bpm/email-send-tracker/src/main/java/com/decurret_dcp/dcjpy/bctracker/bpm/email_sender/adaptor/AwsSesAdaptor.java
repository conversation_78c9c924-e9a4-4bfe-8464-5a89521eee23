package com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.adaptor;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.config.EMailSendTrackerProperty;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.service.EMailSender;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.service.command.TransferSenderCommand;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.Body;
import software.amazon.awssdk.services.ses.model.Content;
import software.amazon.awssdk.services.ses.model.Destination;
import software.amazon.awssdk.services.ses.model.Message;
import software.amazon.awssdk.services.ses.model.SendEmailRequest;

@RequiredArgsConstructor
@Component
@Slf4j
public class AwsSesAdaptor implements EMailSender {

    private final SesClient sesClient;

    private final EMailSendTrackerProperty property;

    @Override
    public void sendMessage(TransferSenderCommand command) {

        Content subjectContent = Content.builder()
                .data(command.subject)
                .build();

        Content textBodyContent = Content.builder()
                .data(command.message)
                .build();

        Body body = Body.builder()
                .text(textBodyContent)
                .build();

        Message message = Message.builder()
                .subject(subjectContent)
                .body(body)
                .build();

        Destination destination = Destination.builder()
                .toAddresses(command.sendEmailAddress)
                .build();

        SendEmailRequest sendEmailRequest = SendEmailRequest.builder()
                .destination(destination)
                .message(message)
                .source(property.sourceEmailAddress)
                .build();

        this.sesClient.sendEmail(sendEmailRequest);
    }
}
