package com.decurret_dcp.dcjpy.bctracker.bpm.email_sender;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import com.decurret_dcp.dcjpy.bctracker.base.config.BCTrackerBaseSqsProperty;
import com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.config.EMailSendTrackerProperty;

/**
 * 取引結果Eメール通知 (BPM)
 */
@SpringBootApplication
@ComponentScan("com.decurret_dcp.dcjpy.bctracker")
@EnableConfigurationProperties({ BCTrackerBaseSqsProperty.class, EMailSendTrackerProperty.class })
public class EmailSenderTrackerMain {

    public static void main(String[] args) {
        SpringApplication.run(EmailSenderTrackerMain.class, args);
    }
}
