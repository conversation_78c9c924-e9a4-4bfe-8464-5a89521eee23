package com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.domain.value;

import java.time.format.DateTimeFormatter;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class EmailSenderMessage {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("uuuu/MM/dd HH:mm");

    public final ValidatorId fromValidatorId;

    public final AccountId accountId;

    public final ZoneId zoneId;

    public final TransactionHash transactionHash;

    public final String transferAt;

    public static EmailSenderMessage create(BCEventTypeHolder event) {
        return switch (event.eventType()) {
            case TRANSFER -> createEmailSenderMessage((TransferEvent) event);
            default -> null;
        };
    }

    private static EmailSenderMessage createEmailSenderMessage(TransferEvent event) {
        if (isCreateFromTransfer(event) == false) {
            return null;
        }

        return EmailSenderMessage.builder()
                .fromValidatorId(event.fromValidatorId)
                .accountId(event.fromAccountId)
                .zoneId(event.zoneId)
                .transactionHash(event.transactionHash)
                .transferAt(event.blockTimeStamp.format(DATE_TIME_FORMATTER))
                .build();
    }

    public ValidatorId validatorId() {
        return this.fromValidatorId;
    }

    public String subject() {
        return "振込依頼（受付）のご連絡";
    }

    private static boolean isCreateFromTransfer(TransferEvent event) {
        if ((event.fromAccountId.isEscrowAccount() == false) && (event.toAccountId.isEscrowAccount() == false)) {
            // 移転がこれに該当
            return true;
        }

        // 上記以外はfalse
        return false;
    }
}
