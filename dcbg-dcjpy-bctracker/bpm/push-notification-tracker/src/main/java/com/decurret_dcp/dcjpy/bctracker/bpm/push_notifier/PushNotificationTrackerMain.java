package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import com.decurret_dcp.dcjpy.bctracker.base.config.BCTrackerBaseSqsProperty;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.config.PushNotificationTrackerProperty;

@SpringBootApplication
@ComponentScan("com.decurret_dcp.dcjpy.bctracker")
@EnableConfigurationProperties({ BCTrackerBaseSqsProperty.class, PushNotificationTrackerProperty.class })
public class PushNotificationTrackerMain {

    public static void main(String[] args) {
        SpringApplication.run(PushNotificationTrackerMain.class, args);
    }
}
