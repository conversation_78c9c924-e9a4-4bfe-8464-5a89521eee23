package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneStatusEvent;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class SyncBusinessZoneStatusMessage implements PushNotificationMessage {

    private static final Set<AccountStatus> ACCEPTABLE_STATUSES =
            Set.of(AccountStatus.APPLYING, AccountStatus.TERMINATING);

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ZoneId businessZoneId;

    public final String businessZoneName;

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final AccountStatus accountStatus;

    public static SyncBusinessZoneStatusMessage create(SyncBusinessZoneStatusEvent event) {
        if (ACCEPTABLE_STATUSES.contains(event.accountStatus) == false) {
            throw new IllegalArgumentException(
                    "AccountStatus must either terminating or applying. status: " + event.accountStatus.getValue());
        }

        return SyncBusinessZoneStatusMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimeStamp(event.blockTimeStamp)
                .businessZoneId(event.businessZoneId)
                .businessZoneName(event.businessZoneName)
                .validatorId(event.validatorId)
                .accountId(event.accountId)
                .accountStatus(event.accountStatus)
                .build();
    }

    @Override
    public TransactionHash transactionHash() {
        return this.transactionHash;
    }

    @Override
    public AppTimeStamp blockTimestamp() {
        return this.blockTimeStamp;
    }

    @Override
    public AccountId accountId() {
        return this.accountId;
    }

    @Override
    public ValidatorId validatorId() {
        return this.validatorId;
    }

    @Override
    public TemplateKey messageKey() {
        return switch (this.accountStatus) {
            case APPLYING -> TemplateKey.BIZ_APPLYING;
            case TERMINATING -> TemplateKey.BIZ_TERMINATING;
            default -> throw new IllegalArgumentException("AccountStatus must either terminating or applying");
        };
    }

    @Override
    public List<MessageKeywordPair> keywordPairs() {
        return List.of(
                MessageKeywordPair.Keyword.ZONE_NAME.with(this.businessZoneName),
                MessageKeywordPair.Keyword.OPERATED_AT.with(this.blockTimestamp().format(DATE_FORMATTER))
        );
    }

    @Override
    public NotificationType notificationType() {
        return switch (this.accountStatus) {
            case APPLYING -> NotificationType.SYNCHRONOUS;
            case TERMINATING -> NotificationType.BIZ_TERMINATING;
            default -> throw new IllegalArgumentException("AccountStatus must either terminating or applying");
        };
    }

    @Override
    public NotificationDetail notificationDetail() {
        return NotificationDetail.of(
                Map.ofEntries(
                        Map.entry("transaction_hash", this.transactionHash().getValue()),
                        Map.entry("zone_id", this.businessZoneId.getValue())
                )
        );
    }
}
