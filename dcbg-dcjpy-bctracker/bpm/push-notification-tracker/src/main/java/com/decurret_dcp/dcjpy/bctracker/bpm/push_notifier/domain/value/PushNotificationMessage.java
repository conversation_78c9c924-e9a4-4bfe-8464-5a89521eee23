package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value;

import java.time.format.DateTimeFormatter;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ModTokenLimitEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneStatusEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

public interface PushNotificationMessage {

    static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("uuuu.MM.dd HH:mm");

    public TransactionHash transactionHash();

    public AppTimeStamp blockTimestamp();

    public AccountId accountId();

    public ValidatorId validatorId();

    public TemplateKey messageKey();

    public List<MessageKeywordPair> keywordPairs();

    public NotificationType notificationType();

    public NotificationDetail notificationDetail();

    public static PushNotificationMessage create(BCEventTypeHolder event) {
        if (event == null) {
            return null;
        }

        return switch (event.eventType()) {
            case TRANSFER -> TransferCompletedMessage.create((TransferEvent) event);
            case MOD_TOKEN_LIMIT -> AccountLimitUpdatedMessage.create((ModTokenLimitEvent) event);
            case SYNC_BUSINESS_ZONE_STATUS -> SyncBusinessZoneStatusMessage.create((SyncBusinessZoneStatusEvent) event);
            default -> null;
        };
    }
}
