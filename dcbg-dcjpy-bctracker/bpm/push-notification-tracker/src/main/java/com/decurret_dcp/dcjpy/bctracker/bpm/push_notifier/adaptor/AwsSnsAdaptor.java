package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.adaptor;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.service.MessageNotifier;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.service.command.PushNotificationCommand;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;
import software.amazon.awssdk.services.sns.model.SnsException;

@RequiredArgsConstructor
@Component
@Slf4j
public class AwsSnsAdaptor implements MessageNotifier {

    private final SnsClient snsClient;

    @Override
    public void publish(PushNotificationCommand command) {

        DcUserDeviceEntity userDevice = command.device;
        if (userDevice == null) {
            log.warn("Unable to send message to device for no userDevice. signInId : {}, messageId : {}",
                     command.signInId.getValue(), command.notificationId.getValue());
            return;
        }

        String message = command.message.replace("\n", "\\\\n");
        String multiPlatformMessage = """
                {
                    "default": "%1$s",
                    "APNS": "{\\"aps\\": {\\"alert\\": {\\"body\\": \\"%1$s\\"}}}",
                    "GCM": "{\\"notification\\": {\\"body\\": \\"%1$s\\"}}"
                }""".formatted(message);

        PublishRequest request = PublishRequest.builder()
                .message(multiPlatformMessage)
                .messageStructure("json")
                .targetArn(userDevice.pushToken)
                .build();
        try {
            PublishResponse response = this.snsClient.publish(request);
            log.info("Published message. notificationId = {}, snsMessageId = {}",
                     command.notificationId, response.messageId());
        } catch (SnsException exception) {
            log.warn("Failed to publish message. notificationId = {}, pushToken = {}, detail = {}",
                     command.notificationId, userDevice.pushToken, exception.getMessage());
        }
    }
}
