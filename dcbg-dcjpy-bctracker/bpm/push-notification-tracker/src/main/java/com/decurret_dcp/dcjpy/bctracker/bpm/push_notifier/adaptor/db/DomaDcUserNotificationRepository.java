package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.adaptor.db;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.bctracker.base.domain.SleepUtil;
import com.decurret_dcp.dcjpy.bctracker.base.domain.UUIDv7;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.repository.DcUserNotificationRepository;
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.PushNotificationMessage;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao.DcUserDeviceDao;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.adaptor.db.dao.DcUserNotificationDao;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserNotificationEntity;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaDcUserNotificationRepository implements DcUserNotificationRepository {

    private final DcUserNotificationDao dcUserNotificationDao;

    private final DcUserDeviceDao dcUserDeviceDao;

    @Override
    public DcUserNotificationEntity save(PushNotificationMessage event, SignInId signInId, String message) {
        NotificationId notificationId = this.initNotificationId();
        NotificationDetail notificationDetail = event.notificationDetail();

        DcUserNotificationEntity entity = DcUserNotificationEntity.builder()
                .notificationId(notificationId)
                .signInId(signInId)
                .notificationType(event.notificationType())
                .message(message)
                .notificationDetail(notificationDetail)
                .publishedAt(event.blockTimestamp())
                .build();
        this.dcUserNotificationDao.insert(entity);

        return entity;
    }

    /**
     * 通知IDを生成する.
     *
     * @return 通知ID.
     */
    private NotificationId initNotificationId() {
        for (int index = 0; index < 3; index++) {
            NotificationId notificationId = NotificationId.of(UUIDv7.create());
            boolean alreadyExists = this.dcUserNotificationDao.exists(notificationId);
            if (!alreadyExists) {
                return notificationId;
            }

            SleepUtil.sleepSilently(100L);
        }

        throw new IllegalStateException("Failed to generate notificationId.");
    }

    @Override
    public DcUserDeviceEntity findDevice(SignInId signInId) {
        return this.dcUserDeviceDao.selectById(signInId);
    }
}
