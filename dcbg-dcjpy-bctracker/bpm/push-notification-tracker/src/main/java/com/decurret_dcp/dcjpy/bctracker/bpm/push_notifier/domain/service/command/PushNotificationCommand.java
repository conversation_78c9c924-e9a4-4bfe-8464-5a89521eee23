package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.service.command;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity.DcUserDeviceEntity;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class PushNotificationCommand {

    public final NotificationId notificationId;

    public final SignInId signInId;

    public final String message;

    public final DcUserDeviceEntity device;
}
