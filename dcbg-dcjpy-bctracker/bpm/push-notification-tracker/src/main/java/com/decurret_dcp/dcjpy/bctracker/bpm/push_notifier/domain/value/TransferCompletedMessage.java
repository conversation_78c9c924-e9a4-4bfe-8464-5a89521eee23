package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value;

import java.util.List;
import java.util.Map;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationDetail;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.NotificationType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent;
import com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.value.MessageKeywordPair;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class TransferCompletedMessage implements PushNotificationMessage {

    /** トランザクションハッシュ. */
    public final TransactionHash transactionHash;

    public final ValidatorId fromValidatorId;

    /** アカウントID. */
    public final AccountId accountId;

    /** 取引日時. */
    public final AppTimeStamp transactedAt;

    /** 取引種別. */
    public final TransactionType transactionType;

    /** 取引額. */
    public final Amount amount;

    /** 取引後残高. */
    public final Balance balance;

    static TransferCompletedMessage create(TransferEvent event) {
        // チャージもしくはディスチャージの場合は通知は不要
        if (event.transferType.isTransfer() == false) {
            return null;
        }

        return TransferCompletedMessage.builder()
                .transactionHash(event.transactionHash)
                .fromValidatorId(event.fromValidatorId)
                .accountId(event.fromAccountId)
                .transactedAt(event.blockTimeStamp)
                .transactionType(TransactionType.TRANSFER)
                .amount(event.amount)
                .balance(event.fromAccountBalance)
                .build();
    }

    @Override
    public TransactionHash transactionHash() {
        return this.transactionHash;
    }

    @Override
    public AppTimeStamp blockTimestamp() {
        return this.transactedAt;
    }

    @Override
    public AccountId accountId() {
        return this.accountId;
    }

    @Override
    public ValidatorId validatorId() {
        return this.fromValidatorId;
    }

    @Override
    public TemplateKey messageKey() {
        return TemplateKey.TRANSFER;
    }

    @Override
    public List<MessageKeywordPair> keywordPairs() {
        return List.of(
                MessageKeywordPair.Keyword.COMPLETED_AT.with(
                        this.transactedAt.zonedDateTime().format(DATE_FORMATTER))
        );
    }

    @Override
    public NotificationType notificationType() {
        return NotificationType.TRANSFER;
    }

    @Override
    public NotificationDetail notificationDetail() {
        return NotificationDetail.of(
                Map.ofEntries(
                        Map.entry("transaction_hash", this.transactionHash().getValue())
                )
        );
    }
}
