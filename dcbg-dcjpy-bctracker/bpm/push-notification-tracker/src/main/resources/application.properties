# SpringBoot のバナーを表示しないようにする
spring.main.banner-mode=off

# ログのフォーマット
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(\\(%32.32X{traceId}\\)){magenta} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx

##########################################################################
# DB Connection
##########################################################################
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=jdbc:postgresql://${DB_BASE:localhost}:${DB_PORT:15432}/${DB_NAME:bpm_db}
spring.datasource.username=${DB_USER:bpm_user}
spring.datasource.password=${DB_PASS:bpm_password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.maximum-pool-size=${DB_MAXIMUM_POOL_SIZE:50}
spring.datasource.hikari.minimum-idle=${DB_MINIMUM_IDLE:10}
# プール内のコネクションが DB に接続するタイムアウト時間 (ミリ秒)
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
# プール内のアイドル状態のコネクションのタイムアウト時間 (ミリ秒)
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}

##########################################################################
# JACKSON
##########################################################################
# JSON のパラメータ名を snake_case として扱う
spring.jackson.property-naming-strategy=SNAKE_CASE
# JSON の日時形式を標準形式として扱う
spring.jackson.serialization.write-dates-as-timestamps=false

##########################################################################
# BCTracker 共通機能 (SQS)
##########################################################################
# 扱うイベントが格納されるキュー名。
bctracker.base.sqs.queue-name=${PUSH_NOTIFICATION_SQS_QUEUE_NAME:dcjpy_bctracker_queue_push-notification.fifo}
# 可視性タイムアウト時間 (秒)
bctracker.base.sqs.visibility-timeout=${BASE_VISIBILITY_TIMEOUT:5}
# ポーリングメッセージ待機時間 (秒)
bctracker.base.sqs.wait-time-seconds=${BASE_WAIT_TIME_SECONDS:1}
# ローカル環境への接続先
bctracker.base.sqs.local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:14566}

##########################################################################
# BCTracker Push 通知
##########################################################################
# SNS のローカル環境への接続先
bctracker.push-notification.sns-local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:14566}
