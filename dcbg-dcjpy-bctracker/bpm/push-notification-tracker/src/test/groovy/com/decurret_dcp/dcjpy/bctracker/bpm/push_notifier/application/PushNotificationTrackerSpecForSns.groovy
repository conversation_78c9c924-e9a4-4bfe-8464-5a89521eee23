package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ModTokenLimitEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.SyncBusinessZoneStatusEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.repository.DcUserNotificationRepository
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.PushNotificationMessage
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.BpmAdhocHelper
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.BpmAdhocHttpHelper
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.UniCodeHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.apache.http.client.HttpClient
import org.apache.http.impl.client.HttpClientBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.*
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class PushNotificationTrackerSpecForSns extends Specification {

    static final HttpClient httpClient = HttpClientBuilder.create().build()

    private static final String PUSH_SERVICE_TYPE_APNS = "APNS"
    private static final String PUSH_SERVICE_TYPE_GCM = "GCM"

    @Autowired
    PushNotificationTracker pushNotificationTracker

    @Autowired
    DcUserNotificationRepository dcUserNotificationRepository

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    BpmAdhocHttpHelper snsHttpHelper

    SnsClient snsClient = SnsClient.builder()
            .region(Region.AP_NORTHEAST_1)
            .endpointOverride(URI.create("http://localhost:" + BpmAdhocHelper.getLocalStackPort()))
            .build()

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = BpmAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        BpmAdhocHelper.cleanupSpec()
    }

    def setup() {
        snsHttpHelper = new BpmAdhocHttpHelper(BpmAdhocHelper.getLocalStackPort().toInteger())
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * TransferイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのindexValuesの値
     */
    private static JsonNode getTransferIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * TransferイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTransferNonIndexValues() {
        String nonIndexValues = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * SyncBusinessZoneStatusイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return SyncBusinessZoneStatusイベントのindexValuesの値
     */
    private static JsonNode getSyncBusinessZoneStatusIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * SyncBusinessZoneStatus(BizZone口座開設)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getApplyingNonIndexValues() {
        String nonIndexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId": "3001",
                "zoneName": "モックギンコウコウザ1",
                "accountStatus": [97,112,112,108,121,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * SyncBusinessZoneStatus(BizZone解約)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTerminatingNonIndexValues() {
        String nonIndexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId": "3001",
                "zoneName": "モックギンコウコウザ1",
                "accountStatus": [116,101,114,109,105,110,97,116,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * ModTokenLimitイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのindexValuesの値
     */
    private static JsonNode getModTokenLimitIndexValues() {
        String indexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * ModTokenLimitイベントのNonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのNonIndexValuesの値
     */
    private static JsonNode getModTokenLimitNonIndexValues() {
        String nonIndexValues = """
            {
                "itemFlgs": [true, true, true, true, true],
                "limitAmounts": [100000, 110000, 120000, 130000, 140000],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * テスト用に、SNSの設定を行います。
     * @param osType OSの種類
     * @param pushServiceType GCM or APNS
     *
     * @return 取得したendpointArn
     */
    private String setupSnS(String osType, String pushServiceType) {
        CreatePlatformApplicationRequest applicationRequest = CreatePlatformApplicationRequest.builder()
                .name("test")
                .platform(pushServiceType)
                .attributes(Map.of("PlatformPrincipal", "test", "PlatformCredential", "test"))
                .build() as CreatePlatformApplicationRequest
        CreatePlatformApplicationResponse applicationResponse = snsClient.createPlatformApplication(applicationRequest)
        CreatePlatformEndpointRequest endpointRequest = CreatePlatformEndpointRequest.builder()
                .platformApplicationArn(applicationResponse.platformApplicationArn())
                .token("test")
                .build() as CreatePlatformEndpointRequest
        CreatePlatformEndpointResponse endpointResponse = snsClient.createPlatformEndpoint(endpointRequest)
        String endpointArn = endpointResponse.endpointArn()

        sql.execute("""
            UPDATE dc_user_device SET push_token = '${endpointArn}', os_type = '${osType}' WHERE sign_in_id = 'SID01AA-1001';
        """)
        return endpointArn
    }

    /**
     * テスト用に作成したエンドポイントを削除します。
     * @param endpointArn 削除するエンドポイントのArn
     */
    private void cleanupSns(String endpointArn) {
        DeleteEndpointRequest deleteEndpointRequest = DeleteEndpointRequest.builder().endpointArn(endpointArn).build() as DeleteEndpointRequest
        snsClient.deleteEndpoint(deleteEndpointRequest)
    }

    /**
     * テスト用のSNSからメッセージを取得します。
     * @param endpointArn
     * @param pushServiceType GCM or APNS
     * @return
     */
    private String getSnsMessage(String endpointArn, String pushServiceType) {
        def snsPath = "/_aws/sns/platform-endpoint-messages"
        def snsRequest = snsHttpHelper.httpGet(snsPath).addQuery("region", "ap-northeast-1").build()
        def snsResponse = httpClient.execute(snsRequest)
        def snsResponseBody = snsHttpHelper.toJson(snsResponse)
        String tmp = snsResponseBody.get("platform_endpoint_messages").get(endpointArn).get(0).get("Message").textValue().replace("\\\"", "").split("body:")[1]
        // \\nを\nに置換する
        int substrEndWith = 4 // APNS
        if (PUSH_SERVICE_TYPE_GCM.equals(pushServiceType)) {
            substrEndWith = 3 // GCM
        }
        String actualMessage = UniCodeHelper.escape(tmp.substring(0, tmp.length() - substrEndWith)).replace("\\\\n", "\n").trim()
        return actualMessage
    }

    def "testOnMessage_transferイベントが発生した場合 #pushServiceType "() {
        setup:
        def endpointArn = setupSnS("android", "${pushServiceType}")
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("transfer")
        record.get(0).get("message").equals("DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
            PUSH_SERVICE_TYPE_APNS
            ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessage_transferイベントが発生した場合 #pushServiceType _miscValue2が複数"() {
        setup:
        def endpointArn = setupSnS("android", "${pushServiceType}")
        JsonNode indexValues = getTransferIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("transfer")
        record.get(0).get("message").equals("DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
                PUSH_SERVICE_TYPE_APNS
                ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessage_transferイベントが発生した場合 #pushServiceType _miscValue2が4096文字"() {
        setup:
        def endpointArn = setupSnS("android", "${pushServiceType}")
        JsonNode indexValues = getTransferIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "${'a' * 4096}",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("transfer")
        record.get(0).get("message").equals("DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
                PUSH_SERVICE_TYPE_APNS
                ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessage_os_typeがIOSで、transferイベントが発生した場合 #pushServiceType "() {
        setup:
        def endpointArn = setupSnS("ios", "${pushServiceType}")
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        // テストデータはos_typeがandroid
        sql.execute("""
            UPDATE dc_user_device SET os_type = 'ios' WHERE sign_in_id = 'SID01AA-1001';
        """)

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("transfer")
        record.get(0).get("message").equals("DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
                PUSH_SERVICE_TYPE_APNS
                ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessage_SyncBusinessZoneStatus(BusinessZone口座開設)イベントが発生した場合 #pushServiceType "() {
        setup:
        def endpointArn = setupSnS("android", "${pushServiceType}")
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()
        JsonNode nonIndexValues = getApplyingNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        SyncBusinessZoneStatusEvent syncEvent = SyncBusinessZoneStatusEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(syncEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "アカウント開設受付が完了しました。\nアカウント開設を確定してください。\n" +
                "ビジネスゾーン名 : モックギンコウコウザ1\nアカウント開設受付完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)
        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("synchronous")
        record.get(0).get("message").equals("アカウント開設受付が完了しました。\nアカウント開設を確定してください。\n" +
                "ビジネスゾーン名 : モックギンコウコウザ1\nアカウント開設受付完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        record.get(0).get("notification_detail").toString().contains("zone_id")
        record.get(0).get("notification_detail").toString().contains("3001")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
                PUSH_SERVICE_TYPE_APNS
                ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessage_SyncBusinessZoneStatus(BusinessZone口座解約)イベントが発生した場合 #pushServiceType "() {
        setup:
        def endpointArn = setupSnS("android", "${pushServiceType}")
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()
        JsonNode nonIndexValues = getTerminatingNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        SyncBusinessZoneStatusEvent syncEvent = SyncBusinessZoneStatusEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(syncEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "アカウント解約受付が完了しました。\nアカウント解約を確定してください。\n" +
                "ビジネスゾーン名 : モックギンコウコウザ1\nアカウント解約受付完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("biz_terminating")
        record.get(0).get("message").equals("アカウント解約受付が完了しました。\nアカウント解約を確定してください。\n" +
                "ビジネスゾーン名 : モックギンコウコウザ1\nアカウント解約受付完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        record.get(0).get("notification_detail").toString().contains("zone_id")
        record.get(0).get("notification_detail").toString().contains("3001")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
                PUSH_SERVICE_TYPE_APNS
                ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessageModTokenLimitイベントが発生した場合 #pushServiceType "() {
        setup:
        def endpointArn = setupSnS("android", "${pushServiceType}")
        JsonNode indexValues = getModTokenLimitIndexValues()
        JsonNode nonIndexValues = getModTokenLimitNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ModTokenLimitEvent event = ModTokenLimitEvent.create(testEvent)
        PushNotificationMessage message = PushNotificationMessage.create(event)

        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(message)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        String actualMessage = getSnsMessage(endpointArn, "${pushServiceType}")
        String expected = "アカウント情報変更が完了しました。\nアカウント情報変更完了日時 : 2020.06.22 15:37"
        actualMessage.equals(expected)

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("account_updated")
        record.get(0).get("message").equals("アカウント情報変更が完了しました。\nアカウント情報変更完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        pushServiceType << [
                PUSH_SERVICE_TYPE_APNS
                ,PUSH_SERVICE_TYPE_GCM
        ]
    }

    def "testOnMessage_プッシュ通知フォーマット確認 #osType #pushServiceType "() {
        setup:
        def endpointArn = setupSnS("${osType}", "${pushServiceType}")
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        def snsPath = "/_aws/sns/platform-endpoint-messages"
        def snsRequest = snsHttpHelper.httpGet(snsPath).addQuery("region", "ap-northeast-1").build()
        def snsResponse = httpClient.execute(snsRequest)
        def snsResponseBody = snsHttpHelper.toJson(snsResponse)
        String tmpMessage = snsResponseBody.get("platform_endpoint_messages").get(endpointArn).get(0)
                .get("Message").textValue().replace("\\\"", "").replace('"','')
        String message = UniCodeHelper.escape(tmpMessage).replace("\\\\n", "\n").trim()

        String expected = "${pushNotificationMessage}"
        message.equals(expected)

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
            UPDATE dc_user_device SET push_token = 'xxxxxxxxxxxxxx', os_type = 'android' WHERE sign_in_id = 'SID01AA-1001';
        """)
        cleanupSns(endpointArn)

        where:
        osType    | pushServiceType        | pushNotificationMessage
        "ios"     | PUSH_SERVICE_TYPE_APNS | "{aps: {alert: {body: DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37}}}"
        "android" | PUSH_SERVICE_TYPE_APNS | "{aps: {alert: {body: DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37}}}"
        "ios"     | PUSH_SERVICE_TYPE_GCM  | "{notification: {body: DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37}}"
        "android" | PUSH_SERVICE_TYPE_GCM  | "{notification: {body: DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37}}"
    }

}
