name: "[BPM Email Send Tracker] Continuous Deployment"
run-name: "[${{ github.event.inputs.environment }}][${{github.ref_name}}] Continuous Deployment"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
env:
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}

permissions:
  id-token: write
  contents: read

jobs:
  build-and-push:
    
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    timeout-minutes: 20

    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          aws-region: ap-northeast-1

      - name: Amazon ECR Login
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build with Gradle / Docker Build and Push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: "${{ secrets.ENV_PREFIX }}-bctracker-email-sender"
          ARGS1: "email-send-tracker "
        run: ./scripts/build_and_ecr_push_bpm.sh $ARGS1

      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BPM Email Send Tracker] build-and-push job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: <!channel> Successfully executed the build-and-push job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BPM Email Send Tracker] build-and-push job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The build-and-push job failed to execute.

  update-manifests:
    runs-on: ubuntu-latest
    needs: build-and-push
    timeout-minutes: 20
    env:
      MANIFEST_REPOSITORY: decurret-lab/dcbg-dcf-kubernetes-bpm

    steps:
      - name: set image_tag environment value
        run:
          echo "image_tag=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV

      - name: dispatch update-manifests
        uses: peter-evans/repository-dispatch@v2
        with:
          repository: ${{ env.MANIFEST_REPOSITORY }}
          token: ${{ secrets.ACCESS_TOKEN_FOR_GITOPS }}
          event-type: update-manifest
          client-payload: '{"sha": "${{ env.image_tag }}", "env": "${{ github.event.inputs.environment }}", "apl": "bctracker-email-sender"}'

      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BPM Email Send Tracker] update-manifests job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: <!channel> Successfully executed the update-manifests job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BPM Email Send Tracker] update-manifests job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The update-manifests job failed to execute.
