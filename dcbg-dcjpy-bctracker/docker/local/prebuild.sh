#!/bin/bash

# 引数が2個以上でなければ終了
if [ $# -lt 2 ]; then
  exit 0
fi

ENV=$1

# biz の場合は、先に fin でビルドしているので、再度ビルドは不要
if [ "${ENV}" != "fin" ]; then
  exit 0
fi

shift

# `--build` オプションが指定されている場合に限り、ビルドを実行する
for arg in "$@"; do
  if [ "$arg" = "--build" ]; then
    ./gradlew clean :core:balance-tracker:build :core:transaction-tracker:build :bpm:push-notification-tracker:build :bpm:email-send-tracker:build -x test
    exit 0
  fi
done
