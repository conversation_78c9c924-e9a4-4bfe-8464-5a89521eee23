package com.decurret_dcp.dcjpy.bctracker.base.domain;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;

public interface MessageHandler<ORIGINAL> {

    /**
     * イベントを取得する。
     * 待機状態のイベントが存在しない場合は null を返却する。
     *
     * @return イベント
     */
    public BCEvent<ORIGINAL> poll();

    /**
     * 指定されたイベントの処理が完了した場合に呼び出す。
     *
     * @param event 完了イベント
     */
    public void complete(BCEvent<ORIGINAL> event);

    /**
     * 該当ハンドラがイベント取得処理を行っているか、状態を取得する。
     *
     * @return 稼働状態の場合は true
     */
    public boolean isActive();
}
