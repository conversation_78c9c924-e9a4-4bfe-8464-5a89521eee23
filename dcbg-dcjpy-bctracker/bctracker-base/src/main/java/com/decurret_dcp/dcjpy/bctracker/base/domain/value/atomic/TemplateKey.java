package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * テンプレートのキーを表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum TemplateKey {

    /** 依頼メッセージ。 */
    ORDER("order"),

    /** 実行完了メッセージ。 */
    ORDER_COMPLETED("order_completed"),

    /** 否認メッセージ。 */
    ORDER_REJECTED("order_rejected"),

    /** 移転完了メッセージ。 */
    TRANSFER("transfer"),

    /** 移転完了メール */
    EMAIL_TRANSFER("email_transfer"),

    /** アカウント情報変更完了メッセージ。 */
    ACCOUNT_UPDATED("account_updated"),

    /** サインイン完了メッセージ。 */
    SIGN_IN("sign_in"),

    /** ビジネスゾーンアカウント開設。 */
    BIZ_APPLYING("biz_applying"),

    /** ビジネスゾーンアカウント解約 */
    BIZ_TERMINATING("biz_terminating");

    private static final Map<String, TemplateKey> OBJECT_MAP = Arrays.stream(TemplateKey.values())
            .collect(Collectors.toMap(key -> key.getValue(), key -> key));

    private final String value;

    private TemplateKey(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static TemplateKey of(String key) {
        return OBJECT_MAP.get(key.toLowerCase());
    }
}
