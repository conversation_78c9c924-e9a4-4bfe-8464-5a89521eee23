package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TraceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class DischargeRequestedEvent implements BCEventTypeHolder {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final ZoneId fromZoneId;

    public final Amount amount;

    public final TraceId traceId;

    public static DischargeRequestedEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.DISCHARGE_REQUESTED) {
            return null;
        }

        String validatorId = JsonNodeReader.bytesToString(bcEvent.indexedValues.get("validatorId"));
        String accountId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("accountId"));
        String fromZoneId = JsonNodeReader.asText(bcEvent.nonIndexedValues.get("fromZoneId"));
        BigInteger amount = new BigInteger(bcEvent.nonIndexedValues.get("amount").asText());
        String traceId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("traceId"));

        return DischargeRequestedEvent.builder()
                .validatorId(ValidatorId.of(validatorId))
                .accountId(AccountId.of(accountId))
                .fromZoneId(ZoneId.of(fromZoneId))
                .amount(Amount.of(amount))
                .traceId(TraceId.of(traceId))
                .build();
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.DISCHARGE_REQUESTED;
    }
}
