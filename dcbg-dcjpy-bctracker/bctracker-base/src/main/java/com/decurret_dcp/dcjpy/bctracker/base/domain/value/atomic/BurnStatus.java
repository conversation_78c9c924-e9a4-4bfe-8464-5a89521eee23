package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * burn 処理状態を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum BurnStatus {

    /** initialize : 処理開始前。 */
    INITIALIZE("initialize"),

    /** burned : DC口座からDCJPY償却済み。 */
    BURNED("burned"),

    /** burn_failed : DC口座からDCJPY償却ができなかった。 */
    BURN_FAILED("burn_failed"),

    /** deposit_failed : DCJPY償却後、銀行預金口座への入金が失敗。 */
    DEPOSIT_FAILED("deposit_failed"),

    /** completed : DCJPY償却手続きが正常に完了。 */
    COMPLETED("completed");

    private static final Map<String, BurnStatus> OBJECT_MAP = Arrays.stream(BurnStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private BurnStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static BurnStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
