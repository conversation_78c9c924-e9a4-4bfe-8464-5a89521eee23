package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.UUID;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class VersionId {

    public final UUID value;

    private VersionId(UUID value) {
        this.value = value;
    }

    public static VersionId of(String informationId) {
        UUID uuid = UUID.fromString(informationId);
        return new VersionId(uuid);
    }

    public static VersionId of(UUID informationId) {
        return new VersionId(informationId);
    }

    public static VersionId generate() {
        return new VersionId(UUID.randomUUID());
    }

    public String getValue() {
        return this.value.toString();
    }
}
