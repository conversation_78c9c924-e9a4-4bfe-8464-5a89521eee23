package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 個人ユーザおよび法人ユーザのユーザ種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum DcUserType {

    /** individual : 個人ユーザ。 */
    INDIVIDUAL("individual", OrderRequiredType.DIRECT),

    /** company_owner : 法人ユーザ (アカウント管理者)。 */
    COMPANY_OWNER("company_owner", OrderRequiredType.DIRECT),

    /** company : 法人ユーザ (アカウント管理者以外)。 */
    COMPANY("company", OrderRequiredType.APPROVAL_FLOW);

    private static final Map<String, DcUserType> OBJECT_MAP = Arrays.stream(DcUserType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final OrderRequiredType orderRequired;

    private DcUserType(String value, OrderRequiredType orderRequired) {
        this.value = value;
        this.orderRequired = orderRequired;
    }

    public String getValue() {
        return this.value;
    }

    public OrderRequiredType orderRequired() {
        return this.orderRequired;
    }

    public static DcUserType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
