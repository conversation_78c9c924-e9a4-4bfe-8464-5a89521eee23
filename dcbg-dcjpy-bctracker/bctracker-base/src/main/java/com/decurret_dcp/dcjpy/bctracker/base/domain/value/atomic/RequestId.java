package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class RequestId {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    private static final String RANDOM_CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    private static final int REQUEST_ID_MAX_LENGTH = 94;

    private final String value;

    private RequestId(String value) {
        this.value = value;
    }

    public static RequestId of(String requestId) {
        return new RequestId(requestId);
    }

    /**
     * プレゼンテーションレイヤにて受け取ったリクエストIDから、
     * アプリケーション内部で利用するリクエストIDを生成する。
     *
     * DC口座番号を頭に付与することで、他ユーザとの重複を論理的に回避する。
     *
     * @param dcBankNumber DC銀行番号
     * @param requestId リクエストID
     * @return アプリケーション内部で利用するリクエストID
     */
    public static RequestId create(DcBankNumber dcBankNumber, String requestId) {
        return of(dcBankNumber.getValue() + "-" + requestId);
    }

    /**
     * RequestIdをランダムな文字列で生成する。
     *
     * @return ランダムに生成したリクエストID
     */
    public static RequestId create() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime now = LocalDateTime.now();
        String formattedDateTime = now.format(formatter);

        int randomStringLength = REQUEST_ID_MAX_LENGTH - formattedDateTime.length();
        StringBuilder stringBuilder = new StringBuilder(randomStringLength);
        for (int i = 0; i < randomStringLength; i++) {
            int index = SECURE_RANDOM.nextInt(RANDOM_CHARACTERS.length());
            stringBuilder.append(RANDOM_CHARACTERS.charAt(index));
        }
        String randomString = stringBuilder.toString();

        return of(formattedDateTime + randomString);
    }

    public RequestId suffix(Object suffix) {
        return of(this.value + suffix.toString());
    }

    public String getValue() {
        return this.value;
    }
}
