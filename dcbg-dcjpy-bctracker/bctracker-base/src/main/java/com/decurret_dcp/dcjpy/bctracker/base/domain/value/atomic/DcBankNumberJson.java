package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class DcBankNumberJson {

    static class Serializer extends JsonSerializer<DcBankNumber> {

        @Override
        public void serialize(DcBankNumber value, JsonGenerator generator, SerializerProvider provider)
                throws IOException {
            if (value == null) {
                return;
            }

            generator.writeString(value.getValue());
        }
    }

    static class Deserializer extends JsonDeserializer<DcBankNumber> {

        @Override
        public DcBankNumber deserialize(<PERSON>sonPars<PERSON> parser, DeserializationContext context) throws IOException {
            String value = parser.getValueAsString();
            return DcBankNumber.of(value);
        }
    }
}
