package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(exclude = "value")
@EqualsAndHashCode
public class Totp {

    private static final Pattern TOTP_PATTERN = Pattern.compile("[0-9]{6}");

    private final String value;

    private Totp(String value) {
        this.value = value;
    }

    public static Totp of(String totp) {
        Matcher matcher = TOTP_PATTERN.matcher(totp);
        if (matcher.matches() == false) {
            throw new IllegalArgumentException("Invalid format.");
        }

        return new Totp(totp);
    }

    public boolean isMatch(String target) {
        return this.value.equals(target);
    }

    public String getValue() {
        return this.value;
    }
}
