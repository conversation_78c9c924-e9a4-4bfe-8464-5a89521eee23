package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.core.type.TypeReference;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class FileTaskOrderDetail {

    private final String jsonString;

    private FileTaskOrderDetail(String jsonString) {
        this.jsonString = jsonString;
    }

    public static FileTaskOrderDetail of(Object jsonValue) {
        String jsonString = JsonConverter.toStringValue(jsonValue);
        return new FileTaskOrderDetail(jsonString);
    }

    // Doma2 が利用する想定
    @Deprecated
    public static FileTaskOrderDetail of(String jsonString) {
        return new FileTaskOrderDetail(jsonString);
    }

    public <TYPE extends FileTaskOrderDetailContent> TYPE getContent(TypeReference<TYPE> typeReference) {
        return JsonConverter.toJsonValue(this.jsonString, typeReference);
    }

    // Doma2 が利用する想定
    @Deprecated
    public String getValue() {
        return this.jsonString;
    }
}
