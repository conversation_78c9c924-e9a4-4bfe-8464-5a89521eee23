package com.decurret_dcp.dcjpy.bctracker.base.config;

import java.net.URI;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.regex.Pattern;

import org.seasar.doma.boot.autoconfigure.DomaConfigBuilder;
import org.seasar.doma.boot.autoconfigure.DomaProperties;
import org.seasar.doma.jdbc.Sql;
import org.seasar.doma.jdbc.UtilLoggingJdbcLogger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.SqsClientBuilder;

@Configuration
@Slf4j
public class BCTrackerBaseConfig {

    @Bean
    public SqsClient sqsClient(BCTrackerBaseSqsProperty property) {
        SqsClientBuilder builder = SqsClient.builder();

        if (property.toAwsConnection() == false) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(property.localEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));

            log.error(
                    "Change the SQS connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    property.localEndpoint
            );
        }

        return builder.build();
    }

    /**
     * Doma の設定。
     *
     * @param properties プロパティ
     *
     * @return builder
     */
    @Bean
    public DomaConfigBuilder domaConfigBuilder(DomaProperties properties) {
        DomaConfigBuilder builder = new DomaConfigBuilder(properties);
        builder.jdbcLogger(new BpmJdbcLogger(Level.FINE));

        return builder;
    }

    /**
     * SQLの実行クエリのログ出力を1行で出力させるためロガーWrapper.
     */
    private static class BpmJdbcLogger extends UtilLoggingJdbcLogger {

        private static final Pattern PATTERN = Pattern.compile("( *\n *)+");

        private BpmJdbcLogger(Level level) {
            super(level);
        }

        @Override
        protected void logSql(String callerClassName, String callerMethodName, Sql<?> sql, Level level,
                              Supplier<String> messageSupplier) {
            super.logSql(callerClassName, callerMethodName, sql, Level.INFO, messageSupplier);
        }

        @Override
        protected String getSqlText(Sql<?> sql) {
            String originalSql = super.getSqlText(sql);
            if (originalSql == null) {
                return null;
            }

            return PATTERN.matcher(originalSql).replaceAll(" ");
        }
    }
}
