package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum AccountStatus {

    APPLYING("applying"),

    ACTIVE("active"),

    FROZEN("frozen"),

    TERMINATING("terminating"),

    TERMINATED("terminated");

    private static final Map<String, AccountStatus> OBJECT_MAP = Arrays.stream(AccountStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private AccountStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static AccountStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
