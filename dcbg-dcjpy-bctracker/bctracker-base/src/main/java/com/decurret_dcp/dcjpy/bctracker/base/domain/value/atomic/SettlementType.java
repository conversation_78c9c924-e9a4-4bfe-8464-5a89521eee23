package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 精算種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum SettlementType {

    /** immediate : 即時実行。 */
    IMMEDIATE("immediate"),

    /** monthly_scheduled : 月次指定日指定。 */
    MONTHLY_SCHEDULED("monthly_scheduled");

    private static final Map<String, SettlementType> OBJECT_MAP = Arrays.stream(SettlementType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private SettlementType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static SettlementType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
