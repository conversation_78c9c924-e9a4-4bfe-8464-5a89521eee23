package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 法人ユーザの申請種別を表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum OrderType {

    /** mint : 発行。 */
    MINT("mint", OperationType.MINT, OperationType.REJECT_MINT),

    /** burn : 償却。 */
    BURN("burn", OperationType.BURN, OperationType.REJECT_BURN),

    /** transfer : 送金。 */
    TRANSFER("transfer", OperationType.TRANSFER, OperationType.REJECT_TRANSFER),

    /** charge : チャージ。 */
    CHARGE("charge", OperationType.CHARGE, OperationType.REJECT_CHARGE),

    /** discharge : ディスチャージ。 */
    DISCHARGE("discharge", OperationType.DISCHARGE, OperationType.REJECT_DISCHARGE),

    /** account_limit_updated : アカウント限度額変更。 */
    ACCOUNT_LIMIT_UPDATED("account_limit_updated", OperationType.CHANGE_ACCOUNT_LIMIT,
                          OperationType.REJECT_CHANGE_ACCOUNT_LIMIT),

    /** account_name_updated : アカウント名変更。 */
    ACCOUNT_NAME_UPDATED("account_name_updated", OperationType.CHANGE_ACCOUNT_NAME,
                         OperationType.REJECT_CHANGE_ACCOUNT_NAME),

    /** approve_transfer : 移転許可。 */
    APPROVE_TRANSFER("approve_transfer", OperationType.APPROVE_TRANSFER, OperationType.REJECT_APPROVE_TRANSFER),

    /** set_settlement : 精算条件設定。 */
    SET_SETTLEMENT("set_settlement", OperationType.CHANGE_ACCOUNT_SETTLEMENT,
                   OperationType.REJECT_CHANGE_ACCOUNT_SETTLEMENT);

    private static final Map<String, OrderType> OBJECT_MAP = Arrays.stream(OrderType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final OperationType operationType;

    private final OperationType rejectOperationType;

    private OrderType(String value, OperationType operationType) {
        this(value, operationType, null);
    }

    private OrderType(String value, OperationType operationType, OperationType rejectOperationType) {
        this.value = value;
        this.operationType = operationType;
        this.rejectOperationType = rejectOperationType;
    }

    public String getValue() {
        return this.value;
    }

    public OperationType getOperationType() {
        return this.operationType;
    }

    public OperationType rejectOperationType() {
        if (this.rejectOperationType == null) {
            throw new IllegalStateException("Not implement rejection. order_type : " + this.value);
        }

        return this.rejectOperationType;
    }

    public static OrderType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
