package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 銀行ユーザおよび事業者ユーザの権限を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum ServiceUserAuthorityKey {

    /** view_accounts : アカウント 一覧照会。 */
    VIEW_ACCOUNTS("view_accounts", true, true),

    /** view_account_detail : アカウント 詳細照会。 */
    VIEW_ACCOUNT_DETAIL("view_account_detail", true, true),

    /** view_transactions : アカウント 取引履歴照会。 */
    VIEW_TRANSACTIONS("view_transactions", true, true),

    /** change_account : アカウント 情報変更。 */
    CHANGE_ACCOUNT("change_account", true, true),

    /** reset_authentication : ユーザ 認証情報リセット。 */
    RESET_AUTHENTICATION("reset_authentication", true, true),

    /** user_suspended : ユーザ サインイン停止。 */
    USER_SUSPENDED("user_suspended", true, true),

    /** user_activated : ユーザ サインイン停止解除。 */
    USER_ACTIVATED("user_activated", true, true),

    /** account_frozen : アカウント 凍結。 */
    ACCOUNT_FROZEN("account_frozen", true, false),

    /** account_activated : アカウント 凍結解除。 */
    ACCOUNT_ACTIVATED("account_activated", true, false),

    /** account_force_burned : アカウント 強制償却。 */
    ACCOUNT_FORCE_BURNED("account_force_burned", true, false),

    /** account_force_terminated : アカウント 強制解約。 */
    ACCOUNT_FORCE_TERMINATED("account_force_terminated", true, false),

    /** view_information : インフォメーション 照会。 */
    VIEW_INFORMATION("view_information", true, true),

    /** change_information : インフォメーション 編集。 */
    CHANGE_INFORMATION("change_information", true, true),

    /** mint_nft : NFT発行。 */
    MINT_NFT("mint_nft", false, true),

    /** transfer_nft : NFT移転。 */
    TRANSFER_NFT("transfer_nft", false, true),

    /** view_nft : NFT照合。 */
    VIEW_NFT("view_nft", false, true);

    private static final Map<String, ServiceUserAuthorityKey> OBJECT_MAP = Arrays.stream(
                    ServiceUserAuthorityKey.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private static final List<ServiceUserAuthorityKey> FIN_ZONE_AVAILABLE_LIST = Arrays.stream(
                    ServiceUserAuthorityKey.values())
            .filter(key -> key.finZoneAvailable == true).toList();

    private static final List<ServiceUserAuthorityKey> BIZ_ZONE_AVAILABLE_LIST = Arrays.stream(
                    ServiceUserAuthorityKey.values())
            .filter(key -> key.bizZoneAvailable == true).toList();

    private final String value;

    private final boolean finZoneAvailable;

    private final boolean bizZoneAvailable;

    private ServiceUserAuthorityKey(String value, boolean finZoneAvailable, boolean bizZoneAvailable) {
        this.value = value;
        this.finZoneAvailable = finZoneAvailable;
        this.bizZoneAvailable = bizZoneAvailable;
    }

    public String getValue() {
        return this.value;
    }

    public static ServiceUserAuthorityKey of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }

    public static List<ServiceUserAuthorityKey> availableInFinZone() {
        return FIN_ZONE_AVAILABLE_LIST;
    }

    public static List<ServiceUserAuthorityKey> availableInBizZone() {
        return BIZ_ZONE_AVAILABLE_LIST;
    }
}
