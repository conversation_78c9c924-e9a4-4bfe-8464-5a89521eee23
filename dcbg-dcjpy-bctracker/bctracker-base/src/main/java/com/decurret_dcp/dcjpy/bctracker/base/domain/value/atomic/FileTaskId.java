package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class FileTaskId {

    public final String value;

    private FileTaskId(String value) {
        this.value = value;
    }

    public static FileTaskId of(String taskId) {
        return new FileTaskId(taskId);
    }

    public String getValue() {
        return this.value;
    }
}
