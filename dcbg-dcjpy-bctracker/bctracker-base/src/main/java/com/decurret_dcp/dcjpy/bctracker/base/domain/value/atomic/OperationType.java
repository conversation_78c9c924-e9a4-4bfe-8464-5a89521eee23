package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 理由コードが利用できる操作の種別を表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum OperationType {

    // 個人ユーザおよび法人ユーザ
    /** mint : DCJPY 発行。 */
    MINT("mint"),

    /** burn : DCJPY 償却。 */
    BURN("burn"),

    /** transfer : DCJPY 送金。 */
    TRANSFER("transfer"),

    /** charge : DCJPY チャージ。 */
    CHARGE("charge"),

    /** charge : DCJPY ディスチャージ。 */
    DISCHARGE("discharge"),

    /** view_account : アカウント 情報照会。 */
    VIEW_ACCOUNT("view_account"),

    /** transactions : アカウント 取引履歴照会。 */
    VIEW_TRANSACTIONS("view_transactions"),

    /** change_account_limit : アカウント限度額変更。 */
    CHANGE_ACCOUNT_LIMIT("change_account_limit"),

    /** change_account_name : アカウント名変更。 */
    CHANGE_ACCOUNT_NAME("change_account_name"),

    /** change_account_name : アカウント 精算条件変更。 */
    CHANGE_ACCOUNT_SETTLEMENT("change_account_settlement"),

    /** approve_transfer : 移転許可。 */
    APPROVE_TRANSFER("approve_transfer"),

    /** account_terminated : アカウント 解約。 */
    ACCOUNT_TERMINATED("account_terminated"),

    /** user_suspended : ユーザ サインイン停止。 */
    USER_SUSPENDED("user_suspended"),

    /** user_activated : ユーザ サインイン停止解除。 */
    USER_ACTIVATED("user_activated"),

    /** user_terminated : ユーザ無効。 */
    USER_TERMINATED("user_terminated"),

    // 銀行ユーザおよび事業者ユーザ

    /** view_accounts : 銀行ユーザ向け アカウント 一覧照会。 */
    VIEW_ACCOUNTS("view_accounts"),

    /** view_account_detail : 銀行ユーザ向け アカウント 詳細照会。 */
    VIEW_ACCOUNT_DETAIL("view_account_detail"),

//    /** view_transactions : 銀行ユーザ向け アカウント 取引履歴照会。 */
//    VIEW_TRANSACTIONS("view_transactions"),
//
//    /** change_account : 銀行ユーザ向け アカウント 情報変更。 */
//    CHANGE_ACCOUNT("change_account"),

    /** account_owner_suspended : 銀行ユーザ向け アカウント サインイン停止。 */
    ACCOUNT_OWNER_SUSPENDED("account_owner_suspended"),

    /** account_owner_activated : 銀行ユーザ向け アカウント サインイン停止解除。 */
    ACCOUNT_OWNER_ACTIVATED("account_owner_activated"),

    RESET_AUTHENTICATION("reset_authentication"),

    /** account_frozen : 銀行ユーザ向け アカウント 凍結。 */
    ACCOUNT_FROZEN("account_frozen"),

    /** account_activated : 銀行ユーザ向け アカウント凍結解除。 */
    ACCOUNT_ACTIVATED("account_activated"),

    /** account_force_burned : 銀行ユーザ向け アカウント 強制償却。 */
    ACCOUNT_FORCE_BURNED("account_force_burned"),

    /** account_force_terminated : 銀行ユーザ向け アカウント 強制解約。 */
    ACCOUNT_FORCE_TERMINATED("account_force_terminated"),

    /** account_inheritance : 銀行ユーザ向け アカウント 相続 / 譲渡。 */
    ACCOUNT_INHERITANCE("account_inheritance"),

//    /** view_information : 銀行ユーザ向け インフォメーション 照会。 */
//    VIEW_INFORMATION("view_information"),
//
//    /** change_information : 銀行ユーザ向け インフォメーション 編集。 */
//    CHANGE_INFORMATION("change_information"),

    // 事業者ユーザ
//    /** view_accounts : 事業者ユーザ向け アカウント 一覧照会。 */
//    VIEW_ACCOUNTS("view_accounts"),
//
//    /** view_account_detail : 事業者ユーザ向け アカウント 詳細照会。 */
//    VIEW_ACCOUNT_DETAIL("view_account_detail"),
//
//    /** view_transactions : 事業者ユーザ向け アカウント 取引履歴照会。 */
//    VIEW_TRANSACTIONS("view_transactions"),
//
//    /** change_account : 事業者ユーザ向け アカウント 情報変更。 */
//    CHANGE_ACCOUNT("change_account"),
//
//    /** reset_authentication : 事業者ユーザ向け ユーザ 認証情報リセット。 */
//    RESET_AUTHENTICATION("reset_authentication"),
//
//    /** user_suspended : 事業者ユーザ向け ユーザ サインイン停止。 */
//    USER_SUSPENDED("user_suspended"),
//
//    /** user_activated : 事業者ユーザ向け ユーザ サインイン停止解除。 */
//    USER_ACTIVATED("user_activated"),
//
//    /** view_information : 事業者ユーザ向け インフォメーション 照会。 */
//    VIEW_INFORMATION("view_information"),
//
//    /** change_information : 事業者ユーザ向け インフォメーション 編集。 */
//    CHANGE_INFORMATION("change_information"),

    /** mint_nft : 事業者ユーザ向け NFT発行。 */
    MINT_NFT("mint_nft"),

    /** transfer_nft : 事業者ユーザ向け NFT移転。 */
    TRANSFER_NFT("transfer_nft"),

    /** view_nft : 事業者ユーザ向け NFT照合。 */
    VIEW_NFT("view_nft"),

    /** reject_mint : DCJPY 発行否認。 */
    REJECT_MINT("reject_mint"),

    /** reject_burn : DCJPY 償却否認。 */
    REJECT_BURN("reject_burn"),

    /** reject_transfer : DCJPY 送金否認。 */
    REJECT_TRANSFER("reject_transfer"),

    /** reject_charge : DCJPY チャージ否認。 */
    REJECT_CHARGE("reject_charge"),

    /** reject_discharge : DCJPY ディスチャージ否認。 */
    REJECT_DISCHARGE("reject_discharge"),

    /** reject_change_account_limit : アカウント限度額変更否認。 */
    REJECT_CHANGE_ACCOUNT_LIMIT("reject_change_account_limit"),

    /** reject_change_account_name : DC口座表示名変更否認。 */
    REJECT_CHANGE_ACCOUNT_NAME("reject_change_account_name"),

    /** reject_approve_transfer : 移転許可否認。 */
    REJECT_APPROVE_TRANSFER("reject_approve_transfer"),

    /** reject_change_account_settlement : 精算条件設定否認。 */
    REJECT_CHANGE_ACCOUNT_SETTLEMENT("reject_change_account_settlement"),

    /** reject_account_frozen : アカウント凍結否認。 */
    REJECT_ACCOUNT_FROZEN("reject_account_frozen"),

    /** reject_account_activated : アカウント凍結解除否認。 */
    REJECT_ACCOUNT_ACTIVATED("reject_account_activated"),

    /** reject_account_force_burned : アカウント強制償却否認。 */
    REJECT_ACCOUNT_FORCE_BURNED("reject_account_force_burned"),

    /** reject_account_force_terminated : アカウント強制解約否認。 */
    REJECT_ACCOUNT_FORCE_TERMINATED("reject_account_force_terminated"),

    /** reject_change_user_name : ユーザ表示名変更否認。 */
    REJECT_CHANGE_USER_NAME("reject_change_user_name"),

    /** reject_reset_auth : ユーザ認証情報リセット否認。 */
    REJECT_RESET_AUTH("reject_reset_auth"),

    /** reject_user_suspended : ユーザサインイン停止否認。 */
    REJECT_USER_SUSPENDED("reject_user_suspended"),

    /** reject_user_activated : ユーザサインイン停止解除否認。 */
    REJECT_USER_ACTIVATED("reject_user_activated"),

    /** reject_change_information : インフォメーション編集否認。 */
    REJECT_CHANGE_INFORMATION("reject_change_information");

    private static final Map<String, OperationType> OBJECT_MAP = Arrays.stream(OperationType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private OperationType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static OperationType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
