package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum SignInStatus {

    /** available : 利用可能 */
    AVAILABLE("available"),

    /** need_setup_authenticator : 認証アプリの設定が必要 */
    NEED_SETUP_AUTHENTICATOR("need_setup_authenticator"),

    /** need_setup_password : パスワードの設定が必要 */
    NEED_SETUP_PASSWORD("need_setup_password"),

    /** need_setup_account : DCアカウント開設が必要 */
    NEED_SETUP_ACCOUNT("need_setup_account"),

    /** need_setup_ib : IB 再認証が必要 */
    NEED_SETUP_IB("need_setup_ib");

    private static final Map<String, SignInStatus> OBJECT_MAP = Arrays.stream(SignInStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private SignInStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static SignInStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
