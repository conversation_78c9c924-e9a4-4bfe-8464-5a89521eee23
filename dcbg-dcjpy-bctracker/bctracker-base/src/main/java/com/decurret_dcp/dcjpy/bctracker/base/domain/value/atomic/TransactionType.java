package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum TransactionType {

    /** 発行。 */
    MINT("mint"),

    /** 償却。 */
    BURN("burn"),

    /** 送金。 */
    TRANSFER("transfer"),

    /** チャージ。 */
    CHARGE("charge"),

    /** ディスチャージ。 */
    DISCHARGE("discharge"),

    /** 強制ディスチャージ。 */
    FORCE_DISCHARGE("force_discharge"),

    /** 強制償却。 */
    FORCE_BURN("force_burn");

    private static final Map<String, TransactionType> OBJECT_MAP = Arrays.stream(TransactionType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private TransactionType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static TransactionType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
