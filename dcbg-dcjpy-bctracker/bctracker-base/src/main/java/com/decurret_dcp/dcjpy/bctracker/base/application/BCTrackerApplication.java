package com.decurret_dcp.dcjpy.bctracker.base.application;

import com.decurret_dcp.dcjpy.bctracker.base.config.BCTrackerBaseSqsProperty;
import com.decurret_dcp.dcjpy.bctracker.base.domain.MessageHandler;
import com.decurret_dcp.dcjpy.bctracker.base.domain.SleepUtil;
import com.decurret_dcp.dcjpy.bctracker.base.domain.service.TraceIdHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * MessageHandler に蓄積されたメッセージを取得し、Tracker に処理を移譲するクラス。
 * MessageHandler の活動が停止するまで処理を継続する。
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BCTrackerApplication<ENTITY, MESSAGE> {

    private final BCTrackerBaseSqsProperty property;

    private final BaseTracker<ENTITY> tracker;

    private final MessageHandler<MESSAGE> messageHandler;

    public void execute() {
        while (true) {
            try {
                BCEvent<MESSAGE> event = this.messageHandler.poll();
                if (event == null) {
                    // イベントが取得できず、ハンドラが稼働していない場合は処理を終了する
                    if (this.messageHandler.isActive() == false) {
                        break;
                    }

                    SleepUtil.sleepSilently(300L);
                    continue;
                }

                TraceIdHolder.setTraceId(event.traceId());
                try {
                    this.doHandleEvent(event);
                } finally {
                    TraceIdHolder.remove();
                }
            } catch (RuntimeException exc) {
                log.error("Unexpected failure is occurred in {}.", this.property.queueName, exc);
            }
        }

        log.warn("Terminated handling message in {}.", this.property.queueName);
    }

    private void doHandleEvent(BCEvent<MESSAGE> event) {
        ENTITY entity = this.tracker.acceptable(event);
        if (entity == null) {
            log.debug("Skip event in {}, transactionHash : {}", this.property.queueName, event.transactionHash);
            this.messageHandler.complete(event);

            return;
        }

        try {
            boolean result = this.tracker.onMessage(entity);
            if (result == true) {
                log.info("Complete event in {}, transactionHash : {}",
                         this.property.queueName, event.transactionHash);
                this.messageHandler.complete(event);
            }
        } catch (RuntimeException exc) {
            log.error("Failed to handle message", exc);
        }
    }
}
