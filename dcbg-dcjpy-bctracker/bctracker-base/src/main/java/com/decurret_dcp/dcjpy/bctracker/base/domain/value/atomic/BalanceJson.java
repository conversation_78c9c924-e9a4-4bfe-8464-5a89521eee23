package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.io.IOException;
import java.math.BigInteger;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class BalanceJson {

    static class Serializer extends JsonSerializer<Balance> {

        @Override
        public void serialize(Balance value, JsonGenerator generator, SerializerProvider provider) throws IOException {
            if (value == null) {
                return;
            }

            generator.writeNumber(value.getValue());
        }
    }

    static class Deserializer extends JsonDeserializer<Balance> {

        @Override
        public Balance deserialize(Json<PERSON>ars<PERSON> parser, DeserializationContext context) throws IOException {
            return new Balance(new BigInteger(parser.getValueAsString()));
        }
    }
}
