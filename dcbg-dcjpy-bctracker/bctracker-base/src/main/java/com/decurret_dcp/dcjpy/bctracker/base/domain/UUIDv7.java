package com.decurret_dcp.dcjpy.bctracker.base.domain;

import java.nio.ByteBuffer;
import java.security.SecureRandom;
import java.time.ZonedDateTime;
import java.util.Random;
import java.util.UUID;

/**
 * UUID v7 を生成するクラス。
 * ref : https://www.ietf.org/archive/id/draft-peabody-dispatch-new-uuid-format-01.html
 *
 */
public class UUIDv7 {

    private static final Random RANDOM = new SecureRandom(
            ByteBuffer.allocate(Long.BYTES).putLong(System.currentTimeMillis()).array());

    public static UUID create() {
        return UUIDv7.createOrderMillSec(RANDOM);
    }

    static UUID createOrderMillSec(Random random) {
        ZonedDateTime now = ZonedDateTime.now();
        long unixTime = now.toEpochSecond();
        long millisecond = now.toInstant().toEpochMilli() % 1000L;

        byte[] from48 = new byte[10];
        random.nextBytes(from48);
        from48[0] &= 0x0f; // clear version
        from48[0] |= 0x70; // set to version 7
        from48[3] &= 0x3f; // clear variant
        from48[3] |= (byte) 0x80; // set to IETF variant

        long mostSigBits = (((unixTime << 20) | (millisecond << 8) | (from48[0] & 0xff)) << 8) | (from48[1] & 0xff);
        long leastSigBits = 0L;
        for (int index = 2; index < from48.length; index++) {
            leastSigBits = (leastSigBits << 8) | (from48[index] & 0xff);
        }

        return new UUID(mostSigBits, leastSigBits);
    }
}
