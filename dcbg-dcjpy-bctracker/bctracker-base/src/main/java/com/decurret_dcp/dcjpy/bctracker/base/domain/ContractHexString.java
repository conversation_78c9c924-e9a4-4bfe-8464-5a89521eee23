package com.decurret_dcp.dcjpy.bctracker.base.domain;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;

import org.web3j.utils.Numeric;

public class ContractHexString {

    public static String toString(String str) {
        if (Objects.isNull(str) || str.isEmpty()) {
            return null;
        }

        byte[] array = Numeric.hexStringToByteArray(str);

        int end;
        for (end = 0; end < array.length; end++) {
            // 0x00を終端とみなしています。Bytes32がそのようになっているため
            if (array[end] == 0x00) {
                break;
            }
        }

        byte[] target = Arrays.copyOf(array, end);
        return new String(target, StandardCharsets.UTF_8);
    }
}
