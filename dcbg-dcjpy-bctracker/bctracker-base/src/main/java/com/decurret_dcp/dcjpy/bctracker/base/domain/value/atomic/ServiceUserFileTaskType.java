package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 銀行ユーザ/事業者ユーザのファイルタスク種別を表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum ServiceUserFileTaskType {

    /** list_nft : NFT一覧ファイル作成。 */
    LIST_NFT("list_nft"),

    /** nft_mint : NFT発行。 */
    NFT_MINT("nft_mint"),

    /** nft_transfer : NFT移転。 */
    NFT_TRANSFER("nft_transfer");

    private static final Map<String, ServiceUserFileTaskType> OBJECT_MAP
            = Arrays.stream(ServiceUserFileTaskType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private ServiceUserFileTaskType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static ServiceUserFileTaskType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
