package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * 銀行ユーザおよび事業者ユーザの申請種別を表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum ServiceOrderType {

    // ユーザに関する操作

    /** user_suspended : サインイン停止。 */
    USER_SUSPENDED("user_suspended", OperationType.USER_SUSPENDED, OperationType.REJECT_USER_SUSPENDED),

    /** user_activated : サインイン停止解除（アクティブ）。 */
    USER_ACTIVATED("user_activated", OperationType.USER_ACTIVATED, OperationType.REJECT_USER_ACTIVATED),

    /** user_updated : ユーザ情報変更。 */
    USER_UPDATED("user_updated", null, null),

    /** user_reset : ユーザ認証リセット。 */
    USER_RESET("user_reset", null, null),

    // アカウントに関する操作

    /** account_limit_updated : アカウント限度額変更。 */
    ACCOUNT_LIMIT_UPDATED("account_limit_updated", OperationType.CHANGE_ACCOUNT_LIMIT,
                          OperationType.REJECT_CHANGE_ACCOUNT_LIMIT),

    /** account_name_updated : アカウント名変更。 */
    ACCOUNT_NAME_UPDATED("account_name_updated", OperationType.CHANGE_ACCOUNT_NAME, null),

    /** account_frozen : アカウント凍結。 */
    ACCOUNT_FROZEN("account_frozen", OperationType.ACCOUNT_FROZEN, OperationType.REJECT_ACCOUNT_FROZEN),

    /** account_activated : アカウント凍結解除（アクティブ）。 */
    ACCOUNT_ACTIVATED("account_activated", OperationType.ACCOUNT_ACTIVATED, OperationType.REJECT_ACCOUNT_ACTIVATED),

    /** account_force_burned : 強制償却。 */
    ACCOUNT_FORCE_BURNED("account_force_burned", OperationType.ACCOUNT_FORCE_BURNED,
                         OperationType.REJECT_ACCOUNT_FORCE_BURNED),

    /** account_force_terminated : 強制解約。 */
    ACCOUNT_FORCE_TERMINATED("account_force_terminated", OperationType.ACCOUNT_FORCE_TERMINATED,
                             OperationType.REJECT_ACCOUNT_FORCE_TERMINATED),

    // 上記以外

    /** information_created : インフォメーション作成。 */
    INFORMATION_CREATED("information_created", null, null),

    /** info_updated : インフォメーション編集。 */
    INFORMATION_EDITED("information_edited", null, OperationType.REJECT_CHANGE_INFORMATION);

    private static final Map<String, ServiceOrderType> OBJECT_MAP = Arrays.stream(ServiceOrderType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final OperationType operationType;

    private final OperationType rejectOperationType;

    private ServiceOrderType(String value, OperationType operationType) {
        this(value, operationType, null);
    }

    private ServiceOrderType(String value, OperationType operationType, OperationType rejectOperationType) {
        this.value = value;
        this.operationType = operationType;
        this.rejectOperationType = rejectOperationType;
    }

    public String getValue() {
        return this.value;
    }

    public OperationType getOperationType() {
        return this.operationType;
    }

    public OperationType rejectOperationType() {
        if (this.rejectOperationType == null) {
            throw new IllegalStateException("Not implement rejection. order_type : " + this.value);
        }

        return this.rejectOperationType;
    }

    public static ServiceOrderType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
