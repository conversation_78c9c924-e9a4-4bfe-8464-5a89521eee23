package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class OAuthState {

    public final String value;

    private OAuthState(String value) {
        this.value = value;
    }

    public static OAuthState of(String state) {
        return new OAuthState(state);
    }

    public String getValue() {
        return this.value;
    }
}
