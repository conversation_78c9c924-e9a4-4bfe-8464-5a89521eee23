package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * ユーザ状態を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum UserStatus {

    /** initializing : 初期設定待ち。 */
    INITIALIZING("initializing", true) {
        @Override
        public boolean doCanTransitionTo(UserStatus target) {
            // アクティブ以外 (= サインイン停止 or ユーザ無効) に遷移可能
            return (target != ACTIVE);
        }
    },

    /** active : アクティブ。 */
    ACTIVE("active", true) {
        @Override
        public boolean doCanTransitionTo(UserStatus target) {
            return true;
        }
    },

    /** suspended : サインイン停止。 */
    SUSPENDED("suspended", false) {
        @Override
        public boolean doCanTransitionTo(UserStatus target) {
            return true;
        }
    },

    /** temporary_suspended : サインイン一時停止。(userテーブルには保持されない) */
    TEMPORARY_SUSPENDED("temporary_suspended", false) {
        @Override
        public boolean doCanTransitionTo(UserStatus target) {
            // ユーザ無効に限り遷移可能
            return (target == INACTIVE);
        }
    },

    /** inactive : ユーザ無効。 */
    INACTIVE("inactive", false) {
        @Override
        public boolean doCanTransitionTo(UserStatus target) {
            // 任意の状態に手動変更不可
            return false;
        }
    };

    private static final Map<String, UserStatus> OBJECT_MAP = Arrays.stream(UserStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private final boolean canSignIn;

    private UserStatus(String value, boolean canSignIn) {
        this.value = value;
        this.canSignIn = canSignIn;
    }

    /**
     * 引数に指定した状態に手動で変更できる場合はtrueを返す。
     *
     * @param target 遷移先の状態
     * @return true:変更可能 false:変更不可
     */
    public boolean canTransitionTo(UserStatus target) {
        if ((target == INITIALIZING) || (target == TEMPORARY_SUSPENDED)) {
            return false;
        }

        return this.doCanTransitionTo(target);
    }

    protected abstract boolean doCanTransitionTo(UserStatus target);

    public String getValue() {
        return this.value;
    }

    /**
     * 該当状態がサインイン可能かどうか。
     *
     * @return true:サインイン可能, false:サインイン不可
     */
    public boolean canSignIn() {
        return this.canSignIn;
    }

    public static UserStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
