package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class EntityId {

    public final String value;

    private EntityId(String value) {
        this.value = value;
    }

    public static EntityId of(String entityId) {
        return new EntityId(entityId);
    }

    public String getValue() {
        return this.value;
    }
}
