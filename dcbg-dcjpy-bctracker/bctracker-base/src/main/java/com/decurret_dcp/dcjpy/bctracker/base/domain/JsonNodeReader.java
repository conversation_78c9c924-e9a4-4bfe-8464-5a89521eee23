package com.decurret_dcp.dcjpy.bctracker.base.domain;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.function.Function;

import com.fasterxml.jackson.databind.JsonNode;

public class JsonNodeReader {

    private JsonNodeReader() {
        // Do nothing.
    }

    public static String asText(JsonNode jsonNode, String... fieldNames) {
        return readValue(jsonNode, fieldNames, (node) -> node.asText());
    }

    public static Long asLong(JsonNode jsonNode, String... fieldNames) {
        return readValue(jsonNode, fieldNames, (node) -> Long.valueOf(node.asLong()));
    }

    private static <TYPE> TYPE readValue(JsonNode node, String[] fieldNames, Function<JsonNode, TYPE> converter) {
        if ((fieldNames == null) || (fieldNames.length == 0)) {
            return converter.apply(node);
        }

        JsonNode target = node;
        for (String fieldName : fieldNames) {
            if (target.has(fieldName) == false) {
                return null;
            }

            target = target.get(fieldName);
        }

        return converter.apply(target);
    }

    public static String bytesToString(JsonNode node) {
        if (node == null || node.isArray() == false) {
            throw new IllegalArgumentException("node value is not byte array");
        }

        ByteBuffer buffer = ByteBuffer.allocate(node.size());
        for (JsonNode subNode : node) {
            int value = subNode.asInt();
            if (value != 0) { // 0は無視
                buffer.put((byte) value);
            }
        }

        byte[] byteArray = new byte[buffer.position()];
        buffer.rewind();
        buffer.get(byteArray);
        return new String(byteArray, StandardCharsets.UTF_8);
    }
}
