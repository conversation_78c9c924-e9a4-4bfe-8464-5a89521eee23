package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.UUID;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class NotificationId {

    public final UUID value;

    private NotificationId(UUID value) {
        this.value = value;
    }

    public static NotificationId of(UUID notificationId) {
        return new NotificationId(notificationId);
    }

    public static NotificationId of(String notificationId) {
        return new NotificationId(UUID.fromString(notificationId));
    }

    public String getValue() {
        return this.value.toString();
    }
}
