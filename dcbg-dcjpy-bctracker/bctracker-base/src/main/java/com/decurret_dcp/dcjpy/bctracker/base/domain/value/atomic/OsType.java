package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * スマートフォンのOS種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum OsType {

    /** ios : iOS。 */
    IOS("ios"),

    /** android : Android。 */
    ANDROID("android"),

    /** other : その他。 */
    OTHER("other");

    private static final Map<String, OsType> OBJECT_MAP = Arrays.stream(OsType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private OsType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static OsType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
