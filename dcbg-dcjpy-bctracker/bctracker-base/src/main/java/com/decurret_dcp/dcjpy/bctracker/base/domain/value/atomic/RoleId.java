package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.UUID;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class RoleId {

    public final UUID value;

    private RoleId(UUID value) {
        this.value = value;
    }

    public static RoleId of(String roleId) {
        UUID uuid = UUID.fromString(roleId);
        return new RoleId(uuid);
    }

    public static RoleId of(UUID roleId) {
        return new RoleId(roleId);
    }

    public String getValue() {
        return this.value.toString();
    }
}
