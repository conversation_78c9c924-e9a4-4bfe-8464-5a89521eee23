package com.decurret_dcp.dcjpy.bctracker.base.domain.value;

import java.util.function.Function;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public class Either<SUCCESS> {

    private final SUCCESS success;

    private final Failure failure;

    private Either(SUCCESS success) {
        this.success = success;
        this.failure = null;
    }

    private Either(Failure failure) {
        this.success = null;
        this.failure = failure;
    }

    public boolean isSuccess() {
        return (this.failure == null);
    }

    public SUCCESS success() {
        if (this.isSuccess() == false) {
            throw new IllegalStateException("This Either is failure.");
        }

        return this.success;
    }

    public Failure failure() {
        if (this.isSuccess()) {
            throw new IllegalStateException("This Either is success.");
        }

        return this.failure;
    }

    public <TO> Either<TO> map(Function<SUCCESS, TO> mapper) {
        if (this.isSuccess() == false) {
            return Either.failure(this.failure);
        }

        return Either.success(mapper.apply(this.success));
    }

    public String errorCode() {
        if (this.failure == null) {
            throw new IllegalStateException("This Either is success.");
        }

        return this.failure.getErrorCode();
    }

    public String errorMessage() {
        if (this.failure == null) {
            throw new IllegalStateException("This Either is success.");
        }

        return this.failure.getErrorMessage();
    }

    public Exception cause() {
        if (this.failure == null) {
            throw new IllegalStateException("This Either is success.");
        }

        return this.failure.exception;
    }

    public RuntimeException toUnexpectedException() {
        if (this.failure == null) {
            throw new IllegalStateException("This Either is success.");
        }

        return this.failure.toUnexpectedException();
    }

    public static <SUCCESS> Either<SUCCESS> success(SUCCESS success) {
        return new Either<>(success);
    }

    public static <SUCCESS> Either<SUCCESS> failure(Failure failure) {
        return new Either<>(failure);
    }

    public static <SUCCESS> Either<SUCCESS> failure(String errorCode, String errorMessage) {
        return new Either<>(
                Failure.builder()
                        .errorCode(errorCode)
                        .errorMessage(errorMessage)
                        .build()
        );
    }

    public static <SUCCESS> Either<SUCCESS> failure(String errorCode, String errorMessage, Exception exception) {
        return new Either<>(
                Failure.builder()
                        .errorCode(errorCode)
                        .errorMessage(errorMessage)
                        .exception(exception)
                        .build()
        );
    }

    @ToString
    @EqualsAndHashCode
    @Builder(access = lombok.AccessLevel.PRIVATE)
    public static class Failure {

        private final String errorCode;

        private final String errorMessage;

        private final Exception exception;

        public String getErrorCode() {
            if (this.errorCode == null) {
                throw this.toUnexpectedException();
            }

            return this.errorCode;
        }

        public RuntimeException toUnexpectedException() {
            return new RuntimeException("[" + this.errorCode + "]" + this.errorMessage, this.exception);
        }

        public String getErrorMessage() {
            return this.errorMessage;
        }
    }
}
