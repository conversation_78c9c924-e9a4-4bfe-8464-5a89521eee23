package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.core.type.TypeReference;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class NotificationDetail {

    private final String jsonString;

    private NotificationDetail(String jsonString) {
        this.jsonString = jsonString;
    }

    public static NotificationDetail of(String jsonString) {
        return new NotificationDetail(jsonString);
    }

    public static NotificationDetail of(Object jsonValue) {
        String jsonString = JsonConverter.toStringValue(jsonValue);
        return new NotificationDetail(jsonString);
    }

    public <TYPE> TYPE getContent(TypeReference<TYPE> typeReference) {
        return JsonConverter.toJsonValue(this.jsonString, typeReference);
    }

    @Deprecated
    public String getValue() {
        return this.jsonString;
    }
}
