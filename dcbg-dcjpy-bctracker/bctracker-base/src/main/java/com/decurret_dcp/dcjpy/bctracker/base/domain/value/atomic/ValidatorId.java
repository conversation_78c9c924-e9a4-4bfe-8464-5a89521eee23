package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class ValidatorId {

    private final String value;

    private ValidatorId(String value) {
        this.value = value;
    }

    public static ValidatorId of(String validatorId) {
        return new ValidatorId(validatorId);
    }

    public EntityId toEntityId() {
        return EntityId.of(this.value);
    }

    public String getValue() {
        return this.value;
    }
}
