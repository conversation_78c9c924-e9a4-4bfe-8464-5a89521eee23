package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

class JsonConverter {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private JsonConverter() {
        // Do nothing.
    }

    // JSON文字列 -> POJO
    public static <T> T toJsonValue(String jsonString, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // POJO -> J<PERSON><PERSON>文字列
    public static String toStringValue(Object jsonValue) {
        try {
            return OBJECT_MAPPER.writeValueAsString(jsonValue);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // JSON文字列 -> JsonNode
    public static JsonNode toJsonNode(String jsonString) {
        try {
            return OBJECT_MAPPER.readTree(jsonString);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
