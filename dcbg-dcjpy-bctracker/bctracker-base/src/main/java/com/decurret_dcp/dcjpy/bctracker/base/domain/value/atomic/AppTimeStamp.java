package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * タイムゾーン付きの日時を表す。
 *
 * DomaGen が生成する日時がデフォルトで LocalDateTime となりタイムゾーンが欠けてしまうので、
 * このクラスを利用することで、タイムゾーンを保持できるようにする。
 */
@ToString
@EqualsAndHashCode
@JsonSerialize(using = AppTimeStampJson.Serializer.class)
@JsonDeserialize(using = AppTimeStampJson.Deserializer.class)
@Domain(valueType = Timestamp.class, factoryMethod = "of", acceptNull = false)
public class AppTimeStamp implements Comparable<AppTimeStamp> {

    private static final ZoneOffset TOKYO_ZONE_OFFSET = ZoneOffset.ofHours(9);

    private final long value;

    private AppTimeStamp() {
        this(System.currentTimeMillis());
    }

    private AppTimeStamp(Timestamp timestamp) {
        this(timestamp.getTime());
    }

    private AppTimeStamp(ZonedDateTime datetime) {
        this(datetime.toInstant().toEpochMilli());
    }

    private AppTimeStamp(long timestamp) {
        this.value = timestamp;
    }

    public static AppTimeStamp create(BlockTimeStamp datetime) {
        if (datetime == null) {
            return null;
        }

        return of(datetime.zonedDateTime());
    }

    /**
     * タイムゾーン付き日時を取得する。
     *
     * @return zoned DateTime
     */
    public ZonedDateTime zonedDateTime() {
        return this.zonedDateTime(TOKYO_ZONE_OFFSET);
    }

    /**
     * タイムゾーン付き日時を取得する。
     *
     * @param zoneId ZoneID
     *
     * @return zoned DateTime
     */
    public ZonedDateTime zonedDateTime(ZoneId zoneId) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(this.value), zoneId);
    }

    public AppTimeStamp after(long time, TimeUnit timeUnit) {
        long nextTimestamp = this.value + timeUnit.toMillis(time);
        return new AppTimeStamp(nextTimestamp);
    }

    /**
     * 自身が引数に指定された日時より過去ならばtrue.
     *
     * @param other 比較対象
     *
     * @return 自身が引数に指定された日時より過去ならばtrue
     */
    public boolean isBeforeFrom(AppTimeStamp other) {
        return (this.value < other.value);
    }

    /**
     * 自身が引数に指定された日時より未来ならばtrue.
     *
     * @param other 比較対象
     *
     * @return 自身が引数に指定された日時より未来ならばtrue
     */
    public boolean isAfterTo(AppTimeStamp other) {
        return (this.value > other.value);
    }

    public String format() {
        return this.format(TOKYO_ZONE_OFFSET, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    }

    public String format(DateTimeFormatter formatter) {
        return this.format(TOKYO_ZONE_OFFSET, formatter);
    }

    public String format(ZoneId zoneId) {
        return this.format(zoneId, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    }

    public String format(ZoneId zoneId, DateTimeFormatter formatter) {
        return this.zonedDateTime(zoneId).format(formatter);
    }

    /**
     * 現在日時を返す。
     */
    public static AppTimeStamp now() {
        return new AppTimeStamp();
    }

    /**
     * 現座日時に分を加算した日時を返す。
     *
     * @param addMinutes 加算する時間(分)
     */
    public static AppTimeStamp addMinutes(Integer addMinutes) {
        return new AppTimeStamp(System.currentTimeMillis() + (addMinutes * 60 * 1000));
    }

    /**
     * 指定された日時のオブジェクトを返す。
     *
     * @param datetime タイムゾーン付き日時
     */
    public static AppTimeStamp of(ZonedDateTime datetime) {
        return new AppTimeStamp(datetime);
    }

    public static AppTimeStamp of(long datetime) {
        return new AppTimeStamp(datetime);
    }

    /**
     * 本メソッドは Doma2 が内部で利用するものなので、アプリケーション側では利用しないこと。
     */
    @Deprecated
    public static AppTimeStamp of(Timestamp timestamp) {
        return new AppTimeStamp(timestamp);
    }

    /**
     * 本メソッドは Doma2 が内部で利用するものなので、アプリケーション側では利用しないこと。
     * アプリケーションで利用する場合は zonedDateTime() メソッドを利用すること。
     *
     * @return java.sql.Timestamp オブジェクト
     */
    @Deprecated
    public Timestamp getValue() {
        return new Timestamp(this.value);
    }

    @Override
    public int compareTo(AppTimeStamp other) {
        return Long.compare(this.value, other.value);
    }
}
