package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum FileTaskDetailStatus {

    /** accept : 受付済み。 */
    ACCEPT("accept"),

    /** format_failure : フォーマット不備。 */
    FORMAT_FAILURE("format_failure"),

    /** not_processed : 未処理。(別レコードがフォーマット不備により処理されない状態) */
    NOT_PROCESSED("not_processed"),

    /** in_processing : 処理実行中。 */
    IN_PROCESSING("in_processing"),

    /** completed : 処理完了。 */
    COMPLETED("completed"),

    /** failed : 処理失敗。 */
    FAILED("failed");

    private static final Map<String, FileTaskDetailStatus> OBJECT_MAP = Arrays.stream(FileTaskDetailStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private FileTaskDetailStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static FileTaskDetailStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
