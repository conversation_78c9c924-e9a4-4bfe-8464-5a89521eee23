package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@JsonSerialize(using = AccountIdJson.Serializer.class)
@JsonDeserialize(using = AccountIdJson.Deserializer.class)
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class AccountId {

    private static final String PREFIX = "60";

    private static final String ESCROW_PREFIX = "61";

    private final String value;

    private AccountId(String value) {
        this.value = value;
    }

    public static AccountId of(String accountId) {
        return new AccountId(accountId);
    }

    public String getValue() {
        return this.value;
    }
    
    public boolean isEscrowAccount() {
        return this.value.startsWith(ESCROW_PREFIX);
    }
}
