package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class SignInId {

    public final String value;

    private SignInId(String value) {
        this.value = value;
    }

    public static SignInId of(String signInId) {
        return new SignInId(signInId);
    }

    public String getValue() {
        return this.value;
    }
}
