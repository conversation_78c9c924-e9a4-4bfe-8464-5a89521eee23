package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum QrConfirmationType {

    UPDATE_ACCOUNT_LIMIT("update_account_limit", OperationType.CHANGE_ACCOUNT_LIMIT),

    UPDATE_ACCOUNT_NAME("update_account_name", OperationType.CHANGE_ACCOUNT_NAME),

    TRANSFER("transfer", OperationType.TRANSFER);

    private static final Map<String, QrConfirmationType> OBJECT_MAP = Arrays.stream(QrConfirmationType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    public static final String KEY_NAME = "confirmation_type";

    private final String value;

    private final OperationType operationType;

    private QrConfirmationType(String value, OperationType operationType) {
        this.value = value;
        this.operationType = operationType;
    }

    public String getValue() {
        return this.value;
    }

    public OperationType operationType() {
        return this.operationType;
    }

    public static QrConfirmationType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}