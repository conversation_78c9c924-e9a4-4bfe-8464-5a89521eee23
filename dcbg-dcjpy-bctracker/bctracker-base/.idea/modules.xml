<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/balance-tracker.bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/balance-tracker.bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1181076771/balance-tracker.core-repository.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1181076771/balance-tracker.core-repository.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/balance-tracker.main.iml" filepath="$PROJECT_DIR$/.idea/modules/balance-tracker.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/balance-tracker.test.iml" filepath="$PROJECT_DIR$/.idea/modules/balance-tracker.test.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/bpm-repository.bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/bpm-repository.bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/bpm-repository.main.iml" filepath="$PROJECT_DIR$/.idea/modules/bpm-repository.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/core-repository.bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/core-repository.bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/core-repository.bctracker-base.test.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/core-repository.bctracker-base.test.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/core-repository.main.iml" filepath="$PROJECT_DIR$/.idea/modules/core-repository.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/../dcbg-dcjpy-bctracker.iml" filepath="$PROJECT_DIR$/../dcbg-dcjpy-bctracker.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/email-send-tracker.bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/email-send-tracker.bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-781903235/email-send-tracker.bpm-repository.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-781903235/email-send-tracker.bpm-repository.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/email-send-tracker.main.iml" filepath="$PROJECT_DIR$/.idea/modules/email-send-tracker.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/email-send-tracker.test.iml" filepath="$PROJECT_DIR$/.idea/modules/email-send-tracker.test.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/push-notification-tracker.bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/push-notification-tracker.bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-781903235/push-notification-tracker.bpm-repository.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-781903235/push-notification-tracker.bpm-repository.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/push-notification-tracker.main.iml" filepath="$PROJECT_DIR$/.idea/modules/push-notification-tracker.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/push-notification-tracker.test.iml" filepath="$PROJECT_DIR$/.idea/modules/push-notification-tracker.test.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-893193764/transaction-tracker.bctracker-base.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-893193764/transaction-tracker.bctracker-base.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/-1181076771/transaction-tracker.core-repository.main.iml" filepath="$PROJECT_DIR$/.idea/modules/-1181076771/transaction-tracker.core-repository.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/transaction-tracker.main.iml" filepath="$PROJECT_DIR$/.idea/modules/transaction-tracker.main.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/modules/transaction-tracker.test.iml" filepath="$PROJECT_DIR$/.idea/modules/transaction-tracker.test.iml" />
    </modules>
  </component>
</project>