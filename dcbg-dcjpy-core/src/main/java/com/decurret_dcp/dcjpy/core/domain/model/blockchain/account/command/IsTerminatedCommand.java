package com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class IsTerminatedCommand extends BlockchainContractCommand {

    public IsTerminatedCommand(AccountId accountId) {
        super(ContractName.ACCOUNT, "isTerminated",
              Map.of("accountId", new ContractBytes32Value(accountId.getValue())));
    }

    @Override
    public List<Type<?>> asAbiParams() {

        return List.of(
                // 署名不要のため空のまま
        );
    }
}
