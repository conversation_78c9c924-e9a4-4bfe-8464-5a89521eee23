package com.decurret_dcp.dcjpy.core.domain.model.account;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class Account {
    public final AccountId accountId;
    public final String accountName;
    public final AccountStatus accountStatus;
    public final ReasonCode reasonCode;
    public final ZoneId zoneId;
    public final String zoneName;
    public final Balance balance;
    public final Balance cacheBalance;
    public final Amount mintLimit;
    public final Amount burnLimit;
    public final Amount chargeLimit;
    public final Amount transferLimit;
    public final Amount cumulativeLimit;
    public final Amount cumulativeAmount;
    public final AppTimeStamp cumulativeDate;
    public final AppTimeStamp appliedAt;
    public final AppTimeStamp registeredAt;
    public final AppTimeStamp terminatingAt;
    public final AppTimeStamp terminatedAt;
}
