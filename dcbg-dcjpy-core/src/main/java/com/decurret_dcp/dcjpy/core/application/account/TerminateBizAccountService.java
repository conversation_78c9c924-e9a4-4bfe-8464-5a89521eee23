package com.decurret_dcp.dcjpy.core.application.account;

import java.util.List;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.command.CheckTerminateBizAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.command.TerminateBizAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ResponseContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.ValidatorSetBizZoneTerminatedCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class TerminateBizAccountService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final GetBalanceList getBalanceList;

    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * BizZone口座解約前確認を実行します。
     *
     * @param checkTerminateCommand BizZone口座解約前確認コマンド
     */
    public void check(CheckTerminateBizAccountServiceCommand checkTerminateCommand) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // getBalanceListを呼び出す
        this.callGetBalanceList(checkTerminateCommand.accountId, checkTerminateCommand.zoneId);
    }

    private void callGetBalanceList(AccountId accountId, ZoneId zoneId) {
        TokenGetBalanceListResult balanceResult = getBalanceList.execute(accountId, callBlockchainContractService);

        List<ZoneId> zoneIds = balanceResult.zoneIds;
        List<ResponseContractBytes32Value> statusList = balanceResult.accountStatus;

        for (int index = 0; index < zoneIds.size(); index++) {
            if (zoneIds.get(index).equals(zoneId)) {
                boolean notTerminating = (AccountStatus.of(statusList.get(index).value) != AccountStatus.TERMINATING);
                if (notTerminating) {
                    throw new BadRequestException(MessageCode.BIZ_ACCOUNT_NOT_ENABLED_TERMINATED);
                }

                break;
            }
        }
    }

    /**
     * BizZone口座解約を実行します。
     *
     * @param terminateCommand BizZone口座解約コマンド
     */
    public void execute(TerminateBizAccountServiceCommand terminateCommand) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // getBalanceListを呼び出す
        this.callGetBalanceList(terminateCommand.accountId, terminateCommand.zoneId);

        // setBizZoneTerminatedを呼び出す
        this.callSetBizZoneTerminated(terminateCommand);
    }

    private void callSetBizZoneTerminated(TerminateBizAccountServiceCommand terminateCommand) {
        EntityId signerId = new EntityId(terminateCommand.validatorId);
        AccountId accountId = terminateCommand.accountId;
        ZoneId zoneId = terminateCommand.zoneId;
        AccountStatus accountStatus = AccountStatus.TERMINATED;

        ValidatorSetBizZoneTerminatedCommand command =
                new ValidatorSetBizZoneTerminatedCommand(accountId, zoneId, accountStatus);
        sendBlockchainContractService.execute(command, signerId);
    }
}
