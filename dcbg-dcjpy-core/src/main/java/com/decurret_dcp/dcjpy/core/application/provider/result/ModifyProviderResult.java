package com.decurret_dcp.dcjpy.core.application.provider.result;

import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneName;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class ModifyProviderResult {
    public final ProviderId providerId;
    public final ZoneName zoneName;

    public ModifyProviderResult(ProviderId providerId, ZoneName zoneName) {
        Assertion.assertNotNull(providerId, "providerId");
        Assertion.assertNotNull(zoneName, "zoneName");
        this.providerId = providerId;
        this.zoneName = zoneName;
    }
}
