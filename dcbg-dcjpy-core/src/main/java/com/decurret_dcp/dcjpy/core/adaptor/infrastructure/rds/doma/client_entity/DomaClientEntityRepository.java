package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.client_entity;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntity;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntityRepository;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaClientEntityRepository implements ClientEntityRepository {

    private final ClientEntityDao clientEntityDao;

    @Override
    public ClientEntity selectByClientId(ClientId clientId) {
        return clientEntityDao.selectByClientId(clientId);
    }

    @Override
    public ClientEntity selectByEntityId(EntityId entityId) {
        return clientEntityDao.selectByEntityId(entityId);
    }

    @Override
    public void save(ClientEntity clientEntity) {
        clientEntityDao.insert(clientEntity);
    }

    @Override
    public void delete(ClientEntity clientEntity) {
        clientEntityDao.delete(clientEntity);
    }
}
