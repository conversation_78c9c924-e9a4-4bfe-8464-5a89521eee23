package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetBalancesServiceCommand {

    public final IssuerId issuerId;
    public final AccountId accountId;

    public GetBalancesServiceCommand(IssuerId issuerId, AccountId accountId) {
        Assertion.assertNotNull(issuerId, "issuerId");
        Assertion.assertNotNull(accountId, "accountId");
        this.issuerId = issuerId;
        this.accountId = accountId;
    }
}