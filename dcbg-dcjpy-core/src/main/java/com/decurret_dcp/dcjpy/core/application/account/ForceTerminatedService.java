package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.result.ForceTerminatedResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.SetTerminatedCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ForceTerminatedService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final ValidatorGetAccount validatorGetAccount;
    private final GetBalanceList getBalanceList;
    private final BalanceCacheRepository balanceCacheRepository;

    public ForceTerminatedResult check(ValidatorId validatorId, AccountId accountId) {
        ValidatorGetAccountResult result = this.checkForceTerminated(validatorId, accountId);

        return ForceTerminatedResult.builder()
                .accountId(accountId)
                .accountName(result.accountData.accountName)
                .build();
    }

    public ForceTerminatedResult execute(ValidatorId validatorId, AccountId accountId, ReasonCode reasonCode) {
        ValidatorGetAccountResult result = this.checkForceTerminated(validatorId, accountId);

        SetTerminatedCommand command = new SetTerminatedCommand(validatorId, accountId, reasonCode);
        EntityId entityId = new EntityId(validatorId);
        sendBlockchainContractService.execute(command, entityId);

        return ForceTerminatedResult.builder()
                .accountId(accountId)
                .accountName(result.accountData.accountName)
                .build();
    }

    private ValidatorGetAccountResult checkForceTerminated(ValidatorId validatorId, AccountId accountId) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // 残高キャッシュを取得する
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(accountId, zoneId);

        if (balanceCache.balance.isZero() == false) {
            throw new BadRequestException(MessageCode.BALANCE_EXISTS);
        }

        // アカウント情報を取得する
        ValidatorGetAccountResult validatorGetAccountResult
                = validatorGetAccount.execute(validatorId, accountId, callBlockchainContractService);

        // アカウント状態の確認
        AccountStatus accountStatus = AccountStatus.of(validatorGetAccountResult.accountData.accountStatus.getValue());
        if (accountStatus == AccountStatus.TERMINATED) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }
        if ((accountStatus != AccountStatus.FROZEN) && (accountStatus != AccountStatus.FORCE_BURNED)) {
            throw new BadRequestException(MessageCode.UNABLE_TO_TERMINATE_ACCOUNT);
        }

        // Biz残高チェック
        TokenGetBalanceListResult listResult = this.getBalanceList.execute(accountId, callBlockchainContractService);
        if (listResult.totalBalance.isZero() == false) {
            throw new BadRequestException(MessageCode.BIZ_BALANCE_EXISTS);
        }

        return validatorGetAccountResult;
    }
}
