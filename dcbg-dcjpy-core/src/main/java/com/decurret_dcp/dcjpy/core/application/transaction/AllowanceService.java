package com.decurret_dcp.dcjpy.core.application.transaction;

import java.math.BigInteger;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.AllowanceServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.AllowanceResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.AllowanceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class AllowanceService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final ValidatorHasAccount validatorHasAccount;

    /**
     * 送金許可額を取得します
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 送信許可額の取得リクエストに失敗した場合
     *     E0020 owner_account_idが見つかりません
     *     E0021 spender_account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public AllowanceResult execute(AllowanceServiceCommand command) throws
            BadRequestException {
        validatorHasAccount.execute(command.validatorId, command.ownerId, MessageCode.OWNER_ACCOUNT_ID_NOT_FOUND,
                                    new ValidatorHasAccount.PathValueSwitch(), callBlockchainContractService);

        validatorHasAccount.execute(command.validatorId, command.spenderId, MessageCode.SPENDER_ACCOUNT_ID_NOT_FOUND,
                                    new ValidatorHasAccount.PathValueSwitch(), callBlockchainContractService);

        AllowanceCommand allowanceCommand = command.toAllowanceCommand();
        BlockchainCallResponse response =
                callBlockchainContractService.executeWithoutSignature(allowanceCommand);
        Object err = response.data.get("err");
        if (err != null && !"".equals(err.toString())) {
            throw new BlockchainCallErrorException(err.toString());
        }
        Amount allowance = new Amount(new BigInteger(response.data.get("allowance").toString()));

        return new AllowanceResult(command.ownerId, command.spenderId, allowance);
    }
}
