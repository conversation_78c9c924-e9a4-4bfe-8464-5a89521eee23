package com.decurret_dcp.dcjpy.core.application.token_spec.result;

import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Enabled;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TotalSupply;

import lombok.Builder;

@Builder
public class GetTokenServiceResult {

    public final TokenId tokenId;

    public final IssuerId issuerId;

    public final TotalSupply totalSupply;

    public final Enabled enabled;
}
