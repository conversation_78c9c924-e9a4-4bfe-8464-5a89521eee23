package com.decurret_dcp.dcjpy.core.application.account.result;

import java.util.List;

import com.decurret_dcp.dcjpy.core.domain.model.account.Account;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class GetAccountListResult {
    public final List<Account> accounts;

    public final Offset offset;

    public final Limit limit;

    public final TotalCount totalCount;

}
