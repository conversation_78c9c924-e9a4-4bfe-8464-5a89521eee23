package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.GetZonesServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.GetZonesResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetZoneByAccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetZoneByAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetZonesService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final ProviderGetZone providerGetZone;
    private final ValidatorGetZoneByAccountId validatorGetZoneByAccountId;

    @Transactional(readOnly = true)
    public GetZonesResult execute(GetZonesServiceCommand command) throws NotFoundException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // BizZone の場合は実行できない
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        ValidatorId validatorId = command.validatorId;
        AccountId accountId = command.accountId;

        //FinZone情報を取得する
        ProviderGetZoneResult providerGetZoneResult = providerGetZone.execute(callBlockchainContractService);

        // アカウントに紐づくZone情報一覧取得
        ValidatorGetZoneByAccountIdResult validatorGetZoneByAccountIdResult =
                validatorGetZoneByAccountId.execute(validatorId, accountId, callBlockchainContractService);
        return GetZonesResult.initResult(providerGetZoneResult, validatorGetZoneByAccountIdResult);
    }

}
