package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.AmountNullable;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.Builder;

@Builder
public class TokenLimitServiceCommand {
    public final IssuerId issuerId;
    public final AccountId accountId;
    public final AmountNullable transferLimit;
    public final AmountNullable chargeLimit;
    public final AmountNullable mintLimit;
    public final AmountNullable burnLimit;
    public final AmountNullable cumulativeLimit;
}
