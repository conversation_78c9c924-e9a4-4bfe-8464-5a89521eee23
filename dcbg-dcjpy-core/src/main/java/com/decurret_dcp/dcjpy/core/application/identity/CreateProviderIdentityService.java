package com.decurret_dcp.dcjpy.core.application.identity;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.identity.command.CreateProviderIdentityServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.HasProvider;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.AddProviderRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentity;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentityManager;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.UserPoolClient;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class CreateProviderIdentityService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final HasProvider hasProvider;
    private final EntityIdentityManager entityIdentityManager;

    /**
     * プロバイダアイデンティティの作成を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException プロバイダアイデンティティの作成に失敗した場合
     *     E0028 provider_idが見つかりません
     *     E0027 provider_idが無効です
     */
    @Transactional
    public UserPoolClient execute(CreateProviderIdentityServiceCommand command) throws BadRequestException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        // プロバイダIDの存在確認
        hasProvider.execute(command.providerId, new HasProvider.PathValueSwitch(), callBlockchainContractService);

        EntityIdentity identity = this.entityIdentityManager.saveIdentity(command.providerId, zoneId);

        AddProviderRoleCommand addProviderRoleCommand = command.toAddProviderRoleCommand(identity.eoa);
        EntityId adminId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(addProviderRoleCommand, adminId);

        return identity.userPoolClient;
    }
}
