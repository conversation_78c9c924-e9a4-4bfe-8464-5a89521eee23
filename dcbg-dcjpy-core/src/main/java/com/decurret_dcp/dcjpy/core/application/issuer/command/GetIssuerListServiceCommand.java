package com.decurret_dcp.dcjpy.core.application.issuer.command;

import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetIssuerListServiceCommand {

    public final Offset offset;

    public final Limit limit;

    public GetIssuerListServiceCommand(Offset offset, Limit limit) {
        Assertion.assertNotNull(offset, "offset");
        Assertion.assertNotNull(limit, "limit");
        this.offset = offset;
        this.limit = limit;
    }
}
