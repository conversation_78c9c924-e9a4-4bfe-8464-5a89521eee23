package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;

@Documented
@Constraint(validatedBy = NftId.NftIdValidator.class)
@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface NftId {
    String message() default "{com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring.annotation.NftId.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    public class NftIdValidator implements ConstraintValidator<NftId, String> {
        Pattern p1 = Pattern.compile("^[A-Za-z0-9_]{0,32}$");

        @Override
        public boolean isValid(String contactField, ConstraintValidatorContext context) {
            if (contactField == null || contactField.isEmpty()) {
                return false;
            }
            Matcher m1 = p1.matcher(contactField);
            return m1.matches();
        }
    }
}