package com.decurret_dcp.dcjpy.core.application.issuer;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.issuer.command.GetIssuerServiceCommand;
import com.decurret_dcp.dcjpy.core.application.issuer.result.GetIssuerResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetIssuer;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetIssuerResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetIssuerService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerGetIssuer issuerGetIssuer;

    @Transactional(readOnly = true)
    public GetIssuerResult execute(GetIssuerServiceCommand command) throws NotFoundException {
        IssuerId issuerId = command.issuerId;

        // Issuer情報取得
        IssuerGetIssuerResult issuerGetIssuerResult =
                issuerGetIssuer.execute(issuerId, callBlockchainContractService);
        return GetIssuerResult.initResult(issuerGetIssuerResult, command);
    }
}
