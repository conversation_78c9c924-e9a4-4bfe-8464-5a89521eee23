package com.decurret_dcp.dcjpy.core.application.validator.result;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;

import lombok.Builder;

@Builder
public class GetValidatorAccountResult {

    /** アカウントID */
    public final AccountId accountId;

    /** アカウント名 */
    public final String accountName;

    /** アカウント状態 */
    public final AccountStatus accountStatus;

    /** 状態変更理由コード */
    public final ReasonCode reasonCode;

    /** アカウント残高 */
    public final Balance balance;

    /** 利用申込日時 */
    public final AppTimeStamp appliedAt;

    /** 利用確定日時 */
    public final AppTimeStamp registeredAt;

    /** 解約申込日時 */
    public final AppTimeStamp terminatingAt;

    /** 解約確定日時 */
    public final AppTimeStamp terminatedAt;
}
