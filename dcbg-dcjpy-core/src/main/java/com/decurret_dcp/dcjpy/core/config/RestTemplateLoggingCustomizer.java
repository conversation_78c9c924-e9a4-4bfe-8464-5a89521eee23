package com.decurret_dcp.dcjpy.core.config;

import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.decurret_dcp.dcjpy.core.util.RestTemplateLoggingInterceptor;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class RestTemplateLoggingCustomizer implements RestTemplateCustomizer {

    private final DcfConfig dcfConfig;

    @Override
    public void customize(RestTemplate restTemplate) {
        restTemplate.getInterceptors().add(
                new RestTemplateLoggingInterceptor(dcfConfig.getLogOutputMaxLengthFromItem()));
    }
}