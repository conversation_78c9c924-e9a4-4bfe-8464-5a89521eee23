package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckExchangeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.JpyTokenDischargeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.Timeout;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class FinZoneDischargeServiceCommand {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final Amount dischargeAmount;

    public final ZoneId fromZoneId;

    public CheckExchangeCommand toCheckCommand(ZoneId toZoneId) {
        return new CheckExchangeCommand(
                this.accountId,
                this.fromZoneId,
                toZoneId,
                this.dischargeAmount
        );
    }

    public JpyTokenDischargeCommand toJpyTokenDischargeCommand(ZoneId toZoneId, long unixTime) {
        return new JpyTokenDischargeCommand(
                this.accountId,
                this.fromZoneId,
                toZoneId,
                this.dischargeAmount,
                new Timeout(unixTime)
        );
    }
}
