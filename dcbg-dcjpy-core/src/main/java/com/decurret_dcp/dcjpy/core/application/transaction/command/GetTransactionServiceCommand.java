package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class GetTransactionServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final TransactionId transactionId;
}
