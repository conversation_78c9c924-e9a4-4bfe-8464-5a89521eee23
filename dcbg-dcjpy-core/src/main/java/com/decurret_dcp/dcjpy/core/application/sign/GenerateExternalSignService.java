package com.decurret_dcp.dcjpy.core.application.sign;

import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bouncycastle.math.ec.ECPoint;
import org.springframework.stereotype.Service;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.ECKeyPair;
import org.web3j.crypto.Hash;
import org.web3j.crypto.Keys;
import org.web3j.crypto.Sign;

import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.application.sign.response.OneTimeKeyResponse;
import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.consts.DCFConst;
import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendRequest;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySignFailedException;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySigner;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySignerRepository;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKey;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.SignKeyHolder;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ExpirationDate;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.PublicKey;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.RequestIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;
import com.decurret_dcp.dcjpy.core.util.DCFStringUtil;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GenerateExternalSignService {

    private final DcfConfig dcfConfig;
    private final SignKeyHolder signKeyHolder;
    private final ClockService clockService;
    private final EntitySignerRepository externalSignerRepository;

    public BlockchainSendRequest createBlockchainSendRequest(BlockchainContractCommand command, EntityId entityId) {
        RequestId requestId = RequestIdThreadLocalHolder.getRequestId();
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        Map<String, Object> args = createContractArgs(command, entityId);
        return BlockchainSendRequest.builder()
                .requestId(requestId.getValue())
                .zoneId((zoneId != null) ? zoneId.getValue().toString() : "")
                .contractName(command.getContractName().getValue())
                .method(command.getMethod())
                .args(args)
                .isAsync(command.isAsync())
                .build();
    }

    public BlockchainCallRequest createBlockchainCallRequest(BlockchainContractCommand command, EntityId entityId) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        Map<String, Object> args = createContractArgs(command, entityId);

        return BlockchainCallRequest.builder()
                .zoneId((zoneId != null) ? zoneId.getValue().toString() : "")
                .contractName(command.getContractName().getValue())
                .method(command.getMethod())
                .args(args)
                .build();
    }

    /**
     * ワンタイムキーを取得する
     *
     * @param issuerId イシュアID
     * @param accountId アカウントID
     * @return 実行結果
     * @throws BadRequestException ワンタイムキーの取得に失敗した場合
     *     E0068 アカウントのアイデンティティが未登録です
     */
    public OneTimeKeyResponse createOneTimeKey(IssuerId issuerId, AccountId accountId) throws BadRequestException {
        EntitySigner issuerEntitySigner = findEntitySigner(new EntityId(issuerId));
        PrivateKey accountPrivateKey;
        try {
            EntitySigner accountEntitySigner = findEntitySigner(new EntityId(accountId));
            accountPrivateKey = this.signKeyHolder.findPrivateKey(accountEntitySigner);
        } catch (EntitySignFailedException signFailed) {
            throw new BadRequestException(MessageCode.ACCOUNT_IDENTITY_NOT_FOUND, signFailed);
        }

        try {
            ECKeyPair ecKeyPair = Keys.createEcKeyPair();
            BigInteger skC = ecKeyPair.getPrivateKey();
            byte[] pkC = getCompressX(skC);

            BigInteger privateKey = new BigInteger(accountPrivateKey.getValue().substring(2), 16);
            BigInteger n = Sign.CURVE_PARAMS.getN();
            BigInteger skO = privateKey.add(skC).mod(n);
            byte[] pkO = getCompressX(skO);

            // 値が999999999999999999未満の場合、現在時刻を加算し
            // 値が999999999999999999以上の場合、最大有効期間(およそ西暦300億年)とし、現在時刻を加算しない
            BigInteger pt = new BigInteger(String.valueOf(dcfConfig.getPeriod()));
            if (dcfConfig.getPeriod() < DCFConst.MAX_PERIOD) {
                pt = pt.add(new BigInteger(String.valueOf(Instant.now().getEpochSecond())));
            }

            @SuppressWarnings("rawtypes")
            List<Type> params = List.of(new DynamicBytes(pkC), new Uint256(pt));
            String hash = Hash.sha3(FunctionEncoder.encodeConstructor(params));

            String signature = this.signKeyHolder.createSignature(issuerEntitySigner, hash);

            return new OneTimeKeyResponse(
                    new PrivateKey(DCFStringUtil.toHexStringWithPrefix64(skO)),
                    new PublicKey(pkO),
                    new PrivateKey(DCFStringUtil.toHexString64(skC)),
                    new PublicKey(pkC),
                    new ExpirationDate(pt),
                    new Signature(signature));
        } catch (InvalidAlgorithmParameterException | NoSuchAlgorithmException | NoSuchProviderException exc) {
            throw new RuntimeException(exc);
        }
    }

    private EntitySigner findEntitySigner(EntityId entityId) {
        EntitySigner signer = externalSignerRepository.selectBySignerId(entityId);
        if (signer == null) {
            // DBに署名者が登録されていなければシステムエラー.
            throw new EntitySignFailedException(MessageCode.UNKNOWN_SIGNER);
        }

        return signer;
    }

    private Map<String, Object> createContractArgs(BlockchainContractCommand command, EntityId entityId) {

        long epochSecond = clockService.instant().getEpochSecond() + dcfConfig.getDeadline();

        List<Type<?>> commandParams = command.asAbiParams();
        @SuppressWarnings("rawtypes")
        List<Type> params = new ArrayList<>(commandParams);
        params.add(new Uint256(epochSecond));
        String hash = Hash.sha3(FunctionEncoder.encodeConstructor(params));

        EntitySigner entitySigner = findEntitySigner(entityId);
        String signature = this.signKeyHolder.createSignature(entitySigner, hash);

        Map<String, Object> args = new HashMap<>();
        command.getArgs().forEach((name, value) -> args.put(name, value.getValue()));
        args.put("deadline", String.valueOf(epochSecond));
        args.put("signature", signature);

        return args;
    }

    private static byte[] getCompressX(BigInteger privateKey) {
        ECPoint point = Sign.publicPointFromPrivate(privateKey);
        return point.getEncoded(true);
    }
}
