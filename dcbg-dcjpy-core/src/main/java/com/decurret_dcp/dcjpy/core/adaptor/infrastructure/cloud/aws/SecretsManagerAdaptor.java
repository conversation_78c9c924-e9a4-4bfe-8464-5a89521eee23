package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.cloud.aws;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.secret.CredentialEntity;
import com.decurret_dcp.dcjpy.core.domain.model.secret.CredentialStorage;
import com.decurret_dcp.dcjpy.core.domain.model.secret.Oauth2Secret;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.CreateSecretRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;
import software.amazon.awssdk.services.secretsmanager.model.ReplicaRegionType;
import software.amazon.awssdk.services.secretsmanager.model.ResourceNotFoundException;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerException;
import software.amazon.awssdk.services.secretsmanager.model.UpdateSecretRequest;

@RequiredArgsConstructor
@Component
@Slf4j
public class SecretsManagerAdaptor implements CredentialStorage {

    private static final String KMS_KEY_ID = "alias/app-resource-encryption-key";

    private final SecretsManagerClient client;

    private final ObjectMapper objectMapper;

    private final DcfConfig config;

    private static String authorityClientSecret(EntityId entityId) {
        return "dcjpy/core/client_credentials/" + entityId.value;
    }

    @Override
    public Oauth2Secret findClientSecret(EntityId entityId) {
        String secretId = authorityClientSecret(entityId);
        return this.doFindCredentials(secretId, Oauth2Secret.class);
    }

    private <TYPE extends CredentialEntity> TYPE doFindCredentials(String secretId, Class<TYPE> returnType) {
        GetSecretValueResponse response = this.doFindSecretValue(secretId);

        try {
            return this.objectMapper.readValue(response.secretString(), returnType);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException("Failed to decode credential. secretId = " + secretId, jsonExc);
        }
    }

    private GetSecretValueResponse doFindSecretValue(String secretId) {
        GetSecretValueRequest request = GetSecretValueRequest.builder()
                .secretId(secretId)
                .build();

        return this.client.getSecretValue(request);
    }

    @Override
    public void saveClientSecret(EntityId entityId, Oauth2Secret clientSecret) {
        String secretId = authorityClientSecret(entityId);
        // リージョンが切り替わった場合、レプリケーションしたデータは更新できない
        // そのため、更新する必要がないクライアントシークレット系のデータのみレプリケーションを行う。
        this.saveCredential(secretId, clientSecret, true);
    }

    private <TYPE extends CredentialEntity> void saveCredential(String secretId, TYPE token, boolean isClientSecret) {
        String secretString;
        try {
            secretString = this.objectMapper.writeValueAsString(token);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException("Failed to encode credential. secretId = " + secretId, jsonExc);
        }

        boolean isExists = this.exists(secretId);
        if (isExists) {
            this.doUpdateCredential(secretId, secretString);
        } else {
            this.doCreateCredential(secretId, secretString, isClientSecret);
        }
    }

    private void doCreateCredential(String secretId, String secretString, boolean withReplication) {

        CreateSecretRequest.Builder builder = CreateSecretRequest.builder()
                .name(secretId)
                .secretString(secretString)
                .kmsKeyId(KMS_KEY_ID);

        // レプリケーションを行うリージョンを設定
        if (StringUtils.hasText(this.config.getSecretManagerReplicaRegion()) && withReplication == true) {
            ReplicaRegionType replicaRegionType = ReplicaRegionType.builder()
                    .region(this.config.getSecretManagerReplicaRegion())
                    .kmsKeyId(KMS_KEY_ID)
                    .build();
            builder.addReplicaRegions(replicaRegionType);
        }

        CreateSecretRequest request = builder.build();

        try {
            this.client.createSecret(request);
        } catch (SecretsManagerException exc) {
            log.error("Failed to create credential in secret manager. secretId : {}, detail : {}",
                      secretId, exc.getMessage());
            throw exc;
        }

        log.info("Created credential in secret manager. secretId : {}, replica region : {}",
                 secretId, (StringUtils.hasText(this.config.getSecretManagerReplicaRegion())) ?
                         this.config.getSecretManagerReplicaRegion() : "none");
    }

    private void doUpdateCredential(String secretId, String secretString) {
        UpdateSecretRequest request = UpdateSecretRequest.builder()
                .secretId(secretId)
                .secretString(secretString)
                .kmsKeyId(KMS_KEY_ID)
                .build();

        try {
            this.client.updateSecret(request);
        } catch (SecretsManagerException exc) {
            log.error("Failed to update credential in secret manager. secretId : {}, detail : {}",
                      secretId, exc.getMessage());
            throw exc;
        }

        log.info("Updated credential in secret manager. secretId : {}", secretId);
    }

    private boolean exists(String secretId) {
        try {
            this.doFindSecretValue(secretId);
            return true;
        } catch (ResourceNotFoundException exc) {
            return false;
        }
    }
}
