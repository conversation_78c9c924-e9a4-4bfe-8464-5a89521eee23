package com.decurret_dcp.dcjpy.core.application.token_spec;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.CreateTokenServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ProviderAddTokenCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenIdGenerator;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenSpec;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
@Validated
public class CreateTokenService {
    private final TokenIdGenerator tokenIdGenerator;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * 共通領域コイン／付加領域コインを作成します
     *
     * @param command コマンド
     * @return 共通領域コイン／付加領域コイン
     */
    @Transactional(readOnly = true)
    public TokenSpec execute(CreateTokenServiceCommand command) {
        TokenId tokenId = tokenIdGenerator.generate();

        EntityId signerId = new EntityId(command.providerId);

        ProviderAddTokenCommand addTokenArgs = command.toProviderAddTokenCommand(tokenId);
        sendBlockchainContractService.execute(addTokenArgs, signerId);

        return TokenSpec.initCommand(tokenId, command);
    }
}