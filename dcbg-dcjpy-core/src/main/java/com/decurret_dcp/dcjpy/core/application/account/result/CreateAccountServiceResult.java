package com.decurret_dcp.dcjpy.core.application.account.result;

import com.decurret_dcp.dcjpy.core.application.account.command.CreateAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;

import lombok.Builder;

@Builder
public class CreateAccountServiceResult {
    public final AccountId accountId;
    public final String accountName;
    public final Amount transferLimit;
    public final Amount chargeLimit;
    public final Amount mintLimit;
    public final Amount burnLimit;
    public final Amount cumulativeLimit;

    public static CreateAccountServiceResult initResult(CreateAccountServiceCommand command) {
        return CreateAccountServiceResult.builder()
                .accountId(command.accountId)
                .accountName(command.accountName)
                .transferLimit(command.transferLimit)
                .chargeLimit(command.chargeLimit)
                .mintLimit(command.mintLimit)
                .burnLimit(command.burnLimit)
                .cumulativeLimit(command.cumulativeLimit)
                .build();
    }
}
