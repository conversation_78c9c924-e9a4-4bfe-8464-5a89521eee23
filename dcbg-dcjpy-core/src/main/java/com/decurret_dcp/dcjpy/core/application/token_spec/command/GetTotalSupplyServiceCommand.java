package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class GetTotalSupplyServiceCommand {

    public final TokenId tokenId;

    public GetTotalSupplyServiceCommand(TokenId tokenId) {
        Assertion.assertNotNull(tokenId, "tokenId");
        this.tokenId = tokenId;
    }
}
