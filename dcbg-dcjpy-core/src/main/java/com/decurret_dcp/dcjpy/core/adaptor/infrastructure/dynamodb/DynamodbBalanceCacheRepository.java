package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.dynamodb;

import java.util.Map;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.GetItemRequest;
import software.amazon.awssdk.services.dynamodb.model.GetItemResponse;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;
import software.amazon.awssdk.services.dynamodb.model.ReturnValue;
import software.amazon.awssdk.services.dynamodb.model.UpdateItemRequest;
import software.amazon.awssdk.services.dynamodb.model.UpdateItemResponse;

@Repository
@RequiredArgsConstructor
public class DynamodbBalanceCacheRepository implements BalanceCacheRepository {

    private final DcfConfig config;

    private final DynamoDbClient dynamoDbClient;

    @Override
    public BalanceCache findBalanceCache(AccountId accountId, ZoneId zoneId) {
        String accountIdStr = accountId.getValue();
        String zoneIdStr = zoneId.getValue().toString();

        GetItemRequest getItemRequest = GetItemRequest.builder()
                .tableName(config.getBalanceCacheTable())
                .key(Map.ofEntries(
                             Map.entry("account_id", AttributeValue.builder().s(accountIdStr).build()),
                             Map.entry("zone_id", AttributeValue.builder().n(zoneIdStr).build())
                     )
                )
                .build();
        try {
            GetItemResponse response = dynamoDbClient.getItem(getItemRequest);
            return BalanceCache.of(response.item());
        } catch (DynamoDbException exc) {
            //取得に失敗した場合はシステムエラー
            throw new RuntimeException("Failed to get balance cache", exc);
        }
    }

    @Override
    public BalanceCache addBalanceCache(AccountId accountId, ZoneId zoneId, Amount addAmount) {
        return this.calcBalanceCache(accountId, zoneId, addAmount.getValue().longValue());
    }

    @Override
    public BalanceCache subtractBalanceCache(AccountId accountId, ZoneId zoneId, Amount subtractAmount) {
        return this.calcBalanceCache(accountId, zoneId, subtractAmount.getValue().longValue() * (-1));
    }

    @Override
    public void createBalanceCache(AccountId accountId, ZoneId zoneId) {
        String accountIdStr = accountId.getValue();
        String zoneIdStr = zoneId.getValue().toString();
        String currentDateTime = AppTimeStamp.now().format();

        PutItemRequest putItemRequest = PutItemRequest.builder()
                .tableName(config.getBalanceCacheTable())
                .item(Map.ofEntries(
                              Map.entry("account_id", AttributeValue.builder().s(accountIdStr).build()),
                              Map.entry("zone_id", AttributeValue.builder().n(zoneIdStr).build()),
                              Map.entry("balance", AttributeValue.builder().n("0").build()),
                              Map.entry("updated_at", AttributeValue.builder().s(currentDateTime).build())
                      )
                )
                .build();
        try {
            dynamoDbClient.putItem(putItemRequest);
        } catch (DynamoDbException exc) {
            //作成に失敗した場合はエラー
            throw new RuntimeException("Failed to put balance cache", exc);
        }
    }

    private BalanceCache calcBalanceCache(AccountId accountId, ZoneId zoneId, long amount) {
        UpdateItemRequest request = this.createUpdateBalanceCacheRequest(accountId, zoneId, amount);

        try {
            //更新処理
            UpdateItemResponse response = dynamoDbClient.updateItem(request);
            //更新後の属性取得
            Map<String, AttributeValue> updatedAttribute = response.attributes();
            return BalanceCache.of(updatedAttribute);
        } catch (DynamoDbException exc) {
            throw new RuntimeException(
                    "Failed to update balance cache. accountId = " + accountId.getValue()
                            + ", zoneId = " + zoneId.getValue() + ", amount = " + amount,
                    exc
            );
        }
    }

    /**
     * 残高キャッシュ更新用のリクエスト生成
     * @param accountId 更新対象アカウントID
     * @param zoneId 更新対象ゾーンID
     * @param amount 加減算額(加算の場合:+,減算の場合:-)
     * @return 生成結果
     */
    private UpdateItemRequest createUpdateBalanceCacheRequest(AccountId accountId, ZoneId zoneId, long amount) {
        String accountIdStr = accountId.getValue();
        String zoneIdStr = zoneId.getValue().toString();
        String currentDateTime = AppTimeStamp.now().format();

        return UpdateItemRequest.builder()
                .tableName(config.getBalanceCacheTable())
                .key(Map.ofEntries(
                             Map.entry("account_id", AttributeValue.builder().s(accountIdStr).build()),
                             Map.entry("zone_id", AttributeValue.builder().n(zoneIdStr).build())
                     )
                )
                .conditionExpression("account_id = :expectedAccountId AND zone_id = :expectedZoneId")
                .updateExpression("ADD balance :amount SET updated_at = :currentDateTime")
                .expressionAttributeValues(
                        Map.ofEntries(
                                Map.entry(":expectedAccountId", AttributeValue.builder().s(accountIdStr).build()),
                                Map.entry(":expectedZoneId", AttributeValue.builder().n(zoneIdStr).build()),
                                Map.entry(":amount", AttributeValue.builder().n(String.valueOf(amount)).build()),
                                Map.entry(":currentDateTime", AttributeValue.builder().s(currentDateTime).build())
                        )
                )
                .returnValues(ReturnValue.ALL_NEW)
                .build();
    }
}
