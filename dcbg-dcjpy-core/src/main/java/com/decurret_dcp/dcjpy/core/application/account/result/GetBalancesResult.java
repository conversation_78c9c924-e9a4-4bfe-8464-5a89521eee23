package com.decurret_dcp.dcjpy.core.application.account.result;

import java.util.List;
import java.util.Objects;

import com.decurret_dcp.dcjpy.core.domain.model.account.BalanceOfZone;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetBalancesResult {

    public final Amount totalAmount;
    public final List<BalanceOfZone> balances;

    public GetBalancesResult(Amount totalAmount, List<BalanceOfZone> balances) {
        Assertion.assertNotNull(totalAmount, "totalAmount");
        this.totalAmount = totalAmount;

        if (Objects.isNull(balances)) {
            this.balances = List.of();
        } else {
            this.balances = List.copyOf(balances);
        }
    }
}
