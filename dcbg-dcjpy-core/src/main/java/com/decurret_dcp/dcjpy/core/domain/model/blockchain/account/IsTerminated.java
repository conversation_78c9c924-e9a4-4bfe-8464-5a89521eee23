package com.decurret_dcp.dcjpy.core.domain.model.blockchain.account;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command.IsTerminatedCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.error.ContractError;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class IsTerminated {

    /**
     * アカウントの解約、存在チェック
     *
     * @param accountId アカウントID
     * @param accountIdNotExistMessage 存在しなかった場合のエラーコード
     * @param accountIdTerminatedMessage 解約の場合のエラーコード
     * @param callBlockchainContractService　BlockchainContractService
     * @throws BadRequestException アカウントが存在しない、もしくは解約された場合
     *     引数のaccountIdNotExistMessage 存在しなかった場合エラーコード
     *     引数のaccountIdTerminatedMessage 解約の場合のエラーコード
     */
    public void isTrueOrNotExistThrowBadRequestException(AccountId accountId,
                                                         MessageCode accountIdNotExistMessage,
                                                         MessageCode accountIdTerminatedMessage,
                                                         CallBlockchainContractService callBlockchainContractService)
            throws BadRequestException {
        IsTerminatedCommand isTerminatedCommand = new IsTerminatedCommand(accountId);
        BlockchainCallResponse response = callBlockchainContractService.executeWithoutSignature(isTerminatedCommand);

        Object err = response.data.get("err");
        if (err != null && !"".equals(err.toString())) {
            String errStr = err.toString();
            ContractError contractError = ContractError.getContractError(errStr);
            if (contractError == ContractError.ACCOUNT_ID_NOT_EXIST) {
                throw new BadRequestException(accountIdNotExistMessage);
            }
            throw new BlockchainCallErrorException(errStr);
        }
        Object terminated = response.data.get("terminated");
        if (terminated != null && Boolean.parseBoolean(terminated.toString())) {
            throw new BadRequestException(accountIdTerminatedMessage);
        }
    }
}
