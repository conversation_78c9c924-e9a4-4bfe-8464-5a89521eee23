package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.entity_signer;

import org.seasar.doma.Dao;
import org.seasar.doma.Delete;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySigner;

@ConfigAutowireable
@Dao
public interface EntitySignerDao {
    @Select
    EntitySigner selectByEntityId(EntityId entityId);

    @Insert
    Result<EntitySigner> insert(EntitySigner externalSigner);

    @Delete(sqlFile = true)
    int deleteByEntityId(EntityId entityId);
}
