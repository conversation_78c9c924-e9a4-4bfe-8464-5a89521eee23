package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AccountStatusUserDetailsChecker;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.security.web.firewall.RequestRejectedHandler;

import com.decurret_dcp.dcjpy.core.application.identity_authority.ClientEntityAccessService;
import com.decurret_dcp.dcjpy.core.consts.DCFConst;
import com.decurret_dcp.dcjpy.core.presentation.rest.TraceIdFilter;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@EnableMethodSecurity(prePostEnabled = true)
@EnableWebSecurity
@Configuration
public class SecurityConfiguration {
    private final ConfigurableJWTProcessor<SecurityContext> jwtProcessor;
    private final ClientEntityAccessService clientEntityAccessService;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests()
                .antMatchers(DCFConst.HEALTH_PATH).permitAll()
                .anyRequest()
                .authenticated()
                .and()
                .addFilterAfter(this.zoneIdCacheFilter(), FilterSecurityInterceptor.class)
                .addFilterAfter(this.traceIdFilter(), ChannelProcessingFilter.class)
                .exceptionHandling()
                .authenticationEntryPoint(new AuthenticationEndpoint());

        http.apply(initPreAuthenticationConfigurer());
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        configureCsrf(http);

        return http.build();
    }

    private void configureCsrf(HttpSecurity http) throws Exception {
        http.csrf().disable();
    }

    // 作成したユーザサービス
    @Bean
    public AuthenticationUserDetailsService<PreAuthenticatedAuthenticationToken> authenticationUserDetailsService() {
        return new CoreAuthenticationUserDetailService(jwtProcessor, clientEntityAccessService);
    }

    // フィルター登録
    @Bean
    public PreAuthenticatedAuthenticationProvider preAuthenticatedAuthenticationProvider() {

        var preAuthenticatedAuthenticationProvider = new PreAuthenticatedAuthenticationProvider();
        preAuthenticatedAuthenticationProvider.setPreAuthenticatedUserDetailsService(
                authenticationUserDetailsService());
        preAuthenticatedAuthenticationProvider.setUserDetailsChecker(new AccountStatusUserDetailsChecker());

        return preAuthenticatedAuthenticationProvider;
    }

    @Bean
    public RequestRejectedHandler requestRejectedHandler() {
        return new CustomRequestRejectedHandler();
    }

    private AbstractHttpConfigurer<PreAuthenticatedConfigurer, HttpSecurity> initPreAuthenticationConfigurer() {
        return new PreAuthenticatedConfigurer();
    }

    @Bean
    public ZoneIdCacheFilter zoneIdCacheFilter() {
        return new ZoneIdCacheFilter();
    }

    @Bean
    public TraceIdFilter traceIdFilter() {
        return new TraceIdFilter();
    }

    private static class PreAuthenticatedConfigurer
            extends AbstractHttpConfigurer<PreAuthenticatedConfigurer, HttpSecurity> {
        @Override
        public void configure(HttpSecurity http) throws Exception {
            AuthenticationManager authenticationManager = http.getSharedObject(AuthenticationManager.class);

            var preAuthenticatedProcessingFilter = new CorePreAuthenticatedProcessingFilter();
            preAuthenticatedProcessingFilter.setAuthenticationManager(authenticationManager);

            http.addFilter(preAuthenticatedProcessingFilter);
        }
    }
}
