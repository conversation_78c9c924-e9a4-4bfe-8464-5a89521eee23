package com.decurret_dcp.dcjpy.core.application.contract.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallRequest;
import com.decurret_dcp.dcjpy.core.domain.model.contract.ContractArgs;
import com.decurret_dcp.dcjpy.core.domain.model.contract.ContractMethod;
import com.decurret_dcp.dcjpy.core.domain.model.contract.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class CallContractServiceCommand {
    public final ContractName contractName;
    public final ContractMethod method;
    public final ContractArgs args;

    public BlockchainCallRequest toBlockchainCallRequest() {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        return BlockchainCallRequest
                .builder()
                .zoneId(zoneId.getValue().toString())
                .contractName(this.contractName.getValue())
                .method(this.method.getValue())
                .args(this.args.value)
                .build();
    }
}
