package com.decurret_dcp.dcjpy.core.domain.model.balance_cache;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

@Builder
public class BalanceCache {
    public final AccountId accountId;

    public final ZoneId zoneId;

    public final Balance balance;

    public final LocalDateTime updatedAt;

    public static BalanceCache of(Map<String, AttributeValue> attributeValueMap) {
        return BalanceCache.builder()
                .accountId(new AccountId(attributeValueMap.get("account_id").s()))
                .zoneId(ZoneId.of(Integer.valueOf(attributeValueMap.get("zone_id").n())))
                .balance(new Balance(BigInteger.valueOf(Long.parseLong(attributeValueMap.get("balance").n()))))
                .updatedAt(
                        LocalDateTime.parse(attributeValueMap.get("updated_at").s(),
                                            DateTimeFormatter.ISO_DATE_TIME)
                )
                .build();
    }

}
