package com.decurret_dcp.dcjpy.core.application.account.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;

import lombok.Builder;

@Builder
public class UpdateAccountStatusResult {

    public final AccountId accountId;

    public final String accountName;

    public final AccountStatus accountStatus;

    public final ReasonCode reasonCode;
}
