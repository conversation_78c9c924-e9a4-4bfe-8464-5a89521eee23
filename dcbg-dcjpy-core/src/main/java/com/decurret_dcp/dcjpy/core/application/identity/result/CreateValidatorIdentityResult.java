package com.decurret_dcp.dcjpy.core.application.identity.result;

import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientId;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientSecret;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerIdNullable;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class CreateValidatorIdentityResult {
    public final ValidatorId validatorId;
    public final IssuerIdNullable issuerId;
    public final ClientId clientId;
    public final ClientSecret clientSecret;
}
