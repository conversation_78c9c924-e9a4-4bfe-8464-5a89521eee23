package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command.SetAccountStatusCommand;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.Builder;

@Builder
public class UpdateAccountStatusServiceCommand {

    public final IssuerId issuerId;
    public final AccountId accountId;
    public final AccountStatus toAccountStatus;
    public final ReasonCode reasonCode;

    public SetAccountStatusCommand toSetAccountStatusCommand() {
        return new SetAccountStatusCommand(
                this.issuerId,
                this.accountId,
                this.toAccountStatus,
                this.reasonCode
        );
    }
}
