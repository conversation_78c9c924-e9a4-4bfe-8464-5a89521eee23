package com.decurret_dcp.dcjpy.core.application.token_spec.result;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.decurret_dcp.dcjpy.core.application.token_spec.command.GetNftListServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.result.RenewableEnergyTokenGetTokenListResult;
import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataEntity;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetaDataDetail;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class GetNftListResult {

    public final List<GetNftResult> nfts;
    public final PagingResult pager;

    @ToString
    @EqualsAndHashCode
    @Builder
    public static class PagingResult {

        public final Offset offset;

        public final Limit limit;

        public final TotalCount total;
    }

    public static GetNftListResult createResult(
            GetNftListServiceCommand command, RenewableEnergyTokenGetTokenListResult result,
            List<NftMetadataEntity> nftMetadataList
    ) {

        // ページング情報をセット
        PagingResult pager = PagingResult.builder()
                .offset(command.offset)
                .limit(command.limit)
                .total(result.totalCount)
                .build();

        // NFT一覧がない場合は、ページング情報とNFT一覧情報を空配列で返却
        if (nftMetadataList == null) {
            return GetNftListResult.builder()
                    .nfts(List.of())
                    .pager(pager)
                    .build();
        }

        // メタデータID と メタデータ詳細 をセットしたMapを生成
        Map<MetadataId, MetaDataDetail> nftMap = nftMetadataList.stream()
                .collect(Collectors.toMap(nft -> nft.metadataId, nft -> nft.metadataDetail));

        // メタデータ詳細を付与したオブジェクトを生成する
        List<GetNftResult> nfts = result.renewableEnergyTokenList.stream()
                .map(nft -> {
                    MetaDataDetail metaDataDetail = nftMap.get(nft.metadataId.metadataId());
                    if (metaDataDetail == null) {
                        throw new IllegalArgumentException(
                                "metaDataDetail is not matched. metadataId = " + nft.metadataId.getValue()
                        );
                    }

                    return GetNftResult.createResult(nft, metaDataDetail);
                }).toList();

        return GetNftListResult.builder()
                .nfts(nfts)
                .pager(pager)
                .build();
    }
}
