package com.decurret_dcp.dcjpy.core.config;

import java.io.IOException;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.decurret_dcp.dcjpy.core.application.clock.ClockService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * システム時刻を変更するFilter.
 * <p>
 * リクエスト単位で {@link ClockService} から得られるシステム時刻を変更する。
 * HTTP リクエストヘッダ <code>X-DCF-System-Clock</code> に UNIX 時間(ミリ秒)で設定する。
 */
@Component
@Slf4j
@AllArgsConstructor
public class SystemClockChangeFilter extends OncePerRequestFilter {

    public static final String SYSTEM_CLOCK_HEADER_NAME = "X-DCF-System-Clock";

    private final DebugConfigurationProperties debugConfigurationProperties;

    private final ClockService clockService;

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        // system.changeClockがプロパティに無い、または無効な文字列、またはfalseの場合はフィルターチェーンを継続して終了
        if (!debugConfigurationProperties.isInjectionSystemDateEnabled()) {
            filterChain.doFilter(request, response);
            return;
        }

        String headerValue = request.getHeader(SYSTEM_CLOCK_HEADER_NAME);

        if (headerValue != null) {
            log.info("HTTP header '{}' found", SYSTEM_CLOCK_HEADER_NAME);
            try {
                long epochMilli = Long.parseLong(headerValue);
                Instant instant = Instant.ofEpochMilli(epochMilli);
                Clock clock = Clock.fixed(instant, ZoneId.systemDefault());
                clockService.setClock(clock);
                log.info("Setting system clock: {}", clock);
            } catch (Exception e) {
                log.info("Failed to set system clock. (reason: {})", e.getMessage());
            }
        }

        filterChain.doFilter(request, response);
    }
}
