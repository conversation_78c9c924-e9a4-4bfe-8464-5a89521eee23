package com.decurret_dcp.dcjpy.core.config;

import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.regex.Pattern;

import org.seasar.doma.boot.autoconfigure.DomaConfigBuilder;
import org.seasar.doma.boot.autoconfigure.DomaProperties;
import org.seasar.doma.jdbc.Sql;
import org.seasar.doma.jdbc.UtilLoggingJdbcLogger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class DomaConfig {
    @Bean
    public DomaConfigBuilder domaConfigBuilder(DomaProperties properties) {
        DomaConfigBuilder builder = new DomaConfigBuilder(properties);
        builder.jdbcLogger(new CoreJdbcLogger(Level.FINE));
        return builder;
    }

    /**
     * SQLの実行クエリのログ出力を1行で出力させるためロガーWrapper.
     */
    private static class CoreJdbcLogger extends UtilLoggingJdbcLogger {

        private static final ThreadLocal<Long> START_TIME = new ThreadLocal<>();

        private static final Pattern PATTERN = Pattern.compile("( *\n *)+");

        private CoreJdbcLogger(Level level) {
            super(level);
        }

        @Override
        protected void logSql(String callerClassName, String callerMethodName, Sql<?> sql, Level level,
                              Supplier<String> messageSupplier) {
            super.logSql(callerClassName, callerMethodName, sql, Level.INFO, messageSupplier);
        }

        @Override
        protected String getSqlText(Sql<?> sql) {
            String originalSql = sql.getRawSql();
            if (originalSql == null) {
                return null;
            }

            return PATTERN.matcher(originalSql).replaceAll(" ");
        }

        @Override
        public void logDaoMethodEntering(
                String callerClassName, String callerMethodName, Object... args) {
            START_TIME.set(Long.valueOf(System.currentTimeMillis()));
        }

        @Override
        public void logDaoMethodExiting(String callerClassName, String callerMethodName, Object result) {
            long start =  START_TIME.get();
            long elapsed = System.currentTimeMillis() - start;
            log.info("SQL exited : {}.{}() : {} ms", callerClassName, callerMethodName, Long.valueOf(elapsed));
            START_TIME.remove();
        }

        @Override
        public void logDaoMethodThrowing(
                String callerClassName, String callerMethodName, RuntimeException e) {
            long start =  START_TIME.get();
            long elapsed = System.currentTimeMillis() - start;
            log.info("SQL throw exception : {}.{}() : {} ms", callerClassName, callerMethodName, Long.valueOf(elapsed));
            START_TIME.remove();
        }
    }
}
