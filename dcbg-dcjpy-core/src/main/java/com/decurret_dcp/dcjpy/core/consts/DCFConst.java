package com.decurret_dcp.dcjpy.core.consts;

import java.math.BigInteger;

public class DCFConst {

    public static final String DATETIME_FORMAT_API = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String TIMEZONE_API = "Asia/Tokyo";
    public static final String TIMEZONE_DB = "GMT";

    public static final String HEALTH_PATH = "/actuator/health";
    public static final String HEALTH_CREDENTIAL = "Health Credential";

    public static final BigInteger REGION_ID_COMMON = new BigInteger("3000");

    public static final String ESCROW_ACCOUNT_PREFIX = "6100";
    public static final String EXCHANGE_FROM = "ExchangeFrom";
    public static final String EXCHANGE_TO = "ExchangeTo";
    public static final String BURN = "Burn";

    // 9を18桁。Long.MAXを超えない9を列挙した形
    public static final Long MAX_PERIOD = 999999999999999999L;

    public static final String ID_NOT_FOUND_SUFFIX = " not found";
    public static final String TX_HASH_PREFIX = " transactionHash: ";
}
