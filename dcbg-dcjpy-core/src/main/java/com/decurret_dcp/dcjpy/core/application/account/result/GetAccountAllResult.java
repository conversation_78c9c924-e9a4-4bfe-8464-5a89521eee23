package com.decurret_dcp.dcjpy.core.application.account.result;

import java.math.BigInteger;
import java.util.List;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.Account;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountAllResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class GetAccountAllResult {

    public final Account account;

    public final List<BizZoneAccount> bizZoneAccounts;

    public final Balance totalBalance;

    @Builder
    public static class BizZoneAccount {

        public final String accountName;
        public final AccountStatus accountStatus;
        public final Balance balance;
        public final ZoneId zoneId;
        public final String zoneName;
        public final AppTimeStamp appliedAt;
        public final AppTimeStamp registeredAt;
        public final AppTimeStamp terminatingAt;
        public final AppTimeStamp terminatedAt;

        public static BizZoneAccount initBizZoneAccount(
                ValidatorGetAccountAllResult.BizZoneAccount contractBizAccount) {
            return BizZoneAccount.builder()
                    .accountName(contractBizAccount.accountName)
                    .accountStatus(AccountStatus.of(contractBizAccount.accountStatus.getValue()))
                    .balance(contractBizAccount.balance)
                    .zoneId(contractBizAccount.zoneId)
                    .zoneName(contractBizAccount.zoneName)
                    .appliedAt(AppTimeStamp.of(contractBizAccount.appliedAt))
                    .registeredAt(AppTimeStamp.of(contractBizAccount.registeredAt))
                    .terminatingAt(AppTimeStamp.of(contractBizAccount.terminatingAt))
                    .terminatedAt(AppTimeStamp.of(contractBizAccount.terminatedAt))
                    .build();
        }
    }

    public static GetAccountAllResult initResult(
            AccountId accountId, ValidatorGetAccountAllResult contractResult, Balance cacheBalance,
            AppTimeStamp cumulativeDate, Amount cumulativeAmount
    ) {

        ValidatorGetAccountAllResult.AccountData accountData = contractResult.accountData;
        Balance totalBalance = calcTotalBalance(contractResult.accountData);

        Account account = Account.builder()
                .accountId(accountId)
                .accountName(accountData.accountName)
                .accountStatus(AccountStatus.of(accountData.accountStatus.getValue()))
                .reasonCode(new ReasonCode(accountData.reasonCode.value))
                .zoneId(accountData.zoneId)
                .zoneName(accountData.zoneName)
                .balance(accountData.balance)
                .cacheBalance(cacheBalance)
                .mintLimit(accountData.mintLimit)
                .burnLimit(accountData.burnLimit)
                .chargeLimit(accountData.chargeLimit)
                .transferLimit(accountData.transferLimit)
                .cumulativeLimit(accountData.cumulativeLimit)
                .cumulativeAmount(cumulativeAmount)
                .cumulativeDate(cumulativeDate)
                .appliedAt(AppTimeStamp.of(accountData.appliedAt))
                .registeredAt(AppTimeStamp.of(accountData.registeredAt))
                .terminatingAt(AppTimeStamp.of(accountData.terminatingAt))
                .terminatedAt(AppTimeStamp.of(accountData.terminatedAt))
                .build();

        List<BizZoneAccount> bizZoneAccounts = accountData.businessZoneAccounts.stream()
                .map(BizZoneAccount::initBizZoneAccount)
                .toList();

        return GetAccountAllResult.builder()
                .account(account)
                .bizZoneAccounts(bizZoneAccounts)
                .totalBalance(totalBalance)
                .build();
    }

    private static Balance calcTotalBalance(ValidatorGetAccountAllResult.AccountData accountData) {

        //FinZoneの足し算
        BigInteger totalBalanceValue = accountData.balance.getValue();
        //BizZoneの足し算
        for (ValidatorGetAccountAllResult.BizZoneAccount bizZoneAccount : accountData.businessZoneAccounts) {
            totalBalanceValue = totalBalanceValue.add(bizZoneAccount.balance.getValue());
        }
        return new Balance(totalBalanceValue);
    }
}
