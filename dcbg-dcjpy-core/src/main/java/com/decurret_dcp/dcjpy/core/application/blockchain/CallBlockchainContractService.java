package com.decurret_dcp.dcjpy.core.application.blockchain;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.sign.GenerateExternalSignService;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainClient;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CallBlockchainContractService {

    private final BlockchainClient blockchainClient;

    private final GenerateExternalSignService generateExternalSignService;

    /**
     * 外部署名つきで/callリクエストする
     * @param command
     * @param signerId
     * @return レスポンス
     */
    public BlockchainCallResponse execute(BlockchainContractCommand command, EntityId signerId) {

        Assertion.assertNotNull(command, "command");

        BlockchainCallRequest request = generateExternalSignService.createBlockchainCallRequest(command, signerId);

        return blockchainClient.call(request);
    }

    /**
     * 外部署名なしで/callリクエストする
     * @param command
     * @return レスポンス
     */
    public BlockchainCallResponse executeWithoutSignature(BlockchainContractCommand command) {

        Assertion.assertNotNull(command, "command");
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        BlockchainCallRequest request = BlockchainCallRequest.builder()
                .zoneId((zoneId != null) ? zoneId.getValue().toString() : "")
                .contractName(command.getContractName().getValue())
                .method(command.getMethod())
                .args(createContractArgs(command))
                .build();

        return blockchainClient.call(request);
    }

    private Map<String, Object> createContractArgs(BlockchainContractCommand command) {
        Map<String, Object> args = new HashMap<>();
        command.getArgs().forEach((name, value) -> args.put(name, value.getValue()));
        return args;
    }
}
