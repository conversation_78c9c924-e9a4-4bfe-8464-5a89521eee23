package com.decurret_dcp.dcjpy.core.domain.model.blockchain.account;

import java.util.function.BiConsumer;

import org.springframework.stereotype.Component;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command.HasAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.error.ContractError;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

@Component
public class HasAccount {

    public void execute(
            AccountId accountId, MessageCode accountIdNotExistMessage,
            BiConsumer<ContractError, MessageCode> biConsumer, CallBlockchainContractService bcService
    ) {
        HasAccountCommand command = new HasAccountCommand(accountId);
        BlockchainCallResponse response = bcService.executeWithoutSignature(command);

        if (response.isSuccess()) {
            return;
        }

        String errStr = response.getError();
        ContractError contractError = ContractError.getContractError(errStr);
        if (contractError != null) {
            biConsumer.accept(contractError, accountIdNotExistMessage);
        }

        throw new BlockchainCallErrorException(errStr);
    }

    public void executeAndReturnNotExist(AccountId accountId, CallBlockchainContractService bcService) {
        // chkEnabled をtrueで呼ぶ想定はないためfalse固定とする
        HasAccountCommand command = new HasAccountCommand(accountId);
        BlockchainCallResponse response = bcService.executeWithoutSignature(command);

        Object err = response.data.get("err");
        if (err == null || "".equals(err.toString())) {
            // ACCOUNTが存在する場合エラー
            throw new BadRequestException(MessageCode.ACCOUNT_ID_ALREADY_EXISTS);

        } else {
            String errStr = err.toString();
            ContractError contractError = ContractError.getContractError(errStr);
            if (contractError != null) {
                switch (contractError) {
                    case ACCOUNT_ID_NOT_EXIST:
                        // ACCOUNT_ID が存在しない場合は正常
                        return;
                    case ACCOUNT_INVALID_VALUE:
                        throw new NotFoundException(MessageCode.ACCOUNT_ID_NOT_FOUND);
                    default:
                }
            }
            throw new BlockchainCallErrorException(errStr);
        }
    }

    public static class RequestSwitch implements BiConsumer<ContractError, MessageCode> {
        @Override
        public void accept(ContractError t, MessageCode accountIdNotExistMessage) {
            if (t == ContractError.ACCOUNT_ID_NOT_EXIST) {
                throw new BadRequestException(accountIdNotExistMessage);
            }
        }
    }

    public static class PathValueSwitch implements BiConsumer<ContractError, MessageCode> {
        @Override
        public void accept(ContractError t, MessageCode accountIdNotExistMessage) {
            if (t == ContractError.ACCOUNT_ID_NOT_EXIST) {
                throw new NotFoundException(accountIdNotExistMessage);
            }
        }
    }
}
