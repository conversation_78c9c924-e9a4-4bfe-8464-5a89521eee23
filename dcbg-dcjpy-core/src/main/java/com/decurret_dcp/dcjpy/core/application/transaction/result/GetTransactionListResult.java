package com.decurret_dcp.dcjpy.core.application.transaction.result;

import java.util.List;

import org.seasar.doma.jdbc.SelectOptions;

import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public class GetTransactionListResult {

    public final List<TransactionResult> transactions;
    public final PagingResult pager;

    public GetTransactionListResult(List<TransactionResult> transactions, PagingResult pager) {
        Assertion.assertNotNull(pager.total, "totalCount");

        this.transactions = transactions;
        this.pager = pager;
    }

    @ToString
    @EqualsAndHashCode
    @Builder
    public static class PagingResult {

        public final Offset offset;

        public final Limit limit;

        public final TotalCount total;

        public static PagingResult create(Offset offset, Limit limit, SelectOptions options) {
            return PagingResult.builder()
                    .offset(offset)
                    .limit(limit)
                    .total(new TotalCount(options.getCount()))
                    .build();
        }
    }
}
