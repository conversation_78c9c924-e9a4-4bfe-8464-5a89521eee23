package com.decurret_dcp.dcjpy.core.application.account.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.result.FinancialCheckGetBizAccountStatusResult;

import lombok.Builder;

@Builder
public class GetBizAccountStatusResult {

    public final AccountId accountId;

    public final AccountStatus accountStatus;

    public static GetBizAccountStatusResult initResult(
            AccountId accountId, FinancialCheckGetBizAccountStatusResult result
    ) {
        return GetBizAccountStatusResult.builder()
                .accountId(accountId)
                .accountStatus(AccountStatus.of(result.accountStatus.getValue()))
                .build();
    }
}
