package com.decurret_dcp.dcjpy.core.application.contract.command;

import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendRequest;
import com.decurret_dcp.dcjpy.core.domain.model.contract.ContractArgs;
import com.decurret_dcp.dcjpy.core.domain.model.contract.ContractMethod;
import com.decurret_dcp.dcjpy.core.domain.model.contract.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.RequestIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class SendContractServiceCommand {
    public final ContractName contractName;
    public final ContractMethod method;
    public final ContractArgs args;

    public BlockchainSendRequest toBlockchainSendRequest() {
        RequestId requestId = RequestIdThreadLocalHolder.getRequestId();
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        return BlockchainSendRequest
                .builder()
                .requestId(requestId.getValue())
                .zoneId(zoneId.getValue().toString())
                .contractName(this.contractName.getValue())
                .method(this.method.getValue())
                .args(this.args.value)
                .build();
    }
}
