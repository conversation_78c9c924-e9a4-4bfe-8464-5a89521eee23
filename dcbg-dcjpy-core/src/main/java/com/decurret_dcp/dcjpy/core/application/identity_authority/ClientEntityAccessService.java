package com.decurret_dcp.dcjpy.core.application.identity_authority;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntity;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntityRepository;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ClientEntityAccessService {

    private final ClientEntityRepository clientEntityRepository;

    @Transactional(readOnly = true)
    public ClientEntity getByClientId(ClientId clientId) {
        return clientEntityRepository.selectByClientId(clientId);
    }
}
