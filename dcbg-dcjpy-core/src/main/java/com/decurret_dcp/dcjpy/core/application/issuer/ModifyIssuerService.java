package com.decurret_dcp.dcjpy.core.application.issuer;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.issuer.command.ModifyIssuerServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.HasIssuer;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.ModIssuerCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ModifyIssuerService {

    private final HasIssuer hasIssuer;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * イシュアの変更を行います
     *
     * @param command コマンド
     * @throws BadRequestException イシュアの変更に失敗した場合
     *     E0017 issuer_idが見つかりません
     *     E0016 issuer_idが無効です
     */
    @Transactional(readOnly = true)
    public void execute(ModifyIssuerServiceCommand command) throws BadRequestException {
        // 発行者ID確認
        IssuerId issuerId = command.issuerId;
        hasIssuer.execute(issuerId, new HasIssuer.PathValueSwitch(), callBlockchainContractService);

        // 発行者変更
        ModIssuerCommand modIssuerCommand = new ModIssuerCommand(issuerId, command.issuerName);
        EntityId signerId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(modIssuerCommand, signerId);
    }
}
