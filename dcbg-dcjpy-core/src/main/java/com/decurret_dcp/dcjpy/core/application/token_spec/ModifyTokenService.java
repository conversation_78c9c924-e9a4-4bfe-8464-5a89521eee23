package com.decurret_dcp.dcjpy.core.application.token_spec;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.ModifyTokenServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ModTokenCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderHasToken;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ModifyTokenService {

    private final ProviderHasToken hasToken;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * トークンの更新を行います
     *
     * @param command コマンド
     * @throws BadRequestException トークンの更新に失敗した場合
     *    E0037 token_idが見つかりません
     *    E0036 token_idが無効です
     */
    @Transactional(readOnly = true)
    public void execute(ModifyTokenServiceCommand command) throws BadRequestException {
        // トークンID確認
        TokenId tokenId = command.tokenId;
        hasToken.execute(tokenId, command.providerId, true, new ProviderHasToken.PathValueSwitch(),
                         callBlockchainContractService);

        // トークン変更
        ModTokenCommand modTokenCommand = command.toModTokenCommand();
        EntityId signerId = new EntityId(command.providerId);
        sendBlockchainContractService.execute(modTokenCommand, signerId);
    }
}
