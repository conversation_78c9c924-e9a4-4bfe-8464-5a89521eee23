package com.decurret_dcp.dcjpy.core.application.provider.command;

import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.Eoa;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class AddProviderRoleServiceCommand {
    public final ProviderId providerId;
    public final Eoa eoa;

    public AddProviderRoleServiceCommand(ProviderId providerId, Eoa eoa) {
        Assertion.assertNotNull(providerId, "providerId");
        Assertion.assertNotNull(eoa, "eoa");
        this.providerId = providerId;
        this.eoa = eoa;
    }
}
