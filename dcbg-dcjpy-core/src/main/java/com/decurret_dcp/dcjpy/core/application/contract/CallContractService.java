package com.decurret_dcp.dcjpy.core.application.contract;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import com.decurret_dcp.dcjpy.core.application.contract.command.CallContractServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainClient;
import com.decurret_dcp.dcjpy.core.domain.model.contract.InvalidContractParameterException;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class CallContractService {

    private final BlockchainClient blockchainClient;

    public BlockchainCallResponse execute(CallContractServiceCommand command) {

        BlockchainCallRequest request = command.toBlockchainCallRequest();

        try {
            return blockchainClient.call(request);
        } catch (HttpClientErrorException exc) {
            HttpStatus httpStatus = exc.getStatusCode();
            // 指定されたパラメータに不備があった場合
            if (httpStatus == HttpStatus.BAD_REQUEST) {
                throw new InvalidContractParameterException(exc.getResponseBodyAsString(), exc);
            }

            throw exc;
        }
    }
}
