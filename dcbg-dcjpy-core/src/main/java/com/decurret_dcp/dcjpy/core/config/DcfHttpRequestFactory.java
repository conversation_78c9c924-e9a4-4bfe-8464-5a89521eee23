package com.decurret_dcp.dcjpy.core.config;

import java.util.function.Supplier;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;

@Component
public class DcfHttpRequestFactory implements Supplier<ClientHttpRequestFactory> {

    private final HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory;

    public DcfHttpRequestFactory(DcfConfig dcfConfig) {
        PoolingHttpClientConnectionManager poolingManager = new PoolingHttpClientConnectionManager();
        poolingManager.setMaxTotal(dcfConfig.getHttpConnectionMaxTotal());
        poolingManager.setDefaultMaxPerRoute(dcfConfig.getHttpConnectionMaxPerRoute());

        CloseableHttpClient client = HttpClientBuilder.create()
                .setConnectionManager(poolingManager)
                .build();

        this.httpComponentsClientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(client);
    }

    @Override
    public ClientHttpRequestFactory get() {
        return this.httpComponentsClientHttpRequestFactory;
    }
}
