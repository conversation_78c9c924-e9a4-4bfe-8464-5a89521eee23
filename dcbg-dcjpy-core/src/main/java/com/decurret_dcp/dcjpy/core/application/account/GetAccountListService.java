package com.decurret_dcp.dcjpy.core.application.account;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.GetAccountListServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.GetAccountListResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.Account;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccountList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountListResult;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class GetAccountListService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final ValidatorGetAccountList validatorGetAccountList;

    /**
     * 口座一覧を取得します
     *
     * @param command 口座一覧取得コマンド
     * @return 口座一覧取得結果
     */
    @Transactional(readOnly = true)
    public GetAccountListResult execute(GetAccountListServiceCommand command) {

        // Validator#getAccountListを取得
        ValidatorGetAccountListResult result = validatorGetAccountList.execute(
                command.validatorId, command, callBlockchainContractService);

        if (Objects.isNull(result.accounts) || result.accounts.length == 0) {
            return GetAccountListResult.builder()
                    .accounts(Collections.emptyList())
                    .offset(command.offset)
                    .limit(command.limit)
                    .totalCount(result.total)
                    .build();
        }

        List<Account> accounts = Arrays.stream(result.accounts)
                .map(item -> Account.builder()
                        .accountId(item.accountId.accountId())
                        .accountName(item.accountName)
                        .accountStatus(AccountStatus.of(item.accountStatus.getValue()))
                        .reasonCode(new ReasonCode(item.reasonCode.value))
                        .balance(item.balance)
                        .appliedAt(AppTimeStamp.of(item.appliedAt))
                        .registeredAt(AppTimeStamp.of(item.registeredAt))
                        .terminatingAt(AppTimeStamp.of(item.terminatingAt))
                        .terminatedAt(AppTimeStamp.of(item.terminatedAt))
                        .build()
                ).toList();

        return GetAccountListResult.builder()
                .accounts(accounts)
                .offset(command.offset)
                .limit(command.limit)
                .totalCount(result.total)
                .build();
    }
}
