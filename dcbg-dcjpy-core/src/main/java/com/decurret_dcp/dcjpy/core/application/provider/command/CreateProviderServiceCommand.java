package com.decurret_dcp.dcjpy.core.application.provider.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ProviderAddProviderCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class CreateProviderServiceCommand {
    public final AdminId adminId;
    public final String zoneName;

    public ProviderAddProviderCommand initContractCommand(ProviderId providerId, ZoneId zoneId) {
        return new ProviderAddProviderCommand(providerId, zoneId, this.zoneName);
    }
}
