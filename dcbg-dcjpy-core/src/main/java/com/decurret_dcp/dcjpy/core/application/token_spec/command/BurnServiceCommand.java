package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.TokenBurnCommand;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class BurnServiceCommand {

    public final IssuerId issuerId;

    public final AccountId accountId;

    public final boolean fullBurn;

    public final Amount burnAmount;

    public TokenBurnCommand toTokenBurnCommand(Amount contractBurnAmount) {
        return new TokenBurnCommand(
                this.issuerId,
                this.accountId,
                contractBurnAmount
        );
    }
}
