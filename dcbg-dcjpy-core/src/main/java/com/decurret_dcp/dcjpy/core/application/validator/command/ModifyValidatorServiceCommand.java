package com.decurret_dcp.dcjpy.core.application.validator.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.ModValidatorCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorName;

import lombok.Builder;

@Builder
public class ModifyValidatorServiceCommand {
    public final AdminId adminId;
    public final ValidatorId validatorId;
    public final ValidatorName validatorName;

    public ModValidatorCommand toModValidatorCommand() {
        return new ModValidatorCommand(this.validatorId, this.validatorName);
    }
}
