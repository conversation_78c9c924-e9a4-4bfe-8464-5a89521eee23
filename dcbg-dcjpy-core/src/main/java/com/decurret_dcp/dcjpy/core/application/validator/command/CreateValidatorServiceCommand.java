package com.decurret_dcp.dcjpy.core.application.validator.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddValidatorCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerIdNullable;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorName;

import lombok.Builder;

@Builder
public class CreateValidatorServiceCommand {

    public final AdminId adminId;

    public final ValidatorName validatorName;

    public final IssuerIdNullable issuerId;

    public AddValidatorCommand toAddValidatorCommand(ValidatorId validatorId) {
        return new AddValidatorCommand(validatorId, this.validatorName, this.issuerId);
    }
}
