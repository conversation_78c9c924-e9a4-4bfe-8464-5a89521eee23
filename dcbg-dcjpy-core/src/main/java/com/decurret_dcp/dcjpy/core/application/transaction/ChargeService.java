package com.decurret_dcp.dcjpy.core.application.transaction;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.ChargeServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.ChargeResult;
import com.decurret_dcp.dcjpy.core.application.transaction.result.CheckChargeResult;
import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.CheckExchange;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckExchangeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.JpyTokenTransferCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetZoneByAccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetZoneByAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendResponse;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class ChargeService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final ClockService clockService;
    private final CheckExchange checkExchange;
    private final ValidatorGetAccount validatorGetAccount;
    private final ProviderGetZone providerGetZone;
    private final ValidatorGetZoneByAccountId validatorGetZoneByAccountId;
    private final DcfConfig config;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * コインのチャージ前確認を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コインのチャージ前確認がNGの場合
     *    E0065 このto_zoneは指定出来ません
     *    E0005 残高が不足しています
     *    E0001 account_idが見つかりません
     *    E0059 account_idが本人未確認です
     *    E0045 account_idが無効です
     *    E0025 charge_amountが1日の上限を超えています
     *    E0073 charge_amountが上限を超えています
     */
    @Transactional(readOnly = true)
    public CheckChargeResult check(ChargeServiceCommand command) throws BadRequestException {
        ZoneId fromZoneId = ZoneIdThreadLocalHolder.getZoneId();

        // アカウントのチェック
        // チャージ前確認
        return this.doCheck(command, fromZoneId);
    }

    private CheckChargeResult doCheck(ChargeServiceCommand command, ZoneId fromZoneId) {
        // FinZone ではない場合はチャージを実行できない
        if (fromZoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        if (fromZoneId.getValue().equals(command.toZoneId.getValue())) {
            throw new BadRequestException(MessageCode.ZONE_ID_INVALID);
        }

        // accountチェック
        ValidatorGetAccountResult accountResult =
                validatorGetAccount.execute(command.validatorId, command.accountId, callBlockchainContractService);

        // zone情報を取得
        Map<Integer, String> zonesMap = getZonesMapByAccountId(command.validatorId, command.accountId);
        if ((zonesMap.containsKey(fromZoneId.getValue()) == false)
                || (zonesMap.containsKey(command.toZoneId.getValue()) == false)) {
            throw new BadRequestException(MessageCode.ZONE_ID_INVALID);
        }

        // FinZoneでのAccountの有効性の確認を行う
        CheckExchangeCommand checkExchangeCommand = command.toCheckCommand(fromZoneId);
        checkExchange.execute(checkExchangeCommand, callBlockchainContractService);

        // 残高キャッシュ確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, fromZoneId);
        Balance finZoneBalance = balanceCache.balance;
        if (command.chargeAmount.value.compareTo(finZoneBalance.value) > 0) {
            throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
        }

        return CheckChargeResult.builder()
                .accountId(command.accountId)
                .accountName(accountResult.accountData.accountName)
                .fromZoneId(fromZoneId)
                .fromZoneName(zonesMap.get(fromZoneId.getValue()))
                .toZoneId(command.toZoneId)
                .toZoneName(zonesMap.get(command.toZoneId.getValue()))
                .chargeAmount(command.chargeAmount)
                .build();
    }

    /**
     * コインのチャージを行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コインのチャージがNGの場合
     *    E0065 このto_zoneは指定出来ません
     *    E0028 provider_idが見つかりません
     *    E0005 残高が不足しています
     *    E0001 account_idが見つかりません
     *    E0059 account_idが本人未確認です
     *    E0045 account_idが無効です
     *    E0025 charge_amountが1日の上限を超えています
     *    E0073 charge_amountが上限を超えています
     */
    @Transactional(readOnly = true)
    public ChargeResult execute(ChargeServiceCommand command) throws BadRequestException {
        ZoneId fromZoneId = ZoneIdThreadLocalHolder.getZoneId();

        // アカウントのチェック
        // チャージ前確認
        CheckChargeResult checkChargeResult = this.doCheck(command, fromZoneId);

        // コインのチャージ実行
        BlockchainSendResponse blockchainSendResponse = this.transfer(command, fromZoneId);

        // 残高更新
        BalanceCache updatedBalancerCache;
        try {
            updatedBalancerCache = this.balanceCacheRepository.subtractBalanceCache(
                    command.accountId, fromZoneId, command.chargeAmount
            );
        } catch (RuntimeException exc) {
            log.error("Failed to subtract balance cache after charged. {}", command, exc);
            throw exc;
        }

        return ChargeResult.builder()
                .accountId(command.accountId)
                .accountName(checkChargeResult.accountName)
                .fromZoneId(checkChargeResult.fromZoneId)
                .fromZoneName(checkChargeResult.fromZoneName)
                .toZoneId(command.toZoneId)
                .toZoneName(checkChargeResult.toZoneName)
                .chargeAmount(command.chargeAmount)
                .balance(updatedBalancerCache.balance)
                .transactionHash(new TransactionHash(blockchainSendResponse.transactionHash))
                .build();
    }

    /**
     * 対象アカウントに関連するゾーン情報をMap形式で返却する
     *
     * @param validatorId バリデータID
     * @param accountId アカウントId
     * @return ゾーン情報マップ
     */
    private Map<Integer, String> getZonesMapByAccountId(ValidatorId validatorId, AccountId accountId) {
        Map<Integer, String> zonesMap = new HashMap<>();
        // 自身のゾーン取得
        ProviderGetZoneResult currentZoneResult = providerGetZone.execute(callBlockchainContractService);
        zonesMap.put(currentZoneResult.zoneId.getValue(), currentZoneResult.zoneName);

        // 連携済みのゾーン情報取得
        ValidatorGetZoneByAccountIdResult getZoneByAccountIdResult
                = validatorGetZoneByAccountId.execute(validatorId, accountId, callBlockchainContractService);
        Map<Integer, String> getZoneByAccountIdResultMap = getZoneByAccountIdResult.zones.stream()
                .collect(Collectors.toMap(zone -> zone.zoneId.getValue(), zone -> zone.zoneName));
        zonesMap.putAll(getZoneByAccountIdResultMap);

        return zonesMap;
    }

    private BlockchainSendResponse transfer(ChargeServiceCommand command, ZoneId fromZoneId)
            throws BadRequestException {

        // 現在時刻+timeoutTimestamp(秒)のUNIX TIMEをタイムアウト値にする
        long unixTime = clockService.instant().getEpochSecond() + config.getTimeoutTimestamp();

        JpyTokenTransferCommand transferCommand = command.toTransferCommand(fromZoneId, unixTime);

        EntityId entityId = new EntityId(command.validatorId);

        return sendBlockchainContractService.execute(transferCommand, entityId);
    }
}
