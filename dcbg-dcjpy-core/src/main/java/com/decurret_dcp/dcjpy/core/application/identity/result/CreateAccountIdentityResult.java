package com.decurret_dcp.dcjpy.core.application.identity.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class CreateAccountIdentityResult {
    public final AccountId accountId;

    public CreateAccountIdentityResult(AccountId accountId) {
        Assertion.assertNotNull(accountId, "accountId");
        this.accountId = accountId;
    }
}
