package com.decurret_dcp.dcjpy.core.application.token_spec.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.result.RenewableEnergyTokenGetTokenListResult;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetaDataDetail;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.NftTokenId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.NftTokenStatus;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class GetNftResult {

    public final NftTokenId tokenId;

    public final NftTokenStatus tokenStatus;

    public final MetadataId metadataId;

    public final String metadataHash;

    public final MetaDataDetail metaDataDetail;

    public final AccountId mintAccountId;

    public final AccountId ownerAccountId;

    public final AccountId operatorAccountId;

    public final boolean isLocked;

    public static GetNftResult createResult(
            RenewableEnergyTokenGetTokenListResult.RenewableEnergyTokenData result,
            MetaDataDetail metaDataDetail
    ) {
        return GetNftResult.builder()
                .tokenId(result.tokenId.nftTokenId())
                .tokenStatus(result.tokenStatus)
                .metadataId(result.metadataId.metadataId())
                .metadataHash(result.metadataHash)
                .metaDataDetail(metaDataDetail)
                .mintAccountId(result.mintAccountId.accountId())
                .ownerAccountId(result.ownerAccountId.accountId())
                .operatorAccountId(result.previousAccountId.accountId())
                .isLocked(result.isLocked)
                .build();
    }
}
