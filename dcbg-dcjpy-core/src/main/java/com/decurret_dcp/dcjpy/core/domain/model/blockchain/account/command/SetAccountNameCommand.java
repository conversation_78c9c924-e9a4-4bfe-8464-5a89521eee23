package com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint16;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractIntValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractStringValue;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class SetAccountNameCommand extends BlockchainContractCommand {

    public SetAccountNameCommand(ValidatorId validatorId, AccountId accountId, String accountName) {

        super(ContractName.VALIDATOR, "modAccount",
              Map.ofEntries(
                      Map.entry("validatorId", new ContractBytes32Value(validatorId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("accountName", new ContractStringValue(accountName)),
                      Map.entry("traceId", new ContractBytes32Value(TraceIdThreadLocalHolder.getTraceId().getValue()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                new Bytes32(((ContractBytes32Value) getArgs().get("validatorId")).asBytes32()),
                new Bytes32(((ContractBytes32Value) getArgs().get("accountId")).asBytes32())
        );
    }
}
