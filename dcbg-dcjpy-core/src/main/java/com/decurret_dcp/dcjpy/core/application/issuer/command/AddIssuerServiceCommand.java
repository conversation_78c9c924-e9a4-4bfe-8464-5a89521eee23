package com.decurret_dcp.dcjpy.core.application.issuer.command;

import com.decurret_dcp.dcjpy.core.domain.model.bank.BankCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.AddIssuerCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerName;

import lombok.Builder;

@Builder
public class AddIssuerServiceCommand {
    public final AdminId adminId;
    public final BankCode bankCode;
    public final IssuerName name;

    public AddIssuerCommand initContractCommand(IssuerId issuerId) {
        return new AddIssuerCommand(issuerId, this.bankCode, this.name);
    }
}
