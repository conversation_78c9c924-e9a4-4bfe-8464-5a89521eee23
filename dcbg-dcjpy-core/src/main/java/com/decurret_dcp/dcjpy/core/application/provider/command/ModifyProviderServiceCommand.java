package com.decurret_dcp.dcjpy.core.application.provider.command;

import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneName;

import lombok.Builder;

@Builder
public class ModifyProviderServiceCommand {
    public final AdminId adminId;
    public final ProviderId providerId;
    public final ZoneName zoneName;
}
