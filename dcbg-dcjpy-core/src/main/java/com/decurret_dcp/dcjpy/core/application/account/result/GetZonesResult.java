package com.decurret_dcp.dcjpy.core.application.account.result;

import java.util.ArrayList;
import java.util.List;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetZoneByAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class GetZonesResult {

    public final List<Zone> zones;

    @Builder
    public static class Zone {

        public final ZoneId zoneId;
        public final String zoneName;

        public static Zone initZone(ValidatorGetZoneByAccountIdResult.Zone contractZones) {
            return Zone.builder()
                    .zoneId(contractZones.zoneId)
                    .zoneName(contractZones.zoneName)
                    .build();
        }
    }

    public static GetZonesResult initResult(
            ProviderGetZoneResult providerGetZoneResult,
            ValidatorGetZoneByAccountIdResult validatorGetZoneByAccountIdResult
    ) {
        List<Zone> zones = new ArrayList<>();
        // FinZone追加
        Zone finZone = Zone
                .builder()
                .zoneId(providerGetZoneResult.zoneId)
                .zoneName(providerGetZoneResult.zoneName)
                .build();
        zones.add(finZone);
        // BizZone追加
        List<Zone> bizZone = validatorGetZoneByAccountIdResult.zones.stream()
                .map(Zone::initZone)
                .toList();
        zones.addAll(bizZone);

        return GetZonesResult.builder()
                .zones(zones)
                .build();
    }
}
