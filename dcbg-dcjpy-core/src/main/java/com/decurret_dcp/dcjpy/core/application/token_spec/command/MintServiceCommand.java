package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.MintCommand;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class MintServiceCommand {

    public final IssuerId issuerId;

    public final AccountId accountId;

    public final Amount mintAmount;

    public MintCommand toMintCommand() {
        return new MintCommand(
                this.issuerId,
                this.accountId,
                this.mintAmount);
    }
}