package com.decurret_dcp.dcjpy.core.application.account.command;

import java.math.BigInteger;

import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account_sync_bridge.command.SyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckSyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.Timeout;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class OrderTerminatingBizAccountServiceCommand {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final RequestId requestId;

    public final Signature accountSignature;

    public final Info info;

    public final ReasonCode reasonCode;

    public CheckSyncAccountCommand toCheckSyncAccountCommand(ZoneId zoneId) {
        return new CheckSyncAccountCommand(
                this.validatorId, this.accountId, zoneId, AccountStatus.TERMINATING, this.accountSignature, this.info
        );
    }

    public SyncAccountCommand toSyncAccountCommand(String accountName, ZoneId zoneId, String zoneName, long unixTime) {
        var timeout = new Timeout(unixTime);
        var accountStatus = AccountStatus.TERMINATING;
        var approveAmount = new Amount(BigInteger.ZERO);

        return new SyncAccountCommand(
                this.validatorId, this.accountId, accountName, zoneId, zoneName,
                accountStatus, this.reasonCode, approveAmount, timeout
        );
    }
}
