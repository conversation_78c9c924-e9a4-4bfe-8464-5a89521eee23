package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class ChargeResult {

    public final AccountId accountId;

    public final String accountName;

    public final ZoneId fromZoneId;

    public final String fromZoneName;

    public final ZoneId toZoneId;

    public final String toZoneName;

    public final Amount chargeAmount;

    public final Balance balance;

    public final TransactionHash transactionHash;
}
