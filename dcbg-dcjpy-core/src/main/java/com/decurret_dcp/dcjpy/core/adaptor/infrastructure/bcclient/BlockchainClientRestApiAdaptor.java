package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.bcclient;

import java.time.Duration;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.config.DcfHttpRequestFactory;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainClient;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendResponse;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

@Component
public class BlockchainClientRestApiAdaptor implements BlockchainClient {

    private final RestTemplate api;

    public BlockchainClientRestApiAdaptor(
            ObjectMapper objectMapper, RestTemplateBuilder builder,
            DcfConfig config, DcfHttpRequestFactory dcfHttpRequestFactory
    ) {

        // BCクライアントのAPI仕様に合わせてObjectMapperの設定をカスタマイズ
        // デフォルトのObjectMapperへの影響を避けるため、設定をコピーした新しいインスタンスを生成している
        ObjectMapper mapper = objectMapper.copy();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);

        this.api = builder
                .messageConverters(new MappingJackson2HttpMessageConverter(mapper))
                .rootUri(config.getBcClientEnv())
                .setReadTimeout(Duration.ofMillis(config.getBcClientReadTimeoutMillisecond()))
                .requestFactory(dcfHttpRequestFactory)
                .additionalInterceptors((request, body, execution)  -> {
                    var traceId = TraceIdThreadLocalHolder.getTraceId().getValue();
                    request.getHeaders().set("trace-id", traceId);
                    return execution.execute(request, body);
                })
                .build();
    }

    @Override
    public BlockchainSendResponse send(BlockchainSendRequest request) {
        return api.postForObject("/send", request, BlockchainSendResponse.class);
    }

    @Override
    public BlockchainCallResponse call(BlockchainCallRequest request) {
        return api.postForObject("/call", request, BlockchainCallResponse.class);
    }
}
