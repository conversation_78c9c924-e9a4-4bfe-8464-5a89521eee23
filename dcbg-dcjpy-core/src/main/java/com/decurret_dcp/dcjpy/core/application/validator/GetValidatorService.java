package com.decurret_dcp.dcjpy.core.application.validator;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.validator.command.GetValidatorServiceCommand;
import com.decurret_dcp.dcjpy.core.application.validator.result.GetValidatorResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetValidator;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetValidatorResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetValidatorService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final ValidatorGetValidator validatorGetValidator;

    /**
     * バリデータを取得します
     *
     * @param command コマンド
     * @return 実行結果
     * @throws NotFoundException バリデータの取得に失敗した場合
     *                           E0042 validator_idが見つかりません
     */
    @Transactional(readOnly = true)
    public GetValidatorResult execute(GetValidatorServiceCommand command) throws NotFoundException {

        // バリデータのデータ取得
        ValidatorGetValidatorResult result
                = validatorGetValidator.execute(command.validatorId, callBlockchainContractService);

        return GetValidatorResult.builder()
                .validatorId(command.validatorId)
                .name(result.name.value)
                .issuerId(result.issuerId.issuerId())
                .build();
    }
}
