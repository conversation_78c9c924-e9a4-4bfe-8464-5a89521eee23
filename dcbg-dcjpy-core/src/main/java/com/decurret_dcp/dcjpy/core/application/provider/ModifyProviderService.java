package com.decurret_dcp.dcjpy.core.application.provider;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.provider.command.ModifyProviderServiceCommand;
import com.decurret_dcp.dcjpy.core.application.provider.result.ModifyProviderResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.HasProvider;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ModProviderCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class ModifyProviderService {
    private final SendBlockchainContractService sendBlockchainContractService;
    private final HasProvider hasProvider;
    private final CallBlockchainContractService callBlockchainContractService;

    /**
     * プロバイダの変更を行います
     *
     * @param command 実行リクエスト
     * @return 実行結果
     * @throws BadRequestException 業務エラー(リクエスト異常)が発生した場合
     *     E0028 provider_idが見つかりません
     *     E0027 provider_idが無効です
     */
    @Transactional(readOnly = true)
    public ModifyProviderResult execute(ModifyProviderServiceCommand command) throws BadRequestException {
        hasProvider.execute(command.providerId, new HasProvider.PathValueSwitch(), callBlockchainContractService);

        ModProviderCommand modifyCommand = ModProviderCommand.initModProviderCommand(command);
        EntityId signerId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(modifyCommand, signerId);

        return new ModifyProviderResult(command.providerId, command.zoneName);
    }
}
