package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetTokenServiceCommand {

    public final ProviderId providerId;

    public GetTokenServiceCommand(ProviderId providerId) {
        Assertion.assertNotNull(providerId, "providerId");
        this.providerId = providerId;
    }
}
