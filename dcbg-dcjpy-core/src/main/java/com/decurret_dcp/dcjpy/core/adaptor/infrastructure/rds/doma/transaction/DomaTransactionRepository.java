package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.transaction;

import java.util.List;

import org.seasar.doma.jdbc.SelectOptions;
import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionId;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.Transaction;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionDetail;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionRepository;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionType;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListForAllAccountCommand;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaTransactionRepository implements TransactionRepository {

    private final TransactionDao dao;

    @Override
    public TransactionDetail selectByAccountIdAndTxId(AccountId accountId, TransactionId txId) {
        return dao.selectByAccountIdAndTxId(accountId, txId);
    }

    @Override
    public Transaction selectByAccountIdAndTxHashAndEventType(
            AccountId accountId, TransactionHash txHash, TransactionType transactionType
    ) {
        return dao.selectByAccountIdAndTxHashAndEventType(accountId, txHash, transactionType);
    }

    @Override
    public List<TransactionDetail> selectByAccountId(TransactionListCommand command, SelectOptions options) {
        return dao.selectByAccountId(command, options);
    }

    @Override
    public List<TransactionDetail> selectByVaridatorId(
            TransactionListForAllAccountCommand command, SelectOptions options
    ) {
        return dao.selectByValidatorId(command, options);
    }
}
