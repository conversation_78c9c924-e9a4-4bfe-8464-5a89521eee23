package com.decurret_dcp.dcjpy.core.domain.model.account;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

class ReasonCodeJson {

    static class Serializer extends JsonSerializer<ReasonCode> {

        @Override
        public void serialize(ReasonCode value, JsonGenerator generator, SerializerProvider provider)
                throws IOException {
            if (value == null) {
                return;
            }

            generator.writeNumber(value.getValue());
        }
    }

    static class Deserializer extends JsonDeserializer<ReasonCode> {

        @Override
        public ReasonCode deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            return new ReasonCode(parser.getValueAsString());
        }
    }
}
