package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckTransactionCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.TransferSingleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.MiscValue1;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.MiscValue2;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class TransferServiceCommand {

    public final ValidatorId validatorId;
    public final AccountId sendAccountId;
    public final AccountId fromAccountId;
    public final AccountId toAccountId;
    public final Amount transferAmount;
    public final Signature accountSignature;
    public final MiscValue1 miscValue1;
    public final MiscValue2 miscValue2;
    public final Info info;
    public final String memo;

    public TransferSingleCommand toTransferCommand() {
        return new TransferSingleCommand(
                this.sendAccountId,
                this.fromAccountId,
                this.toAccountId,
                this.transferAmount,
                this.miscValue1,
                this.miscValue2,
                this.memo
        );
    }

    public CheckTransactionCommand toCheckCommand(ZoneId zoneId) {
        return new CheckTransactionCommand(
                zoneId,
                this.sendAccountId,
                this.fromAccountId,
                this.toAccountId,
                this.transferAmount,
                this.miscValue1,
                this.miscValue2,
                this.accountSignature,
                this.info
        );
    }
}
