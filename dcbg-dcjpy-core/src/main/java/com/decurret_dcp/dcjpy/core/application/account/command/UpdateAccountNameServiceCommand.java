package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command.SetAccountNameCommand;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class UpdateAccountNameServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final String accountName;

    public SetAccountNameCommand toSetAccountNameCommand() {
        return new SetAccountNameCommand(
                this.validatorId,
                this.accountId,
                this.accountName
        );
    }
}
