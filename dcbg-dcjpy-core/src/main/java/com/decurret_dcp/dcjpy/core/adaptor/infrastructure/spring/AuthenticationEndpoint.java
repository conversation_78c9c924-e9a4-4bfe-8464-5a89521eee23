package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import com.decurret_dcp.dcjpy.core.message.MessageCode;
import com.decurret_dcp.dcjpy.core.presentation.rest.GenericErrorResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

public class AuthenticationEndpoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException exception) throws IOException {
        if (response.isCommitted()) {
            return;
        }

        String code = MessageCode.UNAUTHORIZED.getCode();
        GenericErrorResponse errorResponse = new GenericErrorResponse(
                code,
                HttpStatus.UNAUTHORIZED.getReasonPhrase(),
                code + ":" + HttpStatus.UNAUTHORIZED.getReasonPhrase()
        );
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.getOutputStream().print(new ObjectMapper().writeValueAsString(errorResponse));
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    }
}
