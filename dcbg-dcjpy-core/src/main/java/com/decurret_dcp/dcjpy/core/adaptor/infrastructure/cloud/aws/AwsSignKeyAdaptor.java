package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.cloud.aws;

import java.io.IOException;
import java.math.BigInteger;
import java.util.Arrays;

import org.bouncycastle.asn1.ASN1BitString;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.ECDSASignature;
import org.web3j.crypto.ECKeyPair;
import org.web3j.crypto.Sign;
import org.web3j.utils.Numeric;

import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySigner;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.Eoa;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.KeyId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.KeyType;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKey;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKeyAndEoa;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKeyAndEoaGenerator;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.SignKeyHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.util.ECCurveUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.kms.model.CreateKeyRequest;
import software.amazon.awssdk.services.kms.model.CreateKeyResponse;
import software.amazon.awssdk.services.kms.model.CustomerMasterKeySpec;
import software.amazon.awssdk.services.kms.model.GetPublicKeyRequest;
import software.amazon.awssdk.services.kms.model.GetPublicKeyResponse;
import software.amazon.awssdk.services.kms.model.KeyUsageType;
import software.amazon.awssdk.services.kms.model.KmsException;
import software.amazon.awssdk.services.kms.model.MessageType;
import software.amazon.awssdk.services.kms.model.ReplicateKeyRequest;
import software.amazon.awssdk.services.kms.model.ReplicateKeyResponse;
import software.amazon.awssdk.services.kms.model.SignRequest;
import software.amazon.awssdk.services.kms.model.SignResponse;
import software.amazon.awssdk.services.kms.model.SigningAlgorithmSpec;
import software.amazon.awssdk.services.ssm.SsmClient;
import software.amazon.awssdk.services.ssm.model.GetParameterRequest;
import software.amazon.awssdk.services.ssm.model.GetParameterResponse;
import software.amazon.awssdk.services.ssm.model.ParameterType;
import software.amazon.awssdk.services.ssm.model.PutParameterRequest;
import software.amazon.awssdk.services.ssm.model.SsmException;

@RequiredArgsConstructor
@Component
@Slf4j
public class AwsSignKeyAdaptor implements SignKeyHolder {

    private final PrivateKeyAndEoaGenerator privateKeyAndEoaGenerator;

    private final KmsClient kmsClient;

    private final SsmClient ssmClient;

    private final DcfConfig config;

    private static KeyId createKeyId(EntityId entityId, ZoneId zoneId) {
        return new KeyId("/dcjpy/core/private_key/" + zoneId.value + "/" + entityId.value);
    }

    /**
     * SSM に秘密鍵を保存する。
     *
     * @param entityId エンティティID
     * @param zoneId ゾーンID
     * @return 鍵管理情報
     */
    @Override
    public EntitySigner createOpenedSign(EntityId entityId, ZoneId zoneId) {
        KeyId keyId = createKeyId(entityId, zoneId);
        PrivateKeyAndEoa privateKeyAndEoa = this.privateKeyAndEoaGenerator.generate();
        PrivateKey privateKey = privateKeyAndEoa.getPrivateKey();

        // SSMに署名鍵を登録
        this.savePrivateKey(keyId, privateKey);

        return EntitySigner.builder()
                .entityId(entityId)
                .zoneId(zoneId)
                .keyId(keyId)
                .keyType(KeyType.SSM)
                .eoa(privateKeyAndEoa.getEoa())
                .build();
    }

    private void savePrivateKey(KeyId keyId, PrivateKey privateKey) {
        try {
            PutParameterRequest request = PutParameterRequest.builder()
                    .name(keyId.getValue())
                    .value(privateKey.getValue())
                    .type(ParameterType.SECURE_STRING)
                    .overwrite(true)
                    .keyId("alias/app-resource-encryption-key")
                    .build();

            this.ssmClient.putParameter(request);

            log.info("Succeed to create sign key in SSM. keyId = {}", keyId.getValue());
        } catch (SsmException exc) {
            throw new RuntimeException("Failed to put private key in SSM. keyId = " + keyId.getValue(), exc);
        }
    }

    /**
     * KMS に秘密鍵を保存する。
     *
     * @param entityId エンティティID
     * @param zoneId ゾーンID
     * @return 鍵管理情報
     */
    @Override
    public EntitySigner createClosedSign(EntityId entityId, ZoneId zoneId) {
        // KMSで鍵を生成
        KeyId keyId;
        try {
            keyId = this.createKey();
            log.info("Succeed to create key in KMS. entityId : {}, keyId : {}", entityId, keyId.getValue());
        } catch (KmsException kmsExc) {
            log.error("Failed to create key in KMS. entityId : {}, detail : {}", entityId, kmsExc.getMessage(), kmsExc);
            throw kmsExc;
        }

        // レプリケーションが指定されている場合は、レプリケーション鍵を作成する
        if (StringUtils.hasText(this.config.getKmsReplicaRegion())) {
            try {
                KeyId replicaKey = this.createReplicateKey(keyId, this.config.getKmsReplicaRegion());
                log.info("Succeed to create replica key in KMS. entityId : {}, keyId : {}, replicaKeyId : {}",
                         entityId, keyId.getValue(), replicaKey.getValue());
            } catch (KmsException kmsExc) {
                log.error("[MUST DO RECOVERY ACTION] Failed to create replica key in KMS but created main key. "
                                  + "entityId : {}, keyId : {}, detail : {}",
                          entityId, keyId.getValue(), kmsExc.getMessage(), kmsExc);
            }
        }

        // 作成したKMS鍵から公開鍵バイト配列を取得する
        BigInteger publicKey = this.findPublicKey(keyId);
        Eoa createdEoa = Eoa.fromPublicKey(publicKey);

        return EntitySigner.builder()
                .entityId(entityId)
                .zoneId(zoneId)
                .keyId(keyId)
                .keyType(KeyType.KMS)
                .eoa(createdEoa)
                .build();
    }

    /**
     * KMS に鍵生成を要求する。key_id は KMS により決定される。
     *
     * @return key_id
     */
    private KeyId createKey() throws KmsException {
        CreateKeyRequest request = CreateKeyRequest.builder()
                .customerMasterKeySpec(CustomerMasterKeySpec.ECC_SECG_P256_K1)
                .keyUsage(KeyUsageType.SIGN_VERIFY)
                .multiRegion(true)
                .build();

        CreateKeyResponse response = this.kmsClient.createKey(request);
        return new KeyId(response.keyMetadata().keyId());
    }

    private KeyId createReplicateKey(KeyId keyId, String replicaRegion) throws KmsException {
        ReplicateKeyRequest request = ReplicateKeyRequest.builder()
                .keyId(keyId.getValue())
                .replicaRegion(replicaRegion)
                .build();

        ReplicateKeyResponse response = this.kmsClient.replicateKey(request);
        return new KeyId(response.replicaKeyMetadata().keyId());
    }

    private BigInteger findPublicKey(KeyId keyId) {
        GetPublicKeyRequest request = GetPublicKeyRequest.builder()
                .keyId(keyId.getValue())
                .build();

        byte[] publicKeyBytes;
        try {
            GetPublicKeyResponse response = this.kmsClient.getPublicKey(request);
            publicKeyBytes = response.publicKey().asByteArray();
        } catch (KmsException exc) {
            throw new IllegalArgumentException("Failed to fetch public key from KMS. keyId = " + keyId.getValue(), exc);
        }

        ASN1Primitive asn1Primitive;
        try (ASN1InputStream asn1InputStream = new ASN1InputStream(publicKeyBytes)) {
            asn1Primitive = asn1InputStream.readObject();
        } catch (IOException ioExc) {
            throw new RuntimeException(
                    "Unexpected error occurred. The public key bytes format is invalid. keyId = "
                            + keyId.getValue(), ioExc);
        }

        try {
            SubjectPublicKeyInfo publicKeyInfo = SubjectPublicKeyInfo.getInstance(asn1Primitive);
            ASN1BitString bitString = publicKeyInfo.getPublicKeyData();
            ECCurve curve = ECCurveUtil.getCurve();
            ECPoint point = curve.decodePoint(bitString.getOctets());
            byte[] encoded = point.getEncoded(false);

            // 上位1バイトは非圧縮形式を示すため削除
            return new BigInteger(1, Arrays.copyOfRange(encoded, 1, encoded.length));
        } catch (RuntimeException exc) {
            log.error("Failed to encode public key. keyId = {}", keyId.getValue(), exc);
            throw exc;
        }
    }

    @Override
    public String createSignature(EntitySigner entitySigner, String hash) {
        return switch (entitySigner.getKeyType()) {
            case KMS -> this.createSignatureForKms(entitySigner, hash);
            case SSM -> this.createSignatureForSsm(entitySigner, hash);
        };
    }

    String createSignatureForSsm(EntitySigner entitySigner, String hash) {
        PrivateKey privateKey = this.findPrivateKey(entitySigner);
        Credentials credentials = Credentials.create(privateKey.getValue());
        ECKeyPair pair = credentials.getEcKeyPair();

        Sign.SignatureData sign = Sign.signPrefixedMessage(Numeric.hexStringToByteArray(hash), pair);
        return toSignatureString(sign.getR(), sign.getS(), sign.getV());
    }

    String createSignatureForKms(EntitySigner entitySigner, String hash) {
        KeyId keyId = entitySigner.getKeyId();

        byte[] messageHash = Sign.getEthereumMessageHash(Numeric.hexStringToByteArray(hash));
        byte[] byteSig = this.signWithKmsPrivateKey(keyId, messageHash);
        SdkBytes messageBytes = SdkBytes.fromByteArray(messageHash);

        // KMSからpublicKeyを取得
        BigInteger publicKey = this.findPublicKey(keyId);

        ASN1Sequence sequence = ASN1Sequence.getInstance(byteSig);
        BigInteger rValue = ASN1Integer.getInstance(sequence.getObjectAt(0)).getValue();
        BigInteger sValue = ASN1Integer.getInstance(sequence.getObjectAt(1)).getValue();

        // 署名の分解とvの導出
        ECDSASignature signature = new ECDSASignature(rValue, sValue);
        // sの修正
        signature = signature.toCanonicalised();

        // ハッシュ化されたメッセージに対する署名から公開鍵を復元し、v値を導出する
        Sign.SignatureData signatureData = null;
        for (int i = 0; i < 4; i++) {
            BigInteger recoveredPubKey = Sign.recoverFromSignature(i, signature, messageBytes.asByteArray());
            if (publicKey.equals(recoveredPubKey)) {
                // Biginteger -> byte[] 変換時に正の整数を示す最上位ビットに00を追加する場合があるため32バイトで削除する
                byte v = (byte) (i + 27);
                byte[] r = Numeric.toBytesPadded(signature.r, 32);
                byte[] s = Numeric.toBytesPadded(signature.s, 32);
                signatureData = new Sign.SignatureData(v, r, s);

                break;
            }
        }

        if (signatureData == null) {
            throw new RuntimeException(
                    "Failed to create signature for not found valid v value. keyId = " + keyId.getValue());
        }

        return toSignatureString(signatureData.getR(), signatureData.getS(), signatureData.getV());
    }

    private byte[] signWithKmsPrivateKey(KeyId keyId, byte[] messageHash) {
        try {
            SdkBytes hashBytes = SdkBytes.fromByteArray(messageHash);
            SignRequest signRequest = SignRequest
                    .builder()
                    .keyId(keyId.getValue())
                    .signingAlgorithm(SigningAlgorithmSpec.ECDSA_SHA_256)
                    .messageType(MessageType.DIGEST)
                    .message(hashBytes)
                    .build();

            SignResponse signRes = kmsClient.sign(signRequest);
            SdkBytes bytes = signRes.signature();

            return bytes.asByteArray();
        } catch (KmsException exc) {
            throw new IllegalArgumentException("Failed to sign with KMS. keyId = " + keyId.getValue(), exc);
        }
    }

    // https://web3js.readthedocs.io/en/v1.3.1/web3-eth-accounts.html#eth-accounts-sign によると、r, s, v の順で繋げたものが signature
    // 専用のメソッドは用意されていないようなので、自前で結合している。
    private static String toSignatureString(byte[] r, byte[] s, byte[] v) {
        byte[] signBytes = new byte[r.length + s.length + v.length];
        System.arraycopy(r, 0, signBytes, 0, r.length);
        System.arraycopy(s, 0, signBytes, r.length, s.length);
        System.arraycopy(v, 0, signBytes, r.length + s.length, v.length);

        return Numeric.toHexString(signBytes);
    }

    /**
     * SSM より秘密鍵を取得する。
     * 引数に指定された鍵管理情報が KMS の場合は例外をスローする。
     *
     * @param entitySigner 鍵管理情報
     * @return SSM に保存された秘密鍵
     * @throws IllegalArgumentException KMS で管理している鍵情報に対して本メソッドが呼ばれた場合
     */
    @Override
    public PrivateKey findPrivateKey(EntitySigner entitySigner) {
        if (entitySigner.getKeyType() != KeyType.SSM) {
            throw new IllegalArgumentException("This operation is limited for usage ssm.");
        }

        KeyId keyId = entitySigner.getKeyId();
        try {
            // GetParameterリクエストの作成
            GetParameterRequest request = GetParameterRequest.builder()
                    .name(keyId.getValue())
                    .withDecryption(Boolean.TRUE)
                    .build();

            // GetParameterリクエストの発行
            GetParameterResponse response = this.ssmClient.getParameter(request);

            // GetParameterの結果を取得
            return new PrivateKey(response.parameter().value());
        } catch (SsmException exc) {
            throw new RuntimeException("Failed to fetch private key from SSM. keyId = " + keyId.getValue(), exc);
        }
    }
}
