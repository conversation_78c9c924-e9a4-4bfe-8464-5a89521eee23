package com.decurret_dcp.dcjpy.core.application.transaction;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.jdbc.SelectOptions;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.GetAllAccountTransactionListServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.command.GetTransactionListServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.command.GetTransactionServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.GetTransactionListResult;
import com.decurret_dcp.dcjpy.core.application.transaction.result.TransactionResult;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetZoneByAccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetZoneByAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionDetail;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionRepository;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListForAllAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetTransactionService {

    private final ValidatorHasAccount validatorHasAccount;
    private final CallBlockchainContractService callBlockchainContractService;
    private final TransactionRepository transactionRepository;
    private final ProviderGetZone providerGetZone;
    private final ValidatorGetZoneByAccountId validatorGetZoneByAccountId;

    /**
     * 取引一覧を取得する
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 取引一覧の取得に失敗した場合
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public GetTransactionListResult fetchTransactions(GetTransactionListServiceCommand command)
            throws BadRequestException {
        // validator_hasAccount を呼び出し
        validatorHasAccount.execute(
                command.validatorId, command.accountId, MessageCode.ACCOUNT_ID_NOT_FOUND,
                new ValidatorHasAccount.PathValueSwitch(), callBlockchainContractService
        );

        SelectOptions options = SelectOptions.get()
                .limit((int) command.limit.getValue())
                .offset((int) command.offset.getValue())
                .count();

        TransactionListCommand transactionListCommand = command.create();

        List<TransactionDetail> transactions = transactionRepository.selectByAccountId(transactionListCommand, options);

        GetTransactionListResult.PagingResult pagingResult =
                GetTransactionListResult.PagingResult.create(command.offset, command.limit, options);

        List<TransactionResult> resultList = toTransactionDetail(transactions, command.validatorId, command.accountId);
        return new GetTransactionListResult(resultList, pagingResult);
    }

    private List<TransactionResult> toTransactionDetail(
            List<TransactionDetail> transactions, ValidatorId validatorId, AccountId accountId
    ) {
        //取得結果が0件の時は何もせずに空のリストを返却する
        if (transactions.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Integer, String> zonesMap = getZonesMapByAccountId(validatorId, accountId);
        return transactions.stream()
                .map(transaction -> transaction.initTransactionDetail(zonesMap))
                .collect(Collectors.toList());
    }

    /**
     * 取引照会を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 取引照会に失敗した場合
     *                             E0001 account_idが見つかりません
     *                             E0050 transaction_hashが見つかりません
     */
    @Transactional(readOnly = true)
    public TransactionResult fetchTransaction(GetTransactionServiceCommand command) throws BadRequestException {
        validatorHasAccount.execute(
                command.validatorId, command.accountId, MessageCode.ACCOUNT_ID_NOT_FOUND,
                new ValidatorHasAccount.PathValueSwitch(), callBlockchainContractService
        );

        TransactionDetail transaction = transactionRepository.selectByAccountIdAndTxId(
                command.accountId, command.transactionId
        );
        if (transaction == null) {
            throw new NotFoundException(MessageCode.TRANSACTION_HASH_NOT_FOUND);
        }

        ValidatorId validatorId = transaction.validatorId;
        AccountId accountId = transaction.accountId;

        Map<Integer, String> zonesMap = getZonesMapByAccountId(validatorId, accountId);
        return transaction.initTransactionDetail(zonesMap);
    }

    /**
     * 対象アカウントに関連するゾーン情報をMap形式で返却する
     *
     * @param validatorId バリデータID
     * @param accountId アカウントId
     * @return ゾーン情報マップ
     */
    private Map<Integer, String> getZonesMapByAccountId(ValidatorId validatorId, AccountId accountId) {
        Map<Integer, String> zonesMap = new HashMap<>();
        // 自身のゾーン取得
        ProviderGetZoneResult currentZoneResult = providerGetZone.execute(callBlockchainContractService);
        zonesMap.put(currentZoneResult.zoneId.getValue(), currentZoneResult.zoneName);
        // 連携済みのゾーン情報取得
        Map<Integer, String> zonesByAccountIdMap = getZonesByAccountId(validatorId, accountId);
        zonesMap.putAll(zonesByAccountIdMap);

        return zonesMap;
    }

    /**
     * 取引一覧(日次連携)を取得する
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 取引一覧の取得に失敗した場合
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public GetTransactionListResult fetchTransactionsAllAccount(GetAllAccountTransactionListServiceCommand command)
            throws BadRequestException {

        SelectOptions options = SelectOptions.get()
                .limit((int) command.limit.getValue())
                .offset((int) command.offset.getValue())
                .count();

        TransactionListForAllAccountCommand transactionListCommand = command.create(options);
        List<TransactionDetail> transactions = transactionRepository
                .selectByVaridatorId(transactionListCommand, options);

        GetTransactionListResult.PagingResult pagingResult = GetTransactionListResult.PagingResult
                .create(command.offset, command.limit, options);

        List<TransactionResult> resultList = toTransactionDetailForBatch(transactions, command.validatorId);
        return new GetTransactionListResult(resultList, pagingResult);
    }

    private List<TransactionResult> toTransactionDetailForBatch(
            List<TransactionDetail> transactions, ValidatorId validatorId
    ) {
        Map<Integer, String> zonesMap = new HashMap<>();
        // 自身のゾーン取得
        ProviderGetZoneResult currentZoneResult = providerGetZone.execute(callBlockchainContractService);
        zonesMap.put(currentZoneResult.zoneId.getValue(), currentZoneResult.zoneName);

        // BizZoneで実行された場合はあらかじめFinZoneのZoneIdをMapに入れておく
        if (currentZoneResult.zoneId.equals(ZoneId.getFinancialZone()) == false) {
            zonesMap.put(ZoneId.getFinancialZone().getValue(), null);
        }

        return transactions.stream()
                .peek(transaction -> {
                    // zoneIdやotherZoneIdがzonesMapに存在しない場合はMapに追加する
                    if ((zonesMap.containsKey(transaction.zoneId.getValue()) == false)) {
                        Map<Integer, String> zonesByAccountIdMap
                                = getZonesByAccountId(validatorId, transaction.accountId);
                        zonesMap.putAll(zonesByAccountIdMap);
                    }

                    if ((transaction.otherZoneId != null) &&
                            (zonesMap.containsKey(transaction.otherZoneId.getValue()) == false)) {
                        Map<Integer, String> zonesByAccountIdMap
                                = getZonesByAccountId(validatorId, transaction.accountId);
                        zonesMap.putAll(zonesByAccountIdMap);
                    }
                })
                .map(transaction -> transaction.initTransactionDetail(zonesMap))
                .collect(Collectors.toList());
    }

    /**
     * 対象アカウントに関連するゾーン情報を返却する
     * Validator.getZoneByAccountId()で連携済みのbizZone情報を取得する
     * BizZoneで実行する場合はValidator.getZoneByAccountId()が空で返ってくる想定で自身のゾーン情報のみを設定して返却することになる
     *
     * @param targetAccountId 対象のアカウントID
     * @return ゾーン名
     */
    private Map<Integer, String> getZonesByAccountId(ValidatorId validatorId, AccountId targetAccountId) {
        // 連携済みのゾーン情報取得
        ValidatorGetZoneByAccountIdResult getZoneByAccountIdResult
                = validatorGetZoneByAccountId.execute(validatorId, targetAccountId, callBlockchainContractService);

        return getZoneByAccountIdResult.zones.stream()
                .collect(Collectors.toMap(zone -> zone.zoneId.getValue(), zone -> zone.zoneName));
    }
}
