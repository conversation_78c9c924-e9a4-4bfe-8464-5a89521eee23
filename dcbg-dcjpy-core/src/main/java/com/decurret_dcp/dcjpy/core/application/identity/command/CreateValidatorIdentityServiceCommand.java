package com.decurret_dcp.dcjpy.core.application.identity.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddValidatorRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.Eoa;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerIdNullable;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class CreateValidatorIdentityServiceCommand {

    public final AdminId adminId;

    public final ValidatorId validatorId;

    public final IssuerIdNullable issuerId;

    public AddValidatorRoleCommand toAddValidatorRoleCommand(Eoa eoa) {
        return new AddValidatorRoleCommand(this.validatorId, eoa);
    }
}
