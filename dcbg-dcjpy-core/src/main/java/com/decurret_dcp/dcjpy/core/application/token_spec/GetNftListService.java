package com.decurret_dcp.dcjpy.core.application.token_spec;

import java.util.List;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.GetNftListServiceCommand;
import com.decurret_dcp.dcjpy.core.application.token_spec.result.GetNftListResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.RenewableEnergyTokenGetTokenList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.command.RenewableEnergyTokenGetTokenListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.result.RenewableEnergyTokenGetTokenListResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataEntity;
import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataRepository;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetNftListService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final RenewableEnergyTokenGetTokenList renewableEnergyTokenGetTokenList;

    private final NftMetadataRepository nftMetadataRepository;

    public GetNftListResult execute(GetNftListServiceCommand command) {

        // 1. リクエストパラメータのnft_idが、renewable_energy_token かどうかチェックする。
        if ("renewable_energy_token".equals(command.nftId) == false) {
            throw new NotFoundException(MessageCode.NFT_ID_NOT_FOUND);
        }

        // 2. 保有RenewableEnergyToken情報取得 を実行
        RenewableEnergyTokenGetTokenListCommand getNftCommand = command.toGetNftCommand();
        RenewableEnergyTokenGetTokenListResult result
                = renewableEnergyTokenGetTokenList.execute(getNftCommand, callBlockchainContractService);

        // 2-1. 0件の場合は、空配列を返す
        if ((result.renewableEnergyTokenList == null) || (result.renewableEnergyTokenList.isEmpty())) {
            return GetNftListResult.createResult(command, result, null);
        }

        // 3. コントラクトから返却された メタデータID のリストを生成
        List<MetadataId> metadataIdList = result.renewableEnergyTokenList.stream()
                .map(nftToken -> nftToken.metadataId.metadataId())
                .toList();

        // 4. NFTメタデータを取得
        List<NftMetadataEntity> nftMetadataList = nftMetadataRepository
                .fetchMetadata(command.nftId, ZoneIdThreadLocalHolder.getZoneId(), metadataIdList);

        return GetNftListResult.createResult(command, result, nftMetadataList);
    }
}
