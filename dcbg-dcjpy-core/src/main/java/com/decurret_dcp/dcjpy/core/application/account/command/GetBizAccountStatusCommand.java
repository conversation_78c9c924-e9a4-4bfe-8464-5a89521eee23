package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetBizAccountStatusCommand {

    public final AccountId accountId;

    public GetBizAccountStatusCommand(AccountId accountId) {
        Assertion.assertNotNull(accountId, "accountId");

        this.accountId = accountId;
    }
}
