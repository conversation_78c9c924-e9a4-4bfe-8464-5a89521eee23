package com.decurret_dcp.dcjpy.core.config;

import java.net.URI;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClientBuilder;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.kms.KmsClientBuilder;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClientBuilder;
import software.amazon.awssdk.services.ssm.SsmClient;
import software.amazon.awssdk.services.ssm.SsmClientBuilder;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class AwsResourceConfiguration {

    private final DcfConfig dcfConfig;

    @Bean
    public DynamoDbClient dynamoDbClient() {
        DynamoDbClientBuilder builder = DynamoDbClient.builder();

        String localEndpoint = dcfConfig.getLocalStackEndpoint();
        if (StringUtils.hasLength(localEndpoint)) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(localEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));
            log.error(
                    "Change the DynamoDB connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    localEndpoint
            );
        }

        return builder.build();
    }

    @Bean
    public KmsClient kmsClient() {
        KmsClientBuilder builder = KmsClient.builder();

        String localEndpoint = dcfConfig.getLocalStackEndpoint();
        if (StringUtils.hasLength(localEndpoint)) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(localEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));
            log.error(
                    "Change the KMS connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    localEndpoint
            );
        }

        return builder.build();
    }

    @Bean
    public SsmClient ssmClient() {
        SsmClientBuilder builder = SsmClient.builder();

        String localEndpoint = dcfConfig.getLocalStackEndpoint();
        if (StringUtils.hasLength(localEndpoint)) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(localEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));
            log.error(
                    "Change the SSM connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    localEndpoint
            );
        }

        return builder.build();
    }

    @Bean
    public SecretsManagerClient secretsManagerClient() {
        SecretsManagerClientBuilder builder = SecretsManagerClient.builder();

        String localEndpoint = dcfConfig.getLocalStackEndpoint();
        if (StringUtils.hasLength(localEndpoint)) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(localEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));
            log.error(
                    "Change the SecretsManager connection url to {}. "
                            + "If this message is displayed in running on AWS environment, "
                            + "check whether the environment variable is correct.",
                    localEndpoint
            );
        }

        return builder.build();
    }
}
