package com.decurret_dcp.dcjpy.core.application.account;

import java.math.BigInteger;
import java.util.List;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.GetBalancesServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.GetBalancesResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.BalanceOfZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetBalancesService {
    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerGetAccount issuerGetAccount;
    private final ProviderGetZone providerGetZone;
    private final GetBalanceList getBalanceList;

    /**
     * 残高の取得
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 残高の取得に失敗した場合
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public GetBalancesResult execute(GetBalancesServiceCommand command) throws NotFoundException {
        IssuerId issuerid = command.issuerId;
        AccountId accountId = command.accountId;

        // FinZoneの残高取得
        IssuerGetAccountResult issuerGetAccountResult
                = issuerGetAccount.execute(issuerid, accountId, callBlockchainContractService);

        // FinZoneのZone情報を取得する
        ProviderGetZoneResult providerGetZoneResult
                = providerGetZone.execute(callBlockchainContractService);

        // BizZoneの残高取得
        TokenGetBalanceListResult tokenGetBalanceListResult
                = getBalanceList.execute(accountId, callBlockchainContractService);

        // FinancialZone情報を先頭に設定して残高合計を計算する
        List<BalanceOfZone> result =
                convertToBalanceList(issuerGetAccountResult, providerGetZoneResult, tokenGetBalanceListResult);
        BigInteger total = result.stream()
                .map(element -> element.getBalance().getValue())
                .reduce(BigInteger.ZERO, BigInteger::add);

        return new GetBalancesResult(new Amount(total), result);
    }

    private List<BalanceOfZone> convertToBalanceList(
            IssuerGetAccountResult finResult,
            ProviderGetZoneResult providerGetZoneResult,
            TokenGetBalanceListResult bizResultList
    ) {
        return Stream.concat(
                Stream.of(BalanceOfZone.toBalanceOfZone(finResult, providerGetZoneResult.zoneName)),
                IntStream.range(0, bizResultList.zoneIds.size()).mapToObj(
                        index -> new BalanceOfZone(
                                bizResultList.zoneIds.get(index),
                                bizResultList.zoneNames.get(index),
                                bizResultList.balances.get(index),
                                bizResultList.accountNames.get(index),
                                AccountStatus.of(bizResultList.accountStatus.get(index).getValue())
                        )
                )
        ).toList();
    }
}
