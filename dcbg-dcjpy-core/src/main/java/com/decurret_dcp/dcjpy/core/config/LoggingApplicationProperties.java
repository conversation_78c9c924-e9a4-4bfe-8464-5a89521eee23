package com.decurret_dcp.dcjpy.core.config;

import java.util.Arrays;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class LoggingApplicationProperties {

    @Autowired
    private Environment env;

    // パスワードや鍵など、セキュリティ面でログ出力してはいけないものは、追加しないこと
    private List<String> propList = Arrays.asList(
            "spring.datasource.url",
            "spring.datasource.username",
            "spring.datasource.hikari.maximum-pool-size",
            "spring.datasource.hikari.minimum-idle",
            "spring.datasource.hikari.connection-timeout",
            "spring.datasource.hikari.idle-timeout",
            "dcf.bcClientEnv",
            "dcf.httpConnectionMaxPerRoute",
            "dcf.httpConnectionMaxTotal",
            "dcf.auth.issuerUrl",
            "dcf.defaultLimit",
            "dcf.userPoolId",
            "dcf.cognitoRegion",
            "dcf.accessTokenValidity",
            "dcf.allowedOauthScopes",
            "dcf.refreshTokenValidity",
            "dcf.cognitoLocalEndpoint",
            "dcf.period",
            "dcf.deadline",
            "dcf.timeoutTimestamp",
            "dcf.bcClientReadTimeoutMillisecond",
            "dcf.transferLimit",
            "dcf.exchangeLimit",
            "dcf.mintLimit",
            "dcf.burnLimit",
            "dcf.cumulativeLimit"
    );

    /**
     * Spring Boot起動時に、application.propertiesの設定値をログ出力する
     */
    @PostConstruct
    public void logging() {
        propList.forEach(s -> log.info(s + " : " + env.getProperty(s)));
    }
}
