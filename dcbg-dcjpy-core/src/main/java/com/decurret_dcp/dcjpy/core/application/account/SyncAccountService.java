package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.SyncAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account_sync_bridge.command.SyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.CheckSyncAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckSyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class SyncAccountService {

    private final ProviderGetZone providerGetZone;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final CheckSyncAccount checkSyncAccount;
    private final ClockService clockService;
    private final DcfConfig config;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * BizZoneアカウント申込受付を行います
     *
     * @param command コマンド
     * @throws BadRequestException BizZoneアカウント申込受付に失敗した場合
     *     E0028 provider_idが見つかりません
     *     E0001 account_idが見つかりません
     *     E0055 account_idは登録済です
     *     E0045 account_idが無効です
     *     E0062 account_idが無効です
     *     E0063 infoが不正です
     * @throws ForbiddenException 共通領域に対して実行した場合
     *     E0014 共通領域への実行の権限はありません(BizZoneでの実行のみ許可する)
     */
    @Transactional
    public void execute(SyncAccountServiceCommand command) throws BadRequestException, ForbiddenException {
        ProviderGetZoneResult zoneResult = providerGetZone.execute(callBlockchainContractService);
        if (zoneResult.zoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        EntityId signerId = new EntityId(command.validatorId);
        CheckSyncAccountCommand checkSyncAccountCommand
                = command.toCheckSyncAccountCommand(zoneResult.zoneId, AccountStatus.APPLYING);

        // 現在時刻+timeoutTimestamp(秒)のUNIX TIMEをタイムアウト値にする
        long unixTime = clockService.instant().getEpochSecond() + config.getTimeoutTimestamp();

        SyncAccountCommand syncAccountCommand = command.toSyncAccountCommand(
                zoneResult.zoneId, zoneResult.zoneName, AccountStatus.APPLYING, unixTime
        );

        // BizZoneアカウント登録確認
        checkSyncAccount.execute(checkSyncAccountCommand, signerId, callBlockchainContractService);

        // sendトランザクション送信
        sendBlockchainContractService.executeWithoutSignature(syncAccountCommand);

        //残高キャッシュの作成
        this.balanceCacheRepository.createBalanceCache(command.accountId, zoneResult.zoneId);
    }

    /**
     * BizZoneアカウント申込受付前確認を行います
     *
     * @param command コマンド
     * @throws BadRequestException BizZoneアカウント申込受付前確認に失敗した場合
     *     E0001 account_idが見つかりません
     *     E0062 account_idが無効です
     *     E0063 infoが不正です
     * @throws ForbiddenException 権限がない場合
     *     E0014 権限がありません
     * @throws NotFoundException プロバイダが存在しなかった場合
     *     E0038 providerが未登録です
     */
    public void check(SyncAccountServiceCommand command)
            throws BadRequestException, ForbiddenException, NotFoundException {

        ProviderGetZoneResult zoneResult = providerGetZone.execute(callBlockchainContractService);
        if (zoneResult.zoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        EntityId signerId = new EntityId(command.validatorId);
        CheckSyncAccountCommand checkSyncAccountCommand
                = command.toCheckSyncAccountCommand(zoneResult.zoneId, AccountStatus.APPLYING);

        // BizZoneアカウント登録確認
        checkSyncAccount.execute(checkSyncAccountCommand, signerId, callBlockchainContractService);
    }
}
