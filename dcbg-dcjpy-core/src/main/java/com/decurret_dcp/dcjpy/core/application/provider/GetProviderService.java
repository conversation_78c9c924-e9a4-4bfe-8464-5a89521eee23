package com.decurret_dcp.dcjpy.core.application.provider;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetProvider;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.GetProviderCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetProviderResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.provider.Provider;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GetProviderService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final ProviderGetProvider getProvider;

    /**
     * 登録されているプロバイダ情報を取得します
     *
     * @return プロバイダ情報
     * @throws NotFoundException プロバイダが存在しなかった場合
     *     E0038 providerが未登録です
     */
    @Transactional(readOnly = true)
    public Provider execute() throws NotFoundException {
        GetProviderCommand command = new GetProviderCommand();
        ProviderGetProviderResult result = getProvider.execute(command, callBlockchainContractService);

        return createProvider(result);
    }

    private static Provider createProvider(ProviderGetProviderResult result) {
        ProviderId providerId = result.providerId.providerId();
        ZoneId zoneId = result.zoneId;
        String zoneName = result.zoneName;

        return Provider.builder()
                .providerId(providerId)
                .zoneId(zoneId)
                .zoneName(zoneName)
                .build();
    }
}