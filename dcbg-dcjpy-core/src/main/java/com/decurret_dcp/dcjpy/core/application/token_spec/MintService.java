package com.decurret_dcp.dcjpy.core.application.token_spec;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.MintServiceCommand;
import com.decurret_dcp.dcjpy.core.application.token_spec.result.MintServiceResult;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.CheckMint;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.CheckMintCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.MintCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class MintService {

    private final SendBlockchainContractService sendBlockchainContractService;
    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerGetAccount issuerGetAccount;
    private final IssuerHasAccount issuerHasAccount;
    private final CheckMint checkMint;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * コインの発行前確認を行う
     *
     * @param command 発行コマンド
     * @return チェック結果
     * @throws BadRequestException コイン発行チェックでエラーとなった場合
     *     E0001 account_idが見つかりません
     *     E0059 account_idが本人未確認です
     *     E0045 account_idが無効です
     *     E0070 mint_amountが上限を超えています
     *     E0013 mint_amountが1日の上限を超えています
     */
    public MintServiceResult check(MintServiceCommand command) throws BadRequestException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // Accountチェック
        IssuerGetAccountResult accountResult
                = this.issuerGetAccount.execute(command.issuerId, command.accountId, callBlockchainContractService);

        EntityId signerId = new EntityId(command.issuerId);
        CheckMintCommand checkMintCommand = new CheckMintCommand(command.issuerId, command.accountId, command.mintAmount);
        // 発行実行可否チェック
        this.checkMint.execute(checkMintCommand, signerId, callBlockchainContractService);

        return MintServiceResult.builder()
                .issuerId(command.issuerId)
                .accountId(command.accountId)
                .mintAmount(command.mintAmount)
                .accountName(accountResult.accountName)
                .build();
    }

    /**
     * コイン発行を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コイン発行リクエストに失敗した場合
     *     E0001 account_idが見つかりません
     *     E0075 このrequest_idは処理済です
     */
    @Transactional(readOnly = true)
    public MintServiceResult execute(MintServiceCommand command) throws BadRequestException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // Accountチェック
        this.checkAccount(command);

        EntityId signerId = new EntityId(command.issuerId);
        CheckMintCommand checkMintCommand = new CheckMintCommand(command.issuerId, command.accountId, command.mintAmount);
        // 発行実行可否チェック
        this.checkMint.execute(checkMintCommand, signerId, callBlockchainContractService);

        // mint実行
        MintCommand mintCommand = command.toMintCommand();
        // sendリクエスト送信
        sendBlockchainContractService.executeWithoutSignature(mintCommand);

        BalanceCache balanceCache;
        try {
            balanceCache = this.balanceCacheRepository.addBalanceCache(command.accountId, zoneId, command.mintAmount);
        } catch (RuntimeException exc) {
            log.error("Failed to add balance cache after minted. {}", command, exc);
            throw exc;
        }

        return MintServiceResult.builder()
                .issuerId(command.issuerId)
                .accountId(command.accountId)
                .mintAmount(command.mintAmount)
                .cacheBalance(balanceCache.balance)
                .build();
    }

    /**
     * イシュアとアカウントの紐付確認
     *
     * @param command 発行のサービスコマンド
     */
    private void checkAccount(MintServiceCommand command) {
        //Accountチェック
        issuerHasAccount.execute(
                command.issuerId,
                command.accountId,
                new IssuerHasAccount.RequestSwitch(),
                callBlockchainContractService
        );
    }
}
