package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import java.util.Map;

import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.command.RenewableEnergyTokenMintCommand;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.NftTokenId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.Builder;

@Builder
public class NftMintServiceCommand {

    public final ValidatorId validatorId;

    public final String nftId;

    public final RequestId requestId;

    public final AccountId mintedAccountId;

    public final AccountId ownerAccountId;

    public final boolean locked;

    public final Map<String, Object> metadataDetail;

    public CheckNftMintServiceCommand initCheckCommand() {
        return CheckNftMintServiceCommand.builder()
                .validatorId(this.validatorId)
                .nftId(this.nftId)
                .mintedAccountId(this.mintedAccountId)
                .ownerAccountId(this.ownerAccountId)
                .build();
    }

    public RenewableEnergyTokenMintCommand toNftMintCommand(
            NftTokenId nftTokenId, MetadataId metadataId, ContractBytes32Value metadataHash
    ) {
        return new RenewableEnergyTokenMintCommand(
                nftTokenId,
                metadataId,
                metadataHash,
                this.mintedAccountId,
                this.ownerAccountId,
                this.locked
        );
    }
}