package com.decurret_dcp.dcjpy.core.application.bank.command;

import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetBankListServiceCommand {

    public final Offset offset;

    public final Limit limit;

    public GetBankListServiceCommand(Offset offset, Limit limit) {
        Assertion.assertNotNull(offset, "offset");
        Assertion.assertNotNull(limit, "limit");
        this.offset = offset;
        this.limit = limit;
    }
}
