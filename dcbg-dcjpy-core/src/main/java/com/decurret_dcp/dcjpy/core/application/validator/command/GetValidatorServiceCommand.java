package com.decurret_dcp.dcjpy.core.application.validator.command;

import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetValidatorServiceCommand {

    public final ValidatorId validatorId;

    public GetValidatorServiceCommand(ValidatorId validatorId) {
        Assertion.assertNotNull(validatorId, "validatorId");
        this.validatorId = validatorId;
    }
}
