package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.entity_signer;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySigner;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySignerRepository;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaEntitySignerRepository implements EntitySignerRepository {

    private final EntitySignerDao dao;

    @Override
    public EntitySigner selectBySignerId(EntityId entityId) {
        return dao.selectByEntityId(entityId);
    }

    @Override
    public void save(EntitySigner entitySigner) {
        dao.insert(entitySigner);
    }

    @Override
    public void delete(EntityId entityId) {
        dao.deleteByEntityId(entityId);
    }
}
