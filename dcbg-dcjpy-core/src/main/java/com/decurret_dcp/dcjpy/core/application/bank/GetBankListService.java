package com.decurret_dcp.dcjpy.core.application.bank;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.bank.command.GetBankListServiceCommand;
import com.decurret_dcp.dcjpy.core.application.bank.result.GetBankListResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetIssuerList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetIssuerListResult;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class GetBankListService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerGetIssuerList issuerGetIssuerList;

    /**
     * 金融機関の一覧を取得します
     *
     * @param command コマンド
     * @return 実行結果
     */
    @Transactional(readOnly = true)
    public GetBankListResult execute(GetBankListServiceCommand command) {
        Offset offset = command.offset;
        Limit limit = command.limit;

        IssuerGetIssuerListResult issuerGetIssuerListResult =
                issuerGetIssuerList.execute(offset, limit, callBlockchainContractService);

        return GetBankListResult.initResult(issuerGetIssuerListResult);
    }
}
