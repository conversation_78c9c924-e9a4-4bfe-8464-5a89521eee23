package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckExchangeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.JpyTokenTransferCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.Timeout;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class ChargeServiceCommand {

    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final ZoneId toZoneId;
    public final Amount chargeAmount;

    public CheckExchangeCommand toCheckCommand(ZoneId fromZoneId) {
        return new CheckExchangeCommand(
                this.accountId,
                fromZoneId,
                this.toZoneId,
                this.chargeAmount
        );
    }

    public JpyTokenTransferCommand toTransferCommand(ZoneId fromZoneId, long unixTime) {
        return new JpyTokenTransferCommand(
                this.accountId,
                fromZoneId,
                this.toZoneId,
                this.chargeAmount,
                new Timeout(unixTime)
        );
    }
}
