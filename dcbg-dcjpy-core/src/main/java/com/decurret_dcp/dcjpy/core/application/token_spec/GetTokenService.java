package com.decurret_dcp.dcjpy.core.application.token_spec;

import java.math.BigInteger;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.GetTokenServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.GetTokenCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Enabled;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.Symbol;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenName;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenSpec;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TotalSupply;
import com.decurret_dcp.dcjpy.core.message.MessageCode;
import com.decurret_dcp.dcjpy.core.util.DCFStringUtil;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetTokenService {
    private final CallBlockchainContractService callBlockchainContractService;

    /**
     * トークンの取得を行います
     *
     * @param command コマンド
     * @return トークン
     * @throws NotFoundException トークンが未登録の場合
     *     E0046 tokenが未登録です
     */
    @Transactional(readOnly = true)
    public TokenSpec execute(GetTokenServiceCommand command) throws NotFoundException {
        GetTokenCommand getTokenCommand = new GetTokenCommand(command.providerId);
        BlockchainCallResponse response =
                callBlockchainContractService.executeWithoutSignature(getTokenCommand);

        if (response.isSuccess() == false) {
            throw new NotFoundException(MessageCode.TOKEN_NOT_EXISTS);
        }

        Map<String, Object> data = response.data;

        TokenId tokenId = new TokenId(DCFStringUtil.contractHexToString((String) data.get("tokenId")));
        TotalSupply totalSupply = new TotalSupply(new BigInteger((String) data.get("totalSupply")));
        TokenName tokenName = new TokenName(DCFStringUtil.contractHexToString((String) data.get("name")));
        Symbol symbol = new Symbol(DCFStringUtil.contractHexToString((String) data.get("symbol")));
        Enabled enabled = new Enabled((boolean) data.get("enabled"));

        return TokenSpec.builder()
                .tokenId(tokenId)
                .totalSupply(totalSupply)
                .name(tokenName)
                .symbol(symbol)
                .enabled(enabled)
                .build();
    }
}