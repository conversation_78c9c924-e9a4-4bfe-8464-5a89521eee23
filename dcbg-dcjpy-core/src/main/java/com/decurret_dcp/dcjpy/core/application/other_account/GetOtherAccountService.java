package com.decurret_dcp.dcjpy.core.application.other_account;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.other_account.result.GetAccountServiceResult;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetDestinationAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetDestinationAccountResult;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class GetOtherAccountService {

    private final CallBlockchainContractService callBlockchainService;

    private final ValidatorGetDestinationAccount validatorGetDestinationAccount;

    /**
     * 移転先口座情報を取得します。
     *
     * @param accountId アカウントID
     * @return 移転先口座情報取得結果
     */
    @Transactional(readOnly = true)
    public GetAccountServiceResult execute(AccountId accountId) {
        // Contractを呼び出す。
        ValidatorGetDestinationAccountResult result =
                validatorGetDestinationAccount.execute(accountId, callBlockchainService);

        return GetAccountServiceResult.initResult(accountId, result);
    }
}
