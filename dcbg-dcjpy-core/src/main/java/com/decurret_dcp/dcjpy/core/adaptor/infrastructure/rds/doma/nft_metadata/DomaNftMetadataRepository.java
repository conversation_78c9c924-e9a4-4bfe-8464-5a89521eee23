package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.nft_metadata;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataEntity;
import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataRepository;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaNftMetadataRepository implements NftMetadataRepository {

    private final NftMetadataDao nftMetadataDao;

    @Override
    public void save(NftMetadataEntity nftMetadataEntity) {
        nftMetadataDao.insert(nftMetadataEntity);
    }

    @Override
    public List<NftMetadataEntity> fetchMetadata(String nftId, ZoneId zoneId, List<MetadataId> metadataIdList) {
        return nftMetadataDao.selectForMetadataId(nftId, zoneId, metadataIdList);
    }
}
