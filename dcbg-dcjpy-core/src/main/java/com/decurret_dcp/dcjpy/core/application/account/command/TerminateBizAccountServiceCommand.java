package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.user.CoreUserDetails;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.presentation.rest.account.request.TerminateBizAccountRequest;

import lombok.Builder;

@Builder
public class TerminateBizAccountServiceCommand {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final RequestId requestId;

    public final ZoneId zoneId;

    public static TerminateBizAccountServiceCommand initCommand(
            CoreUserDetails user, String accountId, TerminateBizAccountRequest request
    ) {
        var validatorId = user.getValidatorId();
        var actualAccountId = new AccountId(accountId);
        var requestId = RequestId.of(request.requestId);
        var zoneId = ZoneId.of(request.zoneId);

        return TerminateBizAccountServiceCommand.builder()
                .validatorId(validatorId)
                .accountId(actualAccountId)
                .requestId(requestId)
                .zoneId(zoneId)
                .build();
    }
}
