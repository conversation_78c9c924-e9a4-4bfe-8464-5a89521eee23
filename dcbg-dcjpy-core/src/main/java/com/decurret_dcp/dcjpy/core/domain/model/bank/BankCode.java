package com.decurret_dcp.dcjpy.core.domain.model.bank;

import java.math.BigInteger;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = Integer.class, factoryMethod = "of", acceptNull = false)
public class BankCode {
    public final Integer value;

    public BankCode(int value) {
        this.value = value;
    }

    public static BankCode of(Integer bankCode) {
        return new BankCode(bankCode);
    }

    public static BankCode of(String bankCode) {
        return new BankCode(Integer.valueOf(bankCode));
    }

    public Integer getValue() {
        return this.value;
    }

    public BigInteger asBigInteger() {
        return BigInteger.valueOf(value);
    }

    public String zeroPadding() {
        return String.format("%04d", this.value);
    }
}
