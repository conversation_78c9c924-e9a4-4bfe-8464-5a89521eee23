package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.cloud.aws;

import java.net.URI;

import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientId;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientSecret;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.IdentityRepository;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.UserPoolClient;

import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClient;
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClientBuilder;
import software.amazon.awssdk.services.cognitoidentityprovider.model.CreateUserPoolClientRequest;
import software.amazon.awssdk.services.cognitoidentityprovider.model.CreateUserPoolClientResponse;
import software.amazon.awssdk.services.cognitoidentityprovider.model.DeleteUserPoolClientRequest;
import software.amazon.awssdk.services.cognitoidentityprovider.model.DescribeUserPoolClientRequest;
import software.amazon.awssdk.services.cognitoidentityprovider.model.DescribeUserPoolClientResponse;
import software.amazon.awssdk.services.cognitoidentityprovider.model.ExplicitAuthFlowsType;
import software.amazon.awssdk.services.cognitoidentityprovider.model.OAuthFlowType;
import software.amazon.awssdk.services.cognitoidentityprovider.model.PreventUserExistenceErrorTypes;
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserPoolClientType;

@RequiredArgsConstructor
@Repository
public class CognitoIdentityRepository implements IdentityRepository {

    private final DcfConfig config;

    @Override
    public UserPoolClient create(String clientName) {
        try (CognitoIdentityProviderClient cognitoClient = createProviderClient()) {
            CreateUserPoolClientRequest request = CreateUserPoolClientRequest.builder()
                    .clientName(clientName)
                    .userPoolId(config.getUserPoolId())
                    .generateSecret(true)
                    .accessTokenValidity(config.getAccessTokenValidity())
                    .allowedOAuthFlows(OAuthFlowType.CLIENT_CREDENTIALS)
                    .allowedOAuthFlowsUserPoolClient(true)
                    .allowedOAuthScopes(config.getAllowedOAuthScopes())
                    .explicitAuthFlows(ExplicitAuthFlowsType.ALLOW_REFRESH_TOKEN_AUTH,
                                       ExplicitAuthFlowsType.ALLOW_USER_PASSWORD_AUTH)
                    .refreshTokenValidity(config.getRefreshTokenValidity())
                    .preventUserExistenceErrors(PreventUserExistenceErrorTypes.ENABLED)
                    .build();

            CreateUserPoolClientResponse response = cognitoClient.createUserPoolClient(request);
            UserPoolClientType userPoolClient = response.userPoolClient();

            return new UserPoolClient(
                    new ClientId(userPoolClient.clientId()),
                    new ClientSecret(userPoolClient.clientSecret())
            );
        }
    }

    @Override
    public UserPoolClient selectByClientId(String clientId) {
        try (CognitoIdentityProviderClient cognitoClient = createProviderClient()) {
            DescribeUserPoolClientRequest request = DescribeUserPoolClientRequest.builder()
                    .clientId(clientId)
                    .userPoolId(config.getUserPoolId())
                    .build();

            DescribeUserPoolClientResponse response = cognitoClient.describeUserPoolClient(request);
            UserPoolClientType userPoolClient = response.userPoolClient();

            return new UserPoolClient(
                    new ClientId(userPoolClient.clientId()),
                    new ClientSecret(userPoolClient.clientSecret())
            );
        }
    }

    @Override
    public UserPoolClient delete(String clientId) {
        try (CognitoIdentityProviderClient cognitoClient = createProviderClient()) {
            DeleteUserPoolClientRequest request = DeleteUserPoolClientRequest.builder()
                    .clientId(clientId)
                    .userPoolId(config.getUserPoolId())
                    .build();

            cognitoClient.deleteUserPoolClient(request);

            return null;
        }
    }

    private CognitoIdentityProviderClient createProviderClient() {
        CognitoIdentityProviderClientBuilder builder = CognitoIdentityProviderClient.builder();

        String cognitoLocalEndpoint = config.getCognitoLocalEndpoint();
        if (StringUtils.hasLength(cognitoLocalEndpoint)) {
            AwsCredentials localCredentials = AwsBasicCredentials.create("access123", "secret123");
            builder = builder
                    .region(Region.AP_NORTHEAST_1)
                    .endpointOverride(URI.create(cognitoLocalEndpoint))
                    .credentialsProvider(StaticCredentialsProvider.create(localCredentials));
        }

        return builder.build();
    }
}
