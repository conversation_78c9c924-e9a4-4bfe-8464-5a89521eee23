package com.decurret_dcp.dcjpy.core.application.token_spec.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class BurnCancelServiceResult {
    public final AccountId accountId;
    public final TransactionHash cancelTransactionHash;
    public final TransactionHash transactionHash;

    public BurnCancelServiceResult(AccountId accountId, TransactionHash cancelTransactionHash,
                                   TransactionHash transactionHash) {
        Assertion.assertNotNull(accountId, "accountId");
        Assertion.assertNotNull(cancelTransactionHash, "cancelTransactionHash");
        Assertion.assertNotNull(transactionHash, "transactionHash");
        this.accountId = accountId;
        this.cancelTransactionHash = cancelTransactionHash;
        this.transactionHash = transactionHash;
    }
}
