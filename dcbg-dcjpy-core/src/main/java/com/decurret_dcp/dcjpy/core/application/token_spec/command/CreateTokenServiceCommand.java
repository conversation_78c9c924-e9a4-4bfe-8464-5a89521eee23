package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ProviderAddTokenCommand;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.Symbol;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenName;

import lombok.Builder;

@Builder
public class CreateTokenServiceCommand {

    public final ProviderId providerId;

    public final TokenName tokenName;

    public final Symbol symbol;

    public ProviderAddTokenCommand toProviderAddTokenCommand(TokenId tokenId) {
        return new ProviderAddTokenCommand(
                this.providerId,
                tokenId,
                this.tokenName,
                this.symbol
        );
    }
}