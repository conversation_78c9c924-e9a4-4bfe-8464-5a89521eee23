package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.command.CumulativeResetServiceCommand;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.CumulativeResetCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CumulativeResetService {
    private final SendBlockchainContractService sendBlockchainContractService;
    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerHasAccount hasAccount;

    /**
     * 累積限度額初期化を行います
     *
     * @param command コマンド
     * @throws BadRequestException 累積限度額初期化に失敗した場合
     *     E0001 account_idが見つかりません
     * @throws ForbiddenException 権限がない場合
     *     E0014 権限がありません
     */
    public void execute(CumulativeResetServiceCommand command) throws BadRequestException, ForbiddenException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // BizZone の場合は実行できない
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        IssuerId issuerId = command.issuerId;
        AccountId accountId = command.accountId;
        hasAccount.execute(issuerId,
                           accountId,
                           new IssuerHasAccount.PathValueSwitch(),
                           callBlockchainContractService);

        CumulativeResetCommand cumulativeResetCommand = command.toCumulativeResetCommand();
        EntityId signerId = new EntityId(command.issuerId);
        sendBlockchainContractService.execute(cumulativeResetCommand, signerId);
    }
}
