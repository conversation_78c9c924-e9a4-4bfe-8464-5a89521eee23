package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.command.TokenLimitServiceCommand;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.ModTokenLimitCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class TokenLimitService {
    private final SendBlockchainContractService sendBlockchainContractService;
    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerHasAccount hasAccount;

    /**
     * 限度額更新を行います
     *
     * @param command コマンド
     * @throws BadRequestException 限度額更新に失敗した場合
     *     E0012 更新対象の項目がありません
     *     E0001 account_idが見つかりません
     */
    public void execute(TokenLimitServiceCommand command) throws BadRequestException {

        IssuerId issuerId = command.issuerId;
        AccountId accountId = command.accountId;
        hasAccount.execute(issuerId,
                           accountId,
                           new IssuerHasAccount.PathValueSwitch(),
                           callBlockchainContractService
        );

        ModTokenLimitCommand modTokenLimitCommand = ModTokenLimitCommand.buildCommandParameter(command);

        EntityId signerId = new EntityId(command.issuerId);
        sendBlockchainContractService.execute(modTokenLimitCommand, signerId);
    }
}
