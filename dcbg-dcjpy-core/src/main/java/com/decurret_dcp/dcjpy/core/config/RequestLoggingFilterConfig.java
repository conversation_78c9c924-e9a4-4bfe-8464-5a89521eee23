package com.decurret_dcp.dcjpy.core.config;

import java.util.Arrays;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class RequestLoggingFilterConfig {
    private final DcfConfig config;
    // 出力しないHeader情報をリスト化(小文字で登録)
    List<String> blacklist = Arrays.asList("authorization");

    @Bean
    public CustomRequestLoggingFilter customRequestLoggingFilter() {
        // リクエスト受信時、レスポンス送信時ログ出力設定
        CustomRequestLoggingFilter filter = new CustomRequestLoggingFilter(config.getLogOutputMaxLengthFromItem());
        filter.setIncludeClientInfo(true);
        filter.setIncludeQueryString(true);
        filter.setIncludeHeaders(true);
        filter.setIncludePayload(true);
        // 出力しないHeaderを登録
        filter.setHeaderPredicate(s -> !blacklist.contains(s.toLowerCase()));
        return filter;
    }
}