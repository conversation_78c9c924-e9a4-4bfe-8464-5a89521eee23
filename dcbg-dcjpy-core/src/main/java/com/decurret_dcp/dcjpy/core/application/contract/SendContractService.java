package com.decurret_dcp.dcjpy.core.application.contract;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import com.decurret_dcp.dcjpy.core.application.contract.command.SendContractServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainClient;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendResponse;
import com.decurret_dcp.dcjpy.core.domain.model.contract.InvalidContractParameterException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class SendContractService {

    private final BlockchainClient blockchainClient;

    public BlockchainSendResponse execute(SendContractServiceCommand command) {

        BlockchainSendRequest request = command.toBlockchainSendRequest();

        try {
            return blockchainClient.send(request);
        } catch (HttpClientErrorException exc) {
            HttpStatus httpStatus = exc.getStatusCode();

            // request_id 重複エラー
            if (httpStatus == HttpStatus.CONFLICT) {
                throw new BadRequestException(MessageCode.REQUEST_ID_PROCESSED, exc);
            }
            // 指定されたパラメータに不備があった場合
            if (httpStatus == HttpStatus.BAD_REQUEST) {
                throw new InvalidContractParameterException(exc.getResponseBodyAsString(), exc);
            }

            throw exc;
        }
    }
}
