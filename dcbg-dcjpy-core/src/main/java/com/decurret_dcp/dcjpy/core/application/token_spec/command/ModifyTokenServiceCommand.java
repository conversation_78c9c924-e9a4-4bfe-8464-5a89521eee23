package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ModTokenCommand;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.SymbolNullable;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.TokenNameNullable;

import lombok.Builder;

@Builder
public class ModifyTokenServiceCommand {
    public final ProviderId providerId;
    public final TokenId tokenId;
    public final TokenNameNullable tokenName;
    public final SymbolNullable symbol;

    public ModTokenCommand toModTokenCommand() {
        return new ModTokenCommand(
                this.tokenId,
                this.tokenName,
                this.symbol);
    }
}
