package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class CheckApproveServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId ownerId;
    public final AccountId spenderId;
    public final Amount amount;
    public final Signature accountSignature;
    public final Info info;
}
