package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.transaction;

import java.util.List;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.SelectOptions;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionId;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.Transaction;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionDetail;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionType;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListForAllAccountCommand;

@ConfigAutowireable
@Dao
public interface TransactionDao {

    @Select
    TransactionDetail selectByAccountIdAndTxId(AccountId accountId, TransactionId txId);

    @Select
    Transaction selectByAccountIdAndTxHashAndEventType(
            AccountId accountId, TransactionHash txHash, TransactionType transactionType
    );

    @Select
    List<TransactionDetail> selectByAccountId(TransactionListCommand command, SelectOptions options);

    @Select
    List<TransactionDetail> selectByValidatorId(TransactionListForAllAccountCommand command, SelectOptions options);
}
