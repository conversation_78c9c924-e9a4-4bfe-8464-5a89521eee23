package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.CumulativeResetCommand;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class CumulativeResetServiceCommand {
    public final IssuerId issuerId;
    public final AccountId accountId;

    public CumulativeResetServiceCommand(IssuerId issuerId, AccountId accountId) {
        Assertion.assertNotNull(issuerId, "issuerId");
        Assertion.assertNotNull(accountId, "accountId");
        this.issuerId = issuerId;
        this.accountId = accountId;
    }

    public CumulativeResetCommand toCumulativeResetCommand() {
        return new CumulativeResetCommand(this.issuerId, this.accountId);
    }
}
