package com.decurret_dcp.dcjpy.core.domain.model.account;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.Value;

@Value
@JsonSerialize(using = ReasonCodeJson.Serializer.class)
@JsonDeserialize(using = ReasonCodeJson.Deserializer.class)
public class ReasonCode {
    String value;

    public ReasonCode(String value) {
        this.value = value;
    }
}
