package com.decurret_dcp.dcjpy.core.domain.model.account;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.Value;

@Value
public class BalanceOfZone {
    ZoneId zoneId;
    String zoneName;
    Balance balance;
    String accountName;
    AccountStatus accountStatus;
    // StateCode stateCode;

    public BalanceOfZone(ZoneId zoneId, String zoneName, Balance balance, String accountName,
                         AccountStatus accountStatus) {
        Assertion.assertNotNull(zoneId, "zoneId");
        Assertion.assertNotNull(zoneName, "zoneName");
        Assertion.assertNotNull(balance, "balance");
        Assertion.assertNotNull(accountStatus, "accountStatus");
        this.zoneId = zoneId;
        this.zoneName = zoneName;
        this.balance = balance;
        this.accountName = accountName;
        this.accountStatus = accountStatus;
    }

    public static BalanceOfZone toBalanceOfZone(IssuerGetAccountResult issuerGetAccountResult, String zoneName) {
        ZoneId issuerZoneId = ZoneIdThreadLocalHolder.getZoneId();
        return new BalanceOfZone(
                issuerZoneId,
                zoneName,
                issuerGetAccountResult.balance,
                issuerGetAccountResult.accountName,
                AccountStatus.of(issuerGetAccountResult.accountStatus.value)
        );
    }
}
