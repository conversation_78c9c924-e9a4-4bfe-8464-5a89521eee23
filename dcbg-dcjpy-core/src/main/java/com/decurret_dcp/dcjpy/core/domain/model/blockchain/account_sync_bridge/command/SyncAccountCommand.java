package com.decurret_dcp.dcjpy.core.domain.model.blockchain.account_sync_bridge.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Bytes32;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractIntValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractStringValue;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.Timeout;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

public class SyncAccountCommand extends BlockchainContractCommand {

    public SyncAccountCommand(
            ValidatorId validatorId, AccountId accountId, String accountName, ZoneId fromZoneId, String fromZoneName,
            AccountStatus accountStatus, ReasonCode reasonCode, Amount approvalAmount, Timeout timeout
    ) {
        super(ContractName.ACCOUNT_SYNC_BRIDGE, "syncAccount",
              Map.ofEntries(
                      Map.entry("validatorId", new ContractBytes32Value(validatorId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("accountName", new ContractStringValue(accountName)),
                      Map.entry("fromZoneId", new ContractIntValue(fromZoneId.getValue())),
                      Map.entry("zoneName", new ContractStringValue(fromZoneName)),
                      Map.entry("accountStatus", new ContractBytes32Value(accountStatus.getValue())),
                      Map.entry("reasonCode", new ContractBytes32Value(reasonCode.getValue())),
                      Map.entry("approvalAmount", new ContractIntValue(approvalAmount.getValue())),
                      Map.entry("traceId", new ContractBytes32Value(TraceIdThreadLocalHolder.getTraceId().getValue())),
                      Map.entry("timeoutHeight", new ContractIntValue(timeout.asBigInteger()))
              )
        );
    }

    public SyncAccountCommand(
            ValidatorId validatorId, AccountId accountId, String accountName, ZoneId fromZoneId, String fromZoneName,
            AccountStatus accountStatus, Amount approvalAmount, Timeout timeout
    ) {
        this(
                validatorId,
                accountId,
                accountName,
                fromZoneId,
                fromZoneName,
                accountStatus,
                new ReasonCode(null),
                approvalAmount,
                timeout
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                // 署名不要のため空のまま
        );
    }
}
