package com.decurret_dcp.dcjpy.core.application.transaction;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.FinZoneDischargeServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.FinZoneCheckDischargeResult;
import com.decurret_dcp.dcjpy.core.application.transaction.result.FinZoneDischargeResult;
import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.CheckExchange;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckExchangeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.JpyTokenDischargeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetZoneByAccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetZoneByAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class FinZoneDischargeService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final SendBlockchainContractService sendBlockchainContractService;

    private final ClockService clockService;

    private final ValidatorGetZoneByAccountId validatorGetZoneByAccountId;

    private final CheckExchange checkExchange;

    private final ValidatorGetAccount validatorGetAccount;

    private final ProviderGetZone getZone;

    private final DcfConfig config;

    /**
     * ディスチャージ前確認を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException
     *      E0014 権限がありません
     *      E0065 このto_zoneは指定出来ません
     *      E0001 account_idが見つかりません
     *      E0028 provider_idが見つかりません
     *      E0005 残高が不足しています
     *      E0032 account_idのアカウント状態が不正です
     *      E0073 charge_amountが上限を超えています
     *      E0025 charge_amountが1日の上限を超えています
     */
    @Transactional(readOnly = true)
    public FinZoneCheckDischargeResult check(FinZoneDischargeServiceCommand command) throws BadRequestException {
        ZoneId toZoneId = ZoneIdThreadLocalHolder.getZoneId();

        // アカウントのチェック
        // ディスチャージ前確認
        return this.commonCheck(command, toZoneId);
    }

    private FinZoneCheckDischargeResult commonCheck(FinZoneDischargeServiceCommand command, ZoneId toZoneId) {
        // toZoneはFin
        if (!toZoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // fromZoneはBiz
        if (command.fromZoneId.isFinancialZone()) {
            throw new BadRequestException(MessageCode.ZONE_ID_INVALID);
        }

        // account情報取得
        ValidatorGetAccountResult accountResult
                = validatorGetAccount.execute(command.validatorId, command.accountId, callBlockchainContractService);

        // FinZone情報を取得する
        ProviderGetZoneResult finZone = getZone.execute(callBlockchainContractService);

        // BizZone情報を取得する
        ValidatorGetZoneByAccountIdResult validatorGetZoneByAccountIdResult =
                validatorGetZoneByAccountId.execute(command.validatorId, command.accountId,
                                                    callBlockchainContractService);

        if (validatorGetZoneByAccountIdResult.zones.isEmpty()) {
            // ディスチャージ先のZoneが存在しない場合、業務エラー
            throw new BadRequestException(MessageCode.ZONE_ID_INVALID);
        }

        String bizZoneName = validatorGetZoneByAccountIdResult.zones.stream()
                .filter(zone -> zone.zoneId.equals(command.fromZoneId))
                .findFirst()
                .orElseThrow()
                .zoneName;

        // FinZoneでのAccountの有効性の確認を行う
        CheckExchangeCommand checkExchangeCommand = command.toCheckCommand(toZoneId);
        checkExchange.execute(checkExchangeCommand, callBlockchainContractService);

        return FinZoneCheckDischargeResult.builder()
                .accountId(command.accountId)
                .accountName(accountResult.accountData.accountName)
                .fromZoneId(command.fromZoneId)
                .fromZoneName(bizZoneName)
                .toZoneId(toZoneId)
                .toZoneName(finZone.zoneName)
                .dischargeAmount(command.dischargeAmount)
                .build();
    }

    /**
     * ディスチャージを行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException
     *      E0014 権限がありません
     *      E0065 このto_zoneは指定出来ません
     *      E0001 account_idが見つかりません
     *      E0038 providerが未登録です
     *      E0005 残高が不足しています
     *      E0032 account_idのアカウント状態が不正です
     *      E0073 charge_amountが上限を超えています
     *      E0025 charge_amountが1日の上限を超えています
     *
     */
    @Transactional(readOnly = true)
    public FinZoneDischargeResult execute(FinZoneDischargeServiceCommand command) throws BadRequestException {
        ZoneId toZoneId = ZoneIdThreadLocalHolder.getZoneId();

        FinZoneCheckDischargeResult checkDischargeResult = this.commonCheck(command, toZoneId);
        this.discharge(command, toZoneId);

        return FinZoneDischargeResult.builder()
                .accountId(command.accountId)
                .accountName(checkDischargeResult.accountName)
                .dischargeAmount(command.dischargeAmount)
                .fromZoneId(command.fromZoneId)
                .fromZoneName(checkDischargeResult.fromZoneName)
                .toZoneId(toZoneId)
                .toZoneName(checkDischargeResult.toZoneName)
                .build();
    }

    private void discharge(FinZoneDischargeServiceCommand command, ZoneId toZoneId) throws BadRequestException {

        // 現在時刻+timeoutTimestamp(秒)のUNIX TIMEをタイムアウト値にする
        long unixTime = clockService.instant().getEpochSecond() + config.getTimeoutTimestamp();

        // FinZone向けBizZoneのディスチャージ
        JpyTokenDischargeCommand dischargeCommand = command.toJpyTokenDischargeCommand(toZoneId, unixTime);
        EntityId entityId = new EntityId(command.validatorId);
        sendBlockchainContractService.execute(dischargeCommand, entityId);
    }
}
