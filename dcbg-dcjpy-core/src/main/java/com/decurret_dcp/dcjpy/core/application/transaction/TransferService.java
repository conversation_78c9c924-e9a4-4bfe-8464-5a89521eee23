package com.decurret_dcp.dcjpy.core.application.transaction;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.adaptor.infrastructure.dynamodb.DynamodbBalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.TransferServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.CheckTransferResult;
import com.decurret_dcp.dcjpy.core.application.transaction.result.TransferResult;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.CheckTransaction;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.FinancialCheckFinAccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.FinancialCheckGetBizAccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckTransactionCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.result.FinancialCheckFinAccountStatusResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.result.FinancialCheckGetBizAccountStatusResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.TransferSingleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetDestinationAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetDestinationAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class TransferService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final DynamodbBalanceCacheRepository balanceCacheRepository;
    private final ValidatorGetAccount getAccount;
    private final FinancialCheckFinAccountStatus financialCheckFinAccountStatus;
    private final FinancialCheckGetBizAccountStatus financialCheckGetBizAccountStatus;
    private final ValidatorGetDestinationAccount validatorGetDestinationAccount;
    private final CheckTransaction checkTransaction;

    /**
     * コイン移転前確認を行う
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コイン移転前確認がNGの場合
     *     E0067 misc_valueに16進数文字列は指定出来ません
     *     E0066 from_account_idとto_account_idに同じIDは指定出来ません
     *     E0005 残高が不足しています
     *     E0003 from_account_idが見つかりません
     *     E0032 account_idのアカウント状態が不正です
     *     E0002 send_account_idが見つかりません
     *     E0033 send_account_idのアカウント状態が不正です
     *     E0004 to_account_idが見つかりません
     *     E0034 to_account_idのアカウント状態が不正です
     *     E0062 account_signatureが不正です
     *     E0063 account_signatureの有効期限が切れています
     *     E0072 transfer_amountが上限を超えています
     *     E0024 transfer_amountが1日の上限を超えています
     */
    @Transactional(readOnly = true)
    public CheckTransferResult check(TransferServiceCommand command) throws BadRequestException {
        // ゾーンID取得
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        String toAccountName = this.doCheck(command, zoneId);

        // Resource側でmiscValueは未使用のためレスポンスには含めない
        return CheckTransferResult.builder()
                .sendAccountId(command.sendAccountId)
                .fromAccountId(command.fromAccountId)
                .toAccountId(command.toAccountId)
                .toAccountName(toAccountName)
                .transferAmount(command.transferAmount)
                .memo(command.memo)
                .build();
    }

    /**
     * コイン移転を行う
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException
     *     E0067 misc_valueに16進数文字列は指定出来ません
     *     E0066 from_account_idとto_account_idに同じIDは指定出来ません
     *     E0005 残高が不足しています
     *     E0003 from_account_idが見つかりません
     *     E0032 account_idのアカウント状態が不正です
     *     E0002 send_account_idが見つかりません
     *     E0033 send_account_idのアカウント状態が不正です
     *     E0004 to_account_idが見つかりません
     *     E0034 to_account_idのアカウント状態が不正です
     *     E0062 account_signatureが不正です
     *     E0063 account_signatureの有効期限が切れています
     *     E0072 transfer_amountが上限を超えています
     *     E0024 transfer_amountが1日の上限を超えています
     *     E0075 このrequest_idは処理済です
     */
    @Transactional(readOnly = true)
    public TransferResult execute(TransferServiceCommand command) throws BadRequestException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        String toAccountName = this.doCheck(command, zoneId);

        // 移転実行
        TransferSingleCommand transferSingleCommand = command.toTransferCommand();
        sendBlockchainContractService.executeWithoutSignature(transferSingleCommand);

        // 残高キャッシュ減算
        BalanceCache updatedBalancerCache;
        try {
            updatedBalancerCache = this.balanceCacheRepository
                    .subtractBalanceCache(command.fromAccountId, zoneId, command.transferAmount);
        } catch (RuntimeException exc) {
            log.error("Failed to subtract balance cache after transferred. {}", command, exc);
            throw exc;
        }

        // Resource側でmiscValueは未使用のためレスポンスには含めない
        return TransferResult.builder()
                .sendAccountId(command.sendAccountId)
                .fromAccountId(command.fromAccountId)
                .toAccountId(command.toAccountId)
                .toAccountName(toAccountName)
                .transferAmount(command.transferAmount)
                .memo(command.memo)
                .cacheBalance(updatedBalancerCache.balance)
                .build();
    }

    String doCheck(TransferServiceCommand command, ZoneId zoneId) {
        // miscValueのチェック
        if (command.miscValue1.getValue().startsWith("0x")) {
            throw new BadRequestException(MessageCode.MISC_VALUE_INVALID);
        }

        // from_account_idとto_account_idのチェック
        if (command.fromAccountId.getValue().equals(command.toAccountId.getValue())) {
            throw new BadRequestException(MessageCode.FROM_ACCOUNT_ID_TO_ACCOUNT_ID_CAN_NOT_SAME);
        }

        // fromAccountIdチェック
        this.doCheckAccount(zoneId, command.validatorId, command.fromAccountId, ErrorCodePair.FROM);
        // sendAccountIdチェック
        this.doCheckAccount(zoneId, command.validatorId, command.sendAccountId, ErrorCodePair.SEND);

        // toAccountIdの確認
        ValidatorGetDestinationAccountResult toAccountResult =
                validatorGetDestinationAccount.execute(command.toAccountId, callBlockchainContractService);

        // 移転前チェック
        CheckTransactionCommand checkTransactionCommand = command.toCheckCommand(zoneId);
        this.checkTransaction.execute(checkTransactionCommand, callBlockchainContractService);

        // 残高キャッシュのチェック
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.fromAccountId, zoneId);
        if (command.transferAmount.getValue().compareTo(balanceCache.balance.getValue()) > 0) {
            throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
        }

        return toAccountResult.accountName;
    }

    private void doCheckAccount(ZoneId zoneId, ValidatorId validatorId, AccountId accountId, ErrorCodePair errorCode) {
        try {
            // 指定された validator 配下の account であるかの検証も合わせて行う
            ValidatorGetAccountResult mainResult =
                    getAccount.execute(validatorId, accountId, callBlockchainContractService);
            AccountStatus mainStatus = AccountStatus.of(mainResult.accountData.accountStatus.getValue());
            if (mainStatus != AccountStatus.ACTIVE) {
                log.warn("{} is invalid status in get main account. accountId: {}, status: {}",
                         errorCode.accountType, accountId, mainStatus);
                throw new BadRequestException(errorCode.invalidStatus);
            }

            if (zoneId.isFinancialZone()) {
                return;
            }

            // 現状、FinancialCheck.checkTransfer() は from, send, to のアカウントごとのエラーを判断できない。
            // このため、事前にアカウントを照会し、アカウント状態を個別にチェックする。
            // 特に BizZone の場合は FinDLT で保持している状態をチェックする必要があるため、個別にコントラクト関数を呼び出す。
            FinancialCheckFinAccountStatusResult finResult =
                    financialCheckFinAccountStatus.execute(accountId, callBlockchainContractService);
            AccountStatus finStatus = AccountStatus.of(finResult.accountStatus.getValue());
            if (finStatus != AccountStatus.ACTIVE) {
                log.warn("{} is invalid status in get fin account. accountId: {}, status: {}",
                         errorCode.accountType, accountId, finStatus);
                throw new BadRequestException(errorCode.invalidStatus);
            }

            // 強制償却済み状態は FinDLT のみでしか管理していないため、
            // BizZone の場合は、移転元アカウントの状態チェックを行うために FinDLT に問い合わせる必要がある。
            FinancialCheckGetBizAccountStatusResult bizResult =
                    financialCheckGetBizAccountStatus.execute(accountId, zoneId, callBlockchainContractService);
            AccountStatus bizStatus = AccountStatus.of(bizResult.accountStatus.getValue());
            if (bizStatus != AccountStatus.ACTIVE) {
                log.warn("{} is invalid status in get biz account. accountId: {}, status: {}",
                         errorCode.accountType, accountId, bizStatus);
                throw new BadRequestException(errorCode.invalidStatus);
            }
        } catch (NotFoundException exc) {
            log.warn("{} is not found in check transfer. accountId: {}", errorCode.accountType, accountId);
            throw new NotFoundException(errorCode.notFound, exc);
        }
    }

    private static enum ErrorCodePair {

        FROM("FromAccount", MessageCode.FROM_ACCOUNT_ID_NOT_FOUND, MessageCode.ACCOUNT_ID_STATUS_IS_INVALID),

        SEND("SendAccount", MessageCode.SEND_ACCOUNT_ID_NOT_FOUND, MessageCode.SEND_ACCOUNT_ID_STATUS_IS_VALID);

        private final String accountType;

        private final MessageCode notFound;

        private final MessageCode invalidStatus;

        private ErrorCodePair(String accountType, MessageCode notFound, MessageCode invalidStatus) {
            this.accountType = accountType;
            this.notFound = notFound;
            this.invalidStatus = invalidStatus;
        }
    }
}
