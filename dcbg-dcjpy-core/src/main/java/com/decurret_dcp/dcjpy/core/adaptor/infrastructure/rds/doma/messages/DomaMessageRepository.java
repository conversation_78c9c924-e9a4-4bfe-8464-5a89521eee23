package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.messages;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.decurret_dcp.dcjpy.core.domain.model.message.Message;
import com.decurret_dcp.dcjpy.core.domain.model.message.MessageRepository;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class DomaMessageRepository implements MessageRepository {

    private final MessageDao messageDao;

    @Override
    public List<Message> selectAll() {
        return messageDao.selectAll();
    }

    @Override
    public Message selectByMessageCode(String messageCode) {
        return messageDao.selectByMessageCode(messageCode);
    }
}
