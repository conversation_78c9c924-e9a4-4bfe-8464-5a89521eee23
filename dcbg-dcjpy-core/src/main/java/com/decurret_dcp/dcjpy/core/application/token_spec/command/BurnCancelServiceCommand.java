package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.BurnCancelCommand;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.Transaction;

import lombok.Builder;

@Builder
public class BurnCancelServiceCommand {
    public final IssuerId issuerId;
    public final AccountId accountId;
    public final TransactionHash cancelTransactionHash;

    public BurnCancelCommand toBurnCancelCommand(Transaction transaction) {
        return new BurnCancelCommand(
                this.issuerId,
                this.accountId,
                transaction.amount,
                transaction.transactedAt
        );
    }
}
