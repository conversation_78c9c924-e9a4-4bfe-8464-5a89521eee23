package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;

import lombok.Builder;

@Builder
public class TransferResult {

    public final AccountId sendAccountId;

    public final AccountId fromAccountId;

    public final AccountId toAccountId;

    public final String toAccountName;

    public final Amount transferAmount;

    public final String memo;

    public final Balance cacheBalance;
}
