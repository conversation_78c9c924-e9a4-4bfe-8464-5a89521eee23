package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.AllowanceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class AllowanceServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId ownerId;
    public final AccountId spenderId;

    public AllowanceServiceCommand(ValidatorId validatorId, AccountId ownerId, AccountId spenderId) {
        Assertion.assertNotNull(validatorId, "validatorId");
        Assertion.assertNotNull(ownerId, "ownerId");
        Assertion.assertNotNull(spenderId, "spenderId");
        this.validatorId = validatorId;
        this.ownerId = ownerId;
        this.spenderId = spenderId;
    }

    public AllowanceCommand toAllowanceCommand() {
        return new AllowanceCommand(
                this.validatorId,
                this.ownerId,
                this.spenderId);
    }
}
