package com.decurret_dcp.dcjpy.core.application.issuer.command;

import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerName;

import lombok.Builder;

@Builder
public class ModifyIssuerServiceCommand {
    public final AdminId adminId;
    public final IssuerId issuerId;
    public final IssuerName issuerName;
}
