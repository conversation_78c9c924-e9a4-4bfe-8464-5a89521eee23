package com.decurret_dcp.dcjpy.core.application.account;

import java.util.Objects;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.IndustryAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccountAll;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.SetActiveBusinessAccountWithZoneCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountAllResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class IndustryAccountService {

    private final ValidatorGetAccountAll getAccountAll;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * BizZone用口座作成前確認
     *
     * @throws ForbiddenException 権限がない場合
     *     E0014 権限がありません
     */
    public void check(IndustryAccountServiceCommand command) throws ForbiddenException {
        // フィナンシャルゾーン以外の場合エラー
        ZoneId currentZone = ZoneIdThreadLocalHolder.getZoneId();
        if (currentZone.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // アカウント状態のチェック
        this.doCheckBusinessAccount(command);
    }

    /**
     * BizZone用口座作成
     *
     * @param command コマンド
     * @throws BadRequestException 付加領域用口座作成に失敗した場合
     *     E0001 account_idが見つかりません
     *     E0032 account_idのアカウント状態が不正です
     *     E0077 bizゾーン口座が存在しません
     *     E0078 bizゾーン口座のアカウント状態が不正です
     *     E0075 このrequest_idは処理済です
     * @throws ForbiddenException 権限がない場合
     *     E0014 権限がありません
     */
    @Transactional
    public void execute(IndustryAccountServiceCommand command) throws BadRequestException, ForbiddenException {
        // フィナンシャルゾーン以外の場合エラー
        ZoneId currentZone = ZoneIdThreadLocalHolder.getZoneId();
        if (currentZone.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // アカウント状態のチェック
        this.doCheckBusinessAccount(command);

        // コントラクトValidator#setActiveBusinessAccountWithZone呼び出し
        SetActiveBusinessAccountWithZoneCommand setActiveBusinessAccountWithZoneCommand
                = command.toSetActiveBusinessAccountWithZoneCommand();
        EntityId signerId = new EntityId(command.validatorId);
        sendBlockchainContractService.execute(setActiveBusinessAccountWithZoneCommand, signerId);
    }

    private void doCheckBusinessAccount(IndustryAccountServiceCommand command) {
        // Validator#getAccountAllを呼び出し
        ValidatorGetAccountAllResult result
                = getAccountAll.execute(command.validatorId, command.accountId, callBlockchainContractService);

        // フィナンシャルゾーンアカウント状態不正
        if (AccountStatus.of(result.accountData.accountStatus.getValue()) != AccountStatus.ACTIVE) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }

        // ビジネスゾーンアカウントを取得する
        ValidatorGetAccountAllResult.BizZoneAccount resultBizAccount = result.accountData.businessZoneAccounts.stream()
                .filter(bizAccount -> Objects.equals(bizAccount.zoneId.getValue(), command.bizZoneId.getValue()))
                .findFirst()
                .orElse(null);

        // 指定されたゾーンにアカウントが存在しない
        if (resultBizAccount == null) {
            throw new BadRequestException(MessageCode.BUSINESS_ZONE_ACCOUNT_NOT_FOUND);
        }

        // 指定されたゾーンのアカウントステータス不正
        if (AccountStatus.of(resultBizAccount.accountStatus.getValue()) != AccountStatus.APPLYING) {
            throw new BadRequestException(MessageCode.BUSINESS_ZONE_ACCOUNT_STATUS_IS_VALID);
        }
    }
}
