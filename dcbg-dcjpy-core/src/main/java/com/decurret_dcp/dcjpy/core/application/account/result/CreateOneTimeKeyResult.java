package com.decurret_dcp.dcjpy.core.application.account.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKey;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;

import lombok.Builder;

@Builder
public class CreateOneTimeKeyResult {

    public final AccountId accountId;

    public final PrivateKey skO;

    public final Info info;
}
