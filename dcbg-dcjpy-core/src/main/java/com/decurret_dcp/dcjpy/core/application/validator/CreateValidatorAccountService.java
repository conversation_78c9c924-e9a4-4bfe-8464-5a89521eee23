package com.decurret_dcp.dcjpy.core.application.validator;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.validator.command.CreateValidatorAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.validator.result.CreateValidatorAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetValidatorAccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddValidatorAccountIdCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetValidatorAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class CreateValidatorAccountService {

    private final ValidatorGetValidatorAccountId validatorGetValidatorAccountId;
    private final ValidatorGetAccount validatorGetAccount;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final CallBlockchainContractService callBlockchainContractService;

    /**
     * バリデータアカウントの登録
     * 既に登録されていた場合は何もせずに処理を終了
     * @param command コマンド
     */
    public CreateValidatorAccountResult execute(CreateValidatorAccountServiceCommand command) {

        // アカウントの存在チェック
        ValidatorGetAccountResult validatorGetAccountResult
                = validatorGetAccount.execute(command.validatorId, command.accountId, callBlockchainContractService);

        CreateValidatorAccountResult result = CreateValidatorAccountResult.create(validatorGetAccountResult);

        if (this.isRegisteredValidatorAccountId(command)) {
            // 既に登録済みの場合は何もしない
            return result;
        }

        // コントラクトにアカウント登録
        AddValidatorAccountIdCommand addCommand =
                new AddValidatorAccountIdCommand(command.validatorId, command.accountId);

        EntityId entityId = new EntityId(command.validatorId);
        sendBlockchainContractService.execute(addCommand, entityId);

        return result;
    }

    private boolean isRegisteredValidatorAccountId(CreateValidatorAccountServiceCommand command) {
        ValidatorGetValidatorAccountIdResult result;
        try {
            result = validatorGetValidatorAccountId.execute(command.validatorId, callBlockchainContractService);
        } catch (NotFoundException exc) {
            // AccountIdが見つからない場合は登録されていないためFalse
            if (exc.messageCode == MessageCode.ACCOUNT_ID_NOT_FOUND) {
                return false;
            }

            // 上記以外は想定外なのでエラーを再スロー
            throw exc;
        }

        return result.accountId.getValue().equals(command.accountId.getValue());
    }
}
