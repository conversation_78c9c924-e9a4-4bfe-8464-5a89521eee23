package com.decurret_dcp.dcjpy.core.application.bank.result;

import java.util.List;

import com.decurret_dcp.dcjpy.core.domain.model.bank.BankCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetIssuerListResult;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;

import lombok.Builder;

@Builder
public class GetBankListResult {
    public final List<Bank> bankList;
    public final TotalCount totalCount;

    @Builder
    public static class Bank {
        public final IssuerId issuerId;
        public final BankCode bankCode;
        public final String bankName;

        public static Bank initBank(IssuerGetIssuerListResult.Issuer contractIssuers) {
            return Bank.builder()
                    .issuerId(contractIssuers.issuerId.issuerId())
                    .bankCode(contractIssuers.bankCode)
                    .bankName(contractIssuers.name)
                    .build();
        }
    }

    public static GetBankListResult initResult(IssuerGetIssuerListResult contractResult) {
        List<Bank> banks = contractResult.issuers.stream()
                .map(Bank::initBank)
                .toList();

        return GetBankListResult.builder()
                .bankList(banks)
                .totalCount(contractResult.totalCount)
                .build();
    }
}
