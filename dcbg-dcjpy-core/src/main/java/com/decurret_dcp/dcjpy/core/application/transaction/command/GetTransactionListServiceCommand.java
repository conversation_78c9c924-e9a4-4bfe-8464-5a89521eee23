package com.decurret_dcp.dcjpy.core.application.transaction.command;

import java.util.List;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionType;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.fundamental.SortOrder;

import lombok.Builder;

@Builder
public class GetTransactionListServiceCommand {

    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final Offset offset;
    public final Limit limit;
    public final AppTimeStamp from;
    public final AppTimeStamp to;
    public final List<ZoneId> zoneIds;
    public final List<TransactionType> transactionTypes;
    public final SortOrder dateSort;

    public TransactionListCommand create() {
        return TransactionListCommand.builder()
                .validatorId(this.validatorId)
                .accountId(this.accountId)
                .from(this.from)
                .to(this.to)
                .zoneIds(this.zoneIds)
                .transactionTypes(this.transactionTypes)
                .dateSort(this.dateSort)
                .build();
    }
}
