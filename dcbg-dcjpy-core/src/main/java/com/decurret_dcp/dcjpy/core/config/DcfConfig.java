package com.decurret_dcp.dcjpy.core.config;

import java.math.BigInteger;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Range;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import lombok.Data;

/**
 * Config.
 * プロパティから値を取得
 */
@Component
@ConfigurationProperties(prefix = "dcf")
@Data
@Validated
public class DcfConfig {

    /** bcClientEnv. */
    private String bcClientEnv;

    @NotNull
    @Range(min = 1, max = 50)
    private Integer httpConnectionMaxPerRoute;

    @NotNull
    @Range(min = 1, max = 50)
    private Integer httpConnectionMaxTotal;

    @Min(1)
    @Max(100)
    private int defaultLimit;

    @NotEmpty
    private String cognitoRegion;

    @NotNull
    private Integer accessTokenValidity;

    private String[] allowedOAuthScopes;

    @NotNull
    private Integer refreshTokenValidity;

    @NotEmpty
    private String userPoolId;

    private String cognitoLocalEndpoint;

    private Integer logOutputMaxLengthFromItem;

    @NotNull
    private Long period;

    @NotNull
    private Long deadline;

    @NotNull
    private Long timeoutTimestamp;

    @NotNull
    private Long bcClientReadTimeoutMillisecond;

    @NotNull
    private BigInteger transferLimit;

    @NotNull
    private BigInteger chargeLimit;

    @NotNull
    private BigInteger mintLimit;

    @NotNull
    private BigInteger burnLimit;

    @NotNull
    private BigInteger cumulativeLimit;

    @NotNull
    private String balanceCacheTable;

    private String localStackEndpoint;

    private String kmsReplicaRegion;

    private String secretManagerReplicaRegion;

    private String zoneType;
}
