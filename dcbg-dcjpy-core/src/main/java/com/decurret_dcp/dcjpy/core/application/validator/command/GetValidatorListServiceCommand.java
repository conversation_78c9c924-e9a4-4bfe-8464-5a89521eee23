package com.decurret_dcp.dcjpy.core.application.validator.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.GetValidatorListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class GetValidatorListServiceCommand {
    public final Offset offset;
    public final Limit limit;

    public GetValidatorListCommand toGetValidatorListCommand() {
        return new GetValidatorListCommand(this.offset, this.limit);
    }
}
