package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class GetAccountServiceCommand {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public GetAccountServiceCommand(ValidatorId validatorId, AccountId accountId) {
        Assertion.assertNotNull(validatorId, "validatorId");
        Assertion.assertNotNull(accountId, "accountId");
        this.validatorId = validatorId;
        this.accountId = accountId;
    }
}