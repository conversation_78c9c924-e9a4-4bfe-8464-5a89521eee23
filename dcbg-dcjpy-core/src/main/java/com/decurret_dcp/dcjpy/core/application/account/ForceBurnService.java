package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.result.ForceBurnResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerIsFrozen;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.ForceBurnCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerIsFrozenResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ForceBurnService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final IssuerIsFrozen issuerIsFrozen;
    private final IssuerGetAccount getAccount;
    private final GetBalanceList getBalanceList;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * 強制償却のチェックを行います
     *
     * @param accountId 対象のアカウントID
     * @return チェック結果
     */
    public ForceBurnResult check(IssuerId issuerId, AccountId accountId) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // FinZone ではない場合はチャージを実行できない
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // アカウントのステータス確認
        String accountName = this.checkAccountStatus(issuerId, accountId);

        // 残高確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(accountId, zoneId);
        Balance finZoneBalance = balanceCache.balance;

        this.checkBalance(accountId, finZoneBalance);

        return ForceBurnResult.builder()
                .accountId(accountId)
                .accountName(accountName)
                .build();
    }

    /**
     * 強制償却を実行します
     *
     * @param issuerId イシュアID
     * @param accountId アカウントID
     *
     * @return 実行結果
     */
    public ForceBurnResult execute(IssuerId issuerId, AccountId accountId) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // FinZone ではない場合は償却を実行できない
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // ステータスチェック
        String accountName = this.checkAccountStatus(issuerId, accountId);

        // 残高確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(accountId, zoneId);
        Balance finZoneBalance = balanceCache.balance;

        Balance totalBalance = this.checkBalance(accountId, finZoneBalance);

        // 強制償却実行
        EntityId entityId = new EntityId(issuerId);
        ForceBurnCommand burnCommand = new ForceBurnCommand(issuerId, accountId);
        sendBlockchainContractService.execute(burnCommand, entityId);

        // 残高キャッシュ減算
        BalanceCache afterBalanceCache
                = this.balanceCacheRepository.subtractBalanceCache(accountId, zoneId, new Amount(finZoneBalance.value));

        return ForceBurnResult.builder()
                .accountId(accountId)
                .accountName(accountName)
                .balance(afterBalanceCache.balance)
                .burnAmount(new Amount(totalBalance.value))
                .build();
    }

    private String checkAccountStatus(IssuerId issuerId, AccountId accountId) {
        // ステータスチェック
        IssuerIsFrozenResult isFrozenResult
                = issuerIsFrozen.execute(issuerId, accountId, callBlockchainContractService);
        if (isFrozenResult.frozen == false) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }

        // アカウント名取得
        IssuerGetAccountResult getAccountResult
                = getAccount.execute(issuerId, accountId, callBlockchainContractService);

        return getAccountResult.accountName;
    }

    private Balance checkBalance(AccountId accountId, Balance finZoneBalance) {

        // BizZoneの残高取得
        TokenGetBalanceListResult tokenGetBalanceListResult
                = getBalanceList.execute(accountId, callBlockchainContractService);

        Balance bizZoneBalance = tokenGetBalanceListResult.totalBalance;

        Balance allBalance = finZoneBalance.add(bizZoneBalance);
        if (allBalance.isZero()) {
            throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
        }

        return allBalance;
    }
}
