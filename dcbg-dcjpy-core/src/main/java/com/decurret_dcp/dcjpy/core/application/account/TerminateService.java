package com.decurret_dcp.dcjpy.core.application.account;

import java.math.BigInteger;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.result.TerminateResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.GetBalanceList;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.result.TokenGetBalanceListResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.SetTerminatedCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class TerminateService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final ValidatorGetAccount validatorGetAccount;
    private final GetBalanceList getBalanceList;
    private final BalanceCacheRepository balanceCacheRepository;

    public TerminateResult check(ValidatorId validatorId, AccountId accountId) {
        ValidatorGetAccountResult result = this.checkTerminate(validatorId, accountId);

        return TerminateResult.builder()
                .accountId(accountId)
                .accountName(result.accountData.accountName)
                .build();
    }

    public TerminateResult execute(ValidatorId validatorId, AccountId accountId, ReasonCode reasonCode) {
        ValidatorGetAccountResult result = this.checkTerminate(validatorId, accountId);
        EntityId entityId = new EntityId(validatorId);

        SetTerminatedCommand command = new SetTerminatedCommand(validatorId, accountId, reasonCode);
        sendBlockchainContractService.execute(command, entityId);

        return TerminateResult.builder()
                .accountId(accountId)
                .accountName(result.accountData.accountName)
                .build();
    }

    private ValidatorGetAccountResult checkTerminate(ValidatorId validatorId, AccountId accountId) {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // 残高キャッシュを取得する
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(accountId, zoneId);
        if (balanceCache.balance.isZero() == false) {
            throw new BadRequestException(MessageCode.BALANCE_EXISTS);
        }

        ValidatorGetAccountResult validatorGetAccountResult
                = validatorGetAccount.execute(validatorId, accountId, callBlockchainContractService);

        // Biz残高チェック
        TokenGetBalanceListResult listResult = this.getBalanceList.execute(accountId, callBlockchainContractService);
        if (listResult.totalBalance.isZero() == false) {
            throw new BadRequestException(MessageCode.BIZ_BALANCE_EXISTS);
        }

        return validatorGetAccountResult;
    }
}
