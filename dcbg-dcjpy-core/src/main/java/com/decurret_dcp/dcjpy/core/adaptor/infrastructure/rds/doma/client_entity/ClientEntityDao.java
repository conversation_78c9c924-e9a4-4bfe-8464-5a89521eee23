package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.client_entity;

import org.seasar.doma.Dao;
import org.seasar.doma.Delete;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntity;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientId;

@ConfigAutowireable
@Dao
public interface ClientEntityDao {

    @Insert
    Result<ClientEntity> insert(ClientEntity entity);

    @Select
    ClientEntity selectByClientId(ClientId clientId);

    @Select
    ClientEntity selectByEntityId(EntityId entityId);

    @Delete
    Result<ClientEntity> delete(ClientEntity entity);
}
