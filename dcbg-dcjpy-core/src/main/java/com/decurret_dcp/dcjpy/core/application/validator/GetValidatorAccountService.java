package com.decurret_dcp.dcjpy.core.application.validator;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.validator.result.GetValidatorAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetValidatorAccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetValidatorAccountIdResult;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetValidatorAccountService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final ValidatorGetValidatorAccountId validatorGetValidatorAccountId;

    private final ValidatorGetAccount validatorGetAccount;

    public GetValidatorAccountResult execute(ValidatorId validatorId) {

        // 1. BCClient の「バリデータ管理のアカウントIDを取得」を実行
        ValidatorGetValidatorAccountIdResult validatorGetValidatorAccountIdResult
                = validatorGetValidatorAccountId.execute(validatorId, callBlockchainContractService);
        AccountId accountId = validatorGetValidatorAccountIdResult.accountId.accountId();

        // 2. BCClient の「アカウントの情報取得」を実行
        ValidatorGetAccountResult validatorGetAccountResult
                = validatorGetAccount.execute(validatorId, accountId, callBlockchainContractService);
        ValidatorGetAccountResult.AccountData result = validatorGetAccountResult.accountData;

        // 3. #2 で取得した情報を返却
        return GetValidatorAccountResult.builder()
                .accountId(accountId)
                .accountName(result.accountName)
                .accountStatus(AccountStatus.of(result.accountStatus.getValue()))
                .reasonCode(new ReasonCode(result.reasonCode.getValue()))
                .balance(result.balance)
                .appliedAt(AppTimeStamp.of(result.appliedAt))
                .registeredAt(AppTimeStamp.of(result.registeredAt))
                .terminatingAt(AppTimeStamp.of(result.terminatingAt))
                .terminatedAt(AppTimeStamp.of(result.terminatedAt))
                .build();
    }
}

