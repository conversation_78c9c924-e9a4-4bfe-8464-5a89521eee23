package com.decurret_dcp.dcjpy.core.config.condition;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * Core が動作しているZoneの条件。
 */
public class ZoneCondition {

    private ZoneCondition() {
        // Do nothing.
    }

    /**
     * Financial Zoneで稼働しているか判断する条件。
     */
    public static class FinancialZone implements Condition {

        @Override
        public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
            ZoneType zoneType = getZone(context);
            return (zoneType == ZoneType.FINANCIAL_ZONE);
        }
    }

    /**
     * Business Zoneで稼働しているか判断する条件。
     */
    public static class BusinessZone implements Condition {

        @Override
        public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
            ZoneType zoneType = getZone(context);
            return (zoneType == ZoneType.BUSINESS_ZONE);
        }
    }

    private static ZoneType getZone(ConditionContext context) {
        Environment environment = context.getEnvironment();
        String zoneType = environment.getProperty("dcf.zone_type", "");

        if (ZoneType.FINANCIAL_ZONE.name().equalsIgnoreCase(zoneType)) {
            return ZoneType.FINANCIAL_ZONE;
        }
        if (ZoneType.BUSINESS_ZONE.name().equalsIgnoreCase(zoneType)) {
            return ZoneType.BUSINESS_ZONE;
        }

        throw new IllegalArgumentException(
                "Application property 'dcf.zone_type' is invalid value : " + zoneType);
    }

    public static enum ZoneType {
        FINANCIAL_ZONE, BUSINESS_ZONE;
    }
}
