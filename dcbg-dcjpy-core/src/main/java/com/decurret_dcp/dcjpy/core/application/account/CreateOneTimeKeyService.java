package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;

import com.decurret_dcp.dcjpy.core.application.account.command.CreateOneTimeKeyServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.CreateOneTimeKeyResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.sign.GenerateExternalSignService;
import com.decurret_dcp.dcjpy.core.application.sign.response.OneTimeKeyResponse;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CreateOneTimeKeyService {
    private final GenerateExternalSignService generateExternalSignService;
    private final CallBlockchainContractService callBlockchainContractService;
    private final IssuerGetAccount issuerGetAccount;

    /**
     * ワンタイムキー取得を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws NotFoundException アカウントの取得に失敗した場合
     *     E0001 account_idが見つかりません
     * @throws BadRequestException ワンタイムキーの取得に失敗した場合
     *     E0068 アカウントのアイデンティティが未登録です
     */
    public CreateOneTimeKeyResult execute(CreateOneTimeKeyServiceCommand command)
            throws NotFoundException, BadRequestException {

        IssuerId issuerId = command.issuerId;
        AccountId accountId = command.accountId;

        IssuerGetAccountResult issuerGetAccountResult
                = issuerGetAccount.execute(issuerId, accountId, callBlockchainContractService);

        // Biz口座解約申込みの際にアカウント署名が必要であり、この際に FinZone のアカウントが解約済みのパターンはありうるので
        // アカウント状態のチェックは行わない
//        if (AccountStatus.TERMINATED == AccountStatus.of(issuerGetAccountResult.accountStatus.value)) {
//            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
//        }

        OneTimeKeyResponse oneTimeKeyResponse = generateExternalSignService.createOneTimeKey(issuerId, accountId);

        return CreateOneTimeKeyResult.builder()
                .accountId(accountId)
                .skO(oneTimeKeyResponse.getSkO())
                .info(new Info(
                              oneTimeKeyResponse.getPkO(),
                              oneTimeKeyResponse.getPkC(),
                              oneTimeKeyResponse.getPt(),
                              oneTimeKeyResponse.getSigCpt()
                      )
                )
                .build();
    }
}
