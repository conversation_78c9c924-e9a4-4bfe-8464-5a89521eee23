package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.Terminated;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class TerminateAccountServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final Terminated terminated;
}
