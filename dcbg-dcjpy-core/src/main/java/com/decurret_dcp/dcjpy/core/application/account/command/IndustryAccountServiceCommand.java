package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.SetActiveBusinessAccountWithZoneCommand;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class IndustryAccountServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final ZoneId bizZoneId;

    public SetActiveBusinessAccountWithZoneCommand toSetActiveBusinessAccountWithZoneCommand() {
        return new SetActiveBusinessAccountWithZoneCommand(
                this.validatorId,
                this.accountId,
                this.bizZoneId
        );
    }
}
