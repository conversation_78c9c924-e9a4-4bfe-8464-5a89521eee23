package com.decurret_dcp.dcjpy.core.domain.model.account;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.decurret_dcp.dcjpy.core.util.DCFStringUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum AccountStatus {

    APPLYING("applying"),

    ACTIVE("active"),

    FROZEN("frozen"),

    FORCE_BURNED("force_burned"),

    TERMINATING("terminating"),

    TERMINATED("terminated");

    private static final Map<String, AccountStatus> OBJECT_MAP = Arrays.stream(AccountStatus.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private AccountStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static AccountStatus of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }

    @JsonCreator
    public static AccountStatus fromString(String value) {
        String statusStr = DCFStringUtil.contractHexToString(value);
        AccountStatus status = AccountStatus.of(statusStr);
        if (status == null) {
            throw new IllegalArgumentException("Invalid AccountStatus: " + value);
        }
        return status;
    }

    @JsonValue
    public String toValue() {
        return name().toLowerCase();
    }
}
