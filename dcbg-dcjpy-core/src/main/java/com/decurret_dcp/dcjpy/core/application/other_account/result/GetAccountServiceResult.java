package com.decurret_dcp.dcjpy.core.application.other_account.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetDestinationAccountResult;

import lombok.Builder;

@Builder
public class GetAccountServiceResult {

    public final AccountId accountId;

    public final ValidatorGetDestinationAccountResult contractResult;

    public static GetAccountServiceResult initResult(AccountId accountId, ValidatorGetDestinationAccountResult contractResult) {
        return GetAccountServiceResult.builder()
                .accountId(accountId)
                .contractResult(contractResult)
                .build();
    }
}
