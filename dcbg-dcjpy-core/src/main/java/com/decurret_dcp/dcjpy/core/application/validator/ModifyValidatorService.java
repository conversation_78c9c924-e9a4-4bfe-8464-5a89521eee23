package com.decurret_dcp.dcjpy.core.application.validator;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.validator.command.ModifyValidatorServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.HasValidator;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.ModValidatorCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ModifyValidatorService {

    private final HasValidator hasValidator;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * バリデータの変更を行います
     *
     * @param command コマンド
     * @throws BadRequestException バリデータの変更に失敗した場合
     *     E0042 validator_idが見つかりません
     */
    @Transactional(readOnly = true)
    public void execute(ModifyValidatorServiceCommand command) throws BadRequestException {
        // バリデータID確認
        hasValidator.execute(command.validatorId, new HasValidator.PathValueSwitch(), callBlockchainContractService);

        // バリデータ変更
        ModValidatorCommand modValidatorCommand = command.toModValidatorCommand();
        EntityId signerId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(modValidatorCommand, signerId);
    }
}
