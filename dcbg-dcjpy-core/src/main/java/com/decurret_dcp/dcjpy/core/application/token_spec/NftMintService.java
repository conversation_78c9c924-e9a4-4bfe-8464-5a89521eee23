package com.decurret_dcp.dcjpy.core.application.token_spec;

import java.nio.charset.StandardCharsets;

import org.springframework.stereotype.Service;
import org.web3j.crypto.Hash;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.CheckNftMintServiceCommand;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.NftMintServiceCommand;
import com.decurret_dcp.dcjpy.core.application.token_spec.result.CheckNftMintServiceResult;
import com.decurret_dcp.dcjpy.core.application.token_spec.result.NftMintServiceResult;
import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.FinancialCheckFinAccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.result.FinancialCheckFinAccountStatusResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.command.RenewableEnergyTokenMintCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataEntity;
import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataRepository;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetaDataDetail;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.NftTokenId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class NftMintService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final ValidatorGetAccount validatorGetAccount;

    private final FinancialCheckFinAccountStatus financialCheckFinAccountStatus;

    private final SendBlockchainContractService sendBlockchainContractService;

    private final NftMetadataRepository nftMetadataRepository;

    /**
     * NFT発行前確認
     *
     * @param command コマンド
     * @return 実行結果
     */
    public CheckNftMintServiceResult checkNftMint(CheckNftMintServiceCommand command) {
        // 1. リクエストパラメータのnft_idが、renewable_energy_token かどうかチェックする。
        if ("renewable_energy_token".equals(command.nftId) == false) {
            throw new NotFoundException(MessageCode.NFT_ID_NOT_FOUND);
        }

        // 2. 発行者アカウントID のアカウント情報を取得
        ValidatorGetAccountResult mintedAccount = findMintedAccountInfo(command.validatorId, command.mintedAccountId);

        // 3. 所有者アカウントID のアカウント情報を取得
        if (command.mintedAccountId.equals(command.ownerAccountId)) {
            // 3−1. 所有者アカウントID が 発行者アカウントID と等しい場合、発行者アカウントの情報をセットする
            return CheckNftMintServiceResult.builder()
                    .mintedAccountId(command.mintedAccountId)
                    .mintedAccountName(mintedAccount.accountData.accountName)
                    .ownerAccountId(command.mintedAccountId)
                    .ownerAccountName(mintedAccount.accountData.accountName)
                    .build();
        }

        // 3-2. 所有者アカウントID が 発行者アカウントID が異なる場合、所有者アカウントの情報をコントラクトから取得する
        ValidatorGetAccountResult ownerAccount = findOwnerAccountInfo(command.validatorId, command.ownerAccountId);

        return CheckNftMintServiceResult.builder()
                .mintedAccountId(command.mintedAccountId)
                .mintedAccountName(mintedAccount.accountData.accountName)
                .ownerAccountId(command.ownerAccountId)
                .ownerAccountName(ownerAccount.accountData.accountName)
                .build();
    }

    /**
     * 発行者のアカウント情報をコントラクトから取得
     *
     * @param validatorId バリデータID
     * @param mintedAccountId 発行者のアカウントID
     *
     * @return 発行者のアカウント情報
     */
    private ValidatorGetAccountResult findMintedAccountInfo(ValidatorId validatorId, AccountId mintedAccountId) {
        // 2-1. BCClient の Validator#getAccount を呼び出す
        ValidatorGetAccountResult result = validatorGetAccount
                .execute(validatorId, mintedAccountId, callBlockchainContractService);

        // アカウントの状態がアクティブ以外の場合はエラー
        if (AccountStatus.of(result.accountData.accountStatus.getValue()) != AccountStatus.ACTIVE) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }

        // 2-2. BizZoneから呼び出されている場合は、BCClient のFinancialCheck#checkFinAccountStatusを呼び出し、FinZoneのアカウント状態を確認する
        if (!ZoneIdThreadLocalHolder.getZoneId().isFinancialZone()) {
            FinancialCheckFinAccountStatusResult finCheckMintedAccountResult
                    = financialCheckFinAccountStatus.execute(mintedAccountId, callBlockchainContractService);

            // アカウントの状態がアクティブ以外の場合はエラー
            if (AccountStatus.of(finCheckMintedAccountResult.accountStatus.getValue()) != AccountStatus.ACTIVE) {
                throw new BadRequestException(MessageCode.FINANCIAL_ZONE_ACCOUNT_STATUS_IS_VALID);
            }
        }

        return result;
    }

    /**
     * 所有者のアカウント情報をコントラクトから取得
     *
     * @param validatorId バリデータID
     * @param ownerAccountId 所有者のアカウントID
     *
     * @return 所有者のアカウント情報
     */
    private ValidatorGetAccountResult findOwnerAccountInfo(ValidatorId validatorId, AccountId ownerAccountId) {
        // 3-2-1. BCClient の Validator#getAccount を呼び出す
        ValidatorGetAccountResult result;
        try {
            result = validatorGetAccount.execute(validatorId, ownerAccountId, callBlockchainContractService);
        } catch (NotFoundException notFoundException) {
            throw new NotFoundException(MessageCode.OWNER_ACCOUNT_ID_NOT_FOUND, notFoundException);
        }

        // アカウントの状態がアクティブ以外の場合はエラー
        if (AccountStatus.of(result.accountData.accountStatus.getValue()) != AccountStatus.ACTIVE) {
            throw new BadRequestException(MessageCode.OWNER_ACCOUNT_ID_STATUS_IS_VALID);
        }

        // 3-2-2. BizZoneから呼び出されている場合は、BCClient のFinancialCheck#checkFinAccountStatusを呼び出し、FinZoneのアカウント状態を確認する
        if (!ZoneIdThreadLocalHolder.getZoneId().isFinancialZone()) {
            FinancialCheckFinAccountStatusResult finResult;
            try {
                finResult = financialCheckFinAccountStatus.execute(ownerAccountId, callBlockchainContractService);
            } catch (BadRequestException badRequestException) {
                throw new BadRequestException(MessageCode.OWNER_ACCOUNT_ID_NOT_ENABLED);
            } catch (NotFoundException notFoundException) {
                throw new NotFoundException(MessageCode.FINANCIAL_ZONE_OWNER_ACCOUNT_NOT_FOUND, notFoundException);
            }

            // アカウントの状態がアクティブ以外の場合はエラー
            if (AccountStatus.of(finResult.accountStatus.getValue()) != AccountStatus.ACTIVE) {
                throw new BadRequestException(MessageCode.FINANCIAL_ZONE_OWNER_ACCOUNT_STATUS_IS_VALID);
            }
        }

        return result;
    }

    /**
     * NFT発行
     *
     * @param command コマンド
     * @return 実行結果
     */
    public NftMintServiceResult mintNft(NftMintServiceCommand command) {
        // 1. リクエストパラメータのnft_idが、renewable_energy_token かどうかチェックする。
        // 2. 発行者アカウントID のアカウント情報を取得
        // 3. 所有者と発行者が異なる場合は所有者の情報を取得し、等しい場合は発行者の情報をセットするため所有者の情報は取得しない
        CheckNftMintServiceResult checkResult = this.checkNftMint(command.initCheckCommand());

        // 4. nft_token_id を生成
        NftTokenId nftTokenId = NftTokenId.generate();
        // 5. metadata_id を生成
        MetadataId metadataId = MetadataId.generate();
        // 6. BPM Serverから受け取った metadataDetail に メタデータID と 発行日時 を追加
        AppTimeStamp mintedAt = AppTimeStamp.now();
        MetaDataDetail metadataDetail = MetaDataDetail.withMintedMetadata(metadataId, mintedAt, command.metadataDetail);
        // 7. metadata_hash を生成
        ContractBytes32Value metadataHash = createMetadataHash(metadataDetail);

        // 8. NFT発行を実行
        RenewableEnergyTokenMintCommand nftMintCommand = command.toNftMintCommand(nftTokenId, metadataId, metadataHash);
        sendBlockchainContractService.executeWithoutSignature(nftMintCommand);

        // 9. NFTメタデータテーブルにインサート
        NftMetadataEntity nftMetadataEntity = NftMetadataEntity.builder()
                .nftId(command.nftId)
                .metadataId(metadataId)
                .zoneId(ZoneIdThreadLocalHolder.getZoneId())
                .metadataDetail(metadataDetail)
                .mintedAt(mintedAt)
                .build();
        nftMetadataRepository.save(nftMetadataEntity);

        // 10. 取得した情報をセットする
        return NftMintServiceResult.builder()
                .nftTokenId(nftTokenId)
                .mintedAccountId(command.mintedAccountId)
                .mintedAccountName(checkResult.mintedAccountName)
                .ownerAccountId(command.ownerAccountId)
                .ownerAccountName(checkResult.ownerAccountName)
                .locked(command.locked)
                .metadataId(metadataId)
                .metadataHash(metadataHash.getValue())
                .metadataDetail(metadataDetail)
                .build();
    }

    private static ContractBytes32Value createMetadataHash(MetaDataDetail metaDataDetail) {
        String value = metaDataDetail.getValue();
        byte[] byteValue = value.getBytes(StandardCharsets.UTF_8);

        // byte 配列を Keccak-256 でハッシュ化する
        byte[] hash = Hash.sha3(byteValue);
        if (hash.length != 32) {
            throw new IllegalArgumentException("Invalid hash length.");
        }

        return new ContractBytes32Value(hash);
    }
}
