package com.decurret_dcp.dcjpy.core.application.token_spec.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder
public class MintServiceResult {

    public final IssuerId issuerId;

    public final AccountId accountId;

    public final Amount mintAmount;

    public final Balance cacheBalance;

    public final String accountName;
}
