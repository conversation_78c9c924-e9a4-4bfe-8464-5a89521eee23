package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class CreateAccountServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final String accountName;
    public final Amount transferLimit;
    public final Amount chargeLimit;
    public final Amount mintLimit;
    public final Amount burnLimit;
    public final Amount cumulativeLimit;

    public AddAccountCommand toAddAccountCommand() {
        return new AddAccountCommand(
                this.validatorId,
                this.accountId,
                this.accountName,
                this.transferLimit,
                this.chargeLimit,
                this.mintLimit,
                this.burnLimit,
                this.cumulativeLimit);
    }
}
