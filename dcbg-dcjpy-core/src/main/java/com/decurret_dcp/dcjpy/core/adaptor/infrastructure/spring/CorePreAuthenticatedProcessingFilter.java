package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring;

import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.HttpHeaders;
import org.springframework.security.web.authentication.preauth.AbstractPreAuthenticatedProcessingFilter;

import com.decurret_dcp.dcjpy.core.consts.DCFConst;

public class CorePreAuthenticatedProcessingFilter extends AbstractPreAuthenticatedProcessingFilter {
    @Override
    protected Object getPreAuthenticatedPrincipal(HttpServletRequest request) {
        return "";
    }

    @Override
    protected Object getPreAuthenticatedCredentials(HttpServletRequest request) {
        /* NOTE: 本来であれば、springの機能でhealthチェックのリクエストはloadUserDetailsに来ないように
         * できればよかったが、方法が不明のため、healthチェックのリクエストの場合、credentialに
         * 文字列を返すようにする。
         * CoreAuthenticationUserDetailService#loadUserDetails内で判定し処理する。
         */
        if (DCFConst.HEALTH_PATH.equals(request.getRequestURI())) {
            return DCFConst.HEALTH_CREDENTIAL;
        }
        return Optional.ofNullable(request.getHeader(HttpHeaders.AUTHORIZATION)).orElse("");
    }
}
