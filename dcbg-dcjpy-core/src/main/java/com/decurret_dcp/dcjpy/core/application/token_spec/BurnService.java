package com.decurret_dcp.dcjpy.core.application.token_spec;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.BurnServiceCommand;
import com.decurret_dcp.dcjpy.core.application.token_spec.result.BurnServiceResult;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.CheckBurn;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.CheckBurnCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.TokenBurnCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class BurnService {

    private final IssuerHasAccount issuerHasAccount;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final IssuerGetAccount issuerGetAccount;
    private final CheckBurn checkBurn;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * コインの償却前チェックを行う
     *
     * @param command 償却コマンド
     * @return チェック結果
     *     E0005 残高が不足しています
     *     E0001 account_idが見つかりません
     *     E0059 account_idが本人未確認です
     *     E0045 account_idが無効です
     *     E0071 burn_amountが上限を超えています
     *     E0022 burn_amountが1日の上限を超えています
     */
    public BurnServiceResult check(BurnServiceCommand command) {

        // 1.呼び出し元のゾーンIDを確認
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // 2.チェック処理前の残高キャッシュから残高を取得する
        BalanceCache beforeBalanceCache = this.findBalanceCache(command, zoneId);

        // 3.アカウント情報を取得する
        IssuerGetAccountResult accountResult
                = this.issuerGetAccount.execute(command.issuerId, command.accountId, callBlockchainContractService);

        // 4.償却額をセット
        Amount contractBurnAmount = (command.fullBurn == false)
                ? command.burnAmount : new Amount(beforeBalanceCache.balance.getValue());
        EntityId signerId = new EntityId(command.issuerId);
        CheckBurnCommand checkBurnCommand = new CheckBurnCommand(command.issuerId, command.accountId, contractBurnAmount);
        // 償却実行可否チェック
        this.checkBurn.execute(checkBurnCommand, signerId, callBlockchainContractService);

        // 5.チェック処理後の残高キャッシュから残高を取得する
        BalanceCache afterBalanceCache = this.findBalanceCache(command, zoneId);
        if (command.fullBurn == false) {
            if (command.burnAmount.getValue().compareTo(afterBalanceCache.balance.getValue()) > 0) {
                // 償却額 > キャッシュ残高の場合エラー
                throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
            }
        }

        return BurnServiceResult.builder()
                .issuerId(command.issuerId)
                .accountId(command.accountId)
                .burnAmount(contractBurnAmount)
                .accountName(accountResult.accountName)
                .build();
    }

    /**
     * コイン償却を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コイン償却リクエストに失敗した場合
     *     E0001 account_idが見つかりません
     *     E0005 残高が不足しています
     *     E0059 account_idが本人未確認です
     *     E0045 account_idが無効です
     *     E0022 burn_amountが1日の上限を超えています
     *     E0071 burn_amountが上限を超えています
     */
    @Transactional(readOnly = true)
    public BurnServiceResult execute(BurnServiceCommand command) throws BadRequestException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone() == false) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // 1.残高キャッシュから残高を取得する
        BalanceCache beforeBalanceCache = this.findBalanceCache(command, zoneId);

        // イシュアとアカウントの紐付けチェック
        this.checkAccount(command);

        // 償却額をセット
        Amount contractBurnAmount = (command.fullBurn == false)
                ? command.burnAmount : new Amount(beforeBalanceCache.balance.getValue());
        EntityId signerId = new EntityId(command.issuerId);
        CheckBurnCommand checkBurnCommand = new CheckBurnCommand(command.issuerId, command.accountId, contractBurnAmount);
        // 償却実行可否チェック
        this.checkBurn.execute(checkBurnCommand, signerId, callBlockchainContractService);

        // 5.チェック処理後の残高キャッシュから残高を取得する
        BalanceCache afterBalanceCache = this.findBalanceCache(command, zoneId);
        if (command.fullBurn == false) {
            if (command.burnAmount.getValue().compareTo(afterBalanceCache.balance.getValue()) > 0) {
                // 償却額 > キャッシュ残高の場合エラー
                throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
            }
        }

        // FinZoneコインを償却する
        TokenBurnCommand tokenBurnCommand = command.toTokenBurnCommand(contractBurnAmount);
        sendBlockchainContractService.executeWithoutSignature(tokenBurnCommand);

        // 残高キャッシュの更新
        BalanceCache updatedBalancerCache;
        try {
            updatedBalancerCache =
                    this.balanceCacheRepository.subtractBalanceCache(command.accountId, zoneId, contractBurnAmount);
        } catch (RuntimeException exc) {
            log.error("Failed to subtract balance cache after burned. {}", command, exc);
            throw exc;
        }

        return BurnServiceResult.builder()
                .issuerId(command.issuerId)
                .accountId(command.accountId)
                .burnAmount(contractBurnAmount)
                .cacheBalance(updatedBalancerCache.balance)
                .build();
    }

    /**
     * イシュアとアカウントの紐付確認
     *
     * @param command 償却のサービスコマンド
     */
    private void checkAccount(BurnServiceCommand command) {
        // Accountチェック
        issuerHasAccount.execute(
                command.issuerId, command.accountId, new IssuerHasAccount.RequestSwitch(), callBlockchainContractService
        );
    }

    /**
     * 残高キャッシュの取得
     * 償却額 > 残高の場合は業務エラーとする
     * @param command サービスコマンド
     * @return 取得結果
     */
    private BalanceCache findBalanceCache(BurnServiceCommand command, ZoneId zoneId) {
        // 1.残高キャッシュから残高を取得する
        return this.balanceCacheRepository.findBalanceCache(command.accountId, zoneId);
    }
}
