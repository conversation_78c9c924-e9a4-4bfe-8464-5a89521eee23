package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckExchangeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.JpyTokenTransferCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.Timeout;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class DischargeServiceCommand {

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public final Amount dischargeAmount;

    public CheckExchangeCommand toCheckCommand(ZoneId fromZoneId) {
        return new CheckExchangeCommand(
                this.accountId,
                fromZoneId,
                ZoneId.getFinancialZone(),
                this.dischargeAmount
        );
    }

    public JpyTokenTransferCommand toTransferCommand(ZoneId fromZoneId, long unixTime) {
        return new JpyTokenTransferCommand(
                this.accountId,
                fromZoneId,
                ZoneId.getFinancialZone(),
                this.dischargeAmount,
                new Timeout(unixTime)
        );
    }
}
