package com.decurret_dcp.dcjpy.core.domain.model.balance_cache;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

public interface BalanceCacheRepository {

    /**
     * 残高キャッシュを取得します
     *
     * @param accountId 取得対象アカウントID
     * @param zoneId 取得対象ゾーンID
     * @return 取得結果
     */
    BalanceCache findBalanceCache(AccountId accountId, ZoneId zoneId);

    /**
     * 残高キャッシュ額を加算します。
     *
     * @param accountId 加算対象アカウントID
     * @param zoneId 加算対象ゾーンID
     * @param addAmount 加算額
     * @return 加算後の残高キャッシュデータ
     */
    BalanceCache addBalanceCache(AccountId accountId, ZoneId zoneId, Amount addAmount);

    /**
     * 残高キャッシュ額を減算します
     *
     * @param accountId 減算対象アカウントID
     * @param zoneId 減算対象ゾーンID
     * @param subtractAmount 減算額(プラスで指定すること)
     * @return 減算後の残高キャッシュデータ
     */
    BalanceCache subtractBalanceCache(AccountId accountId, ZoneId zoneId, Amount subtractAmount);


    void createBalanceCache(AccountId accountId, ZoneId zoneId);
}
