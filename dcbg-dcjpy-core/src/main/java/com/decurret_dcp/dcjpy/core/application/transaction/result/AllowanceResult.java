package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class AllowanceResult {
    public final AccountId ownerId;
    public final AccountId spenderId;
    public final Amount allowance;

    public AllowanceResult(AccountId ownerId, AccountId spenderId, Amount allowance) {
        Assertion.assertNotNull(ownerId, "ownerId");
        Assertion.assertNotNull(spenderId, "spenderId");
        Assertion.assertNotNull(allowance, "allowance");
        this.ownerId = ownerId;
        this.spenderId = spenderId;
        this.allowance = allowance;
    }
}
