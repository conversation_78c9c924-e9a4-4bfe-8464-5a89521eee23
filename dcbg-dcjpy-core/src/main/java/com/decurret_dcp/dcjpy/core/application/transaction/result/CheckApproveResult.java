package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class CheckApproveResult {
    public final AccountId ownerId;
    public final AccountId spenderId;
    public final Amount amount;

    public CheckApproveResult(AccountId ownerId, AccountId spenderId, Amount amount) {
        Assertion.assertNotNull(ownerId, "ownerId");
        Assertion.assertNotNull(spenderId, "spenderId");
        Assertion.assertNotNull(amount, "amount");
        this.ownerId = ownerId;
        this.spenderId = spenderId;
        this.amount = amount;
    }
}
