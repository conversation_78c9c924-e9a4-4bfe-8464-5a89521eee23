package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.UpdateAccountStatusServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.UpdateAccountStatusResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command.SetAccountStatusCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class UpdateAccountStatusService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final IssuerGetAccount getAccount;

    /**
     * アカウントステータスの更新を行います。
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException　アカウントステータスの更新に失敗。
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public UpdateAccountStatusResult execute(UpdateAccountStatusServiceCommand command) throws BadRequestException {

        // 1.アカウントの情報を取得する
        IssuerGetAccountResult getAccountResult
                = getAccount.execute(command.issuerId, command.accountId, callBlockchainContractService);

        // 2.アカウントステータス確認
        AccountStatus currentStatus = AccountStatus.of(getAccountResult.accountStatus.value);
        boolean notFrozen = (currentStatus != AccountStatus.FROZEN);
        boolean notForceBurned = (currentStatus != AccountStatus.FORCE_BURNED);

        // active 状態に変更しようとしているときに、凍結でも強制焼却でもない場合はエラー
        if ((command.toAccountStatus == AccountStatus.ACTIVE) && notFrozen && notForceBurned) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }
        // 凍結状態に変更しようとしているときに、アクティブでない場合はエラー
        if ((command.toAccountStatus == AccountStatus.FROZEN) && (currentStatus != AccountStatus.ACTIVE)) {
            throw new BadRequestException(MessageCode.ACCOUNT_ID_STATUS_IS_INVALID);
        }

        // 3.アカウントステータスの更新
        SetAccountStatusCommand setAccountStatusCommand = command.toSetAccountStatusCommand();
        sendBlockchainContractService.execute(setAccountStatusCommand, new EntityId(command.issuerId));

        return UpdateAccountStatusResult.builder()
                .accountId(command.accountId)
                .accountName(getAccountResult.accountName)
                .accountStatus(command.toAccountStatus)
                .reasonCode(command.reasonCode)
                .build();
    }
}
