package com.decurret_dcp.dcjpy.core.application.sign.response;

import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.PrivateKey;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ExpirationDate;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.PublicKey;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

import lombok.Getter;

@Getter
public class OneTimeKeyResponse {
    PrivateKey skO;
    PublicKey pkO;
    PrivateKey skC;
    PublicKey pkC;
    ExpirationDate pt;
    Signature sigCpt;

    public OneTimeKeyResponse(PrivateKey skO, PublicKey pkO, PrivateKey skC, PublicKey pkC, ExpirationDate pt,
                              Signature sigCpt) {
        Assertion.assertNotNull(skO, "skO");
        Assertion.assertNotNull(pkO, "pkO");
        Assertion.assertNotNull(skC, "skC");
        Assertion.assertNotNull(pkC, "pkC");
        Assertion.assertNotNull(pt, "pkCX");
        Assertion.assertNotNull(sigCpt, "sigCpt");
        this.skO = skO;
        this.pkO = pkO;
        this.skC = skC;
        this.pkC = pkC;
        this.pt = pt;
        this.sigCpt = sigCpt;
    }
}
