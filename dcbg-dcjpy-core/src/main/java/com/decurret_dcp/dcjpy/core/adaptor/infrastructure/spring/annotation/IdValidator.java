package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring.annotation;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class IdValidator implements ConstraintValidator<IdConstraint, String> {
    Pattern p1 = Pattern.compile("^[A-Za-z0-9]{0,32}$");

    @Override
    public boolean isValid(String contactField, ConstraintValidatorContext context) {
        if (contactField == null) {
            return true;
        }
        Matcher m1 = p1.matcher(contactField);
        return m1.matches();
    }
}
