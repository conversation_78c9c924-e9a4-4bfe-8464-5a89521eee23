package com.decurret_dcp.dcjpy.core.application.account;

import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.GetAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.command.GetBizAccountStatusCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.GetAccountAllResult;
import com.decurret_dcp.dcjpy.core.application.account.result.GetBizAccountStatusResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.Account;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockTimestamp;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.FinancialCheckGetAccountLimit;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.FinancialCheckGetBizAccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.result.FinancialCheckGetAccountLimitResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.result.FinancialCheckGetBizAccountStatusResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccountAll;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountAllResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class GetAccountService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final BalanceCacheRepository balanceCacheRepository;
    private final ValidatorGetAccount validatorGetAccount;
    private final ValidatorGetAccountAll validatorGetAccountAll;
    private final FinancialCheckGetAccountLimit financialCheckGetAccountLimit;
    private final FinancialCheckGetBizAccountStatus financialCheckGetBizAccountStatus;
    private final ProviderGetZone getZone;
    private final ClockService clockService;

    /**
     * 共通領域用口座／付加領域用口座の取得を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws NotFoundException 共通領域用口座／付加領域用口座の取得に失敗した場合
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public Account execute(GetAccountServiceCommand command) throws NotFoundException {
        ValidatorId validatorId = command.validatorId;
        AccountId accountId = command.accountId;

        // Validator#getAccountを取得
        ValidatorGetAccountResult getAccountResult =
                validatorGetAccount.execute(validatorId, accountId, callBlockchainContractService);

        // Limit情報を取得する
        FinancialCheckGetAccountLimitResult limitResult =
                financialCheckGetAccountLimit.execute(accountId, callBlockchainContractService);

        // ゾーン情報を取得する
        ProviderGetZoneResult getZoneResult = getZone.execute(callBlockchainContractService);
        // キャッシュ残高を取得
        BalanceCache balanceCache =
                this.balanceCacheRepository.findBalanceCache(command.accountId, getZoneResult.zoneId);

        AppTimeStamp cumulativeDate = this.initConvertCumulativeDate(
                getAccountResult.accountData.registeredAt, limitResult.accountLimitData.cumulativeDate);
        // 最終累積日時が昨日以前の場合、累積額を 0 にする
        Amount cumulativeAmount = this.calcCumulativeAmount(
                cumulativeDate, limitResult.accountLimitData.cumulativeAmount);

        return Account.builder()
                .accountId(accountId)
                .accountName(getAccountResult.accountData.accountName)
                .accountStatus(AccountStatus.of(getAccountResult.accountData.accountStatus.getValue()))
                .reasonCode(new ReasonCode(getAccountResult.accountData.reasonCode.value))
                .zoneId(getZoneResult.zoneId)
                .zoneName(getZoneResult.zoneName)
                .balance(getAccountResult.accountData.balance)
                .cacheBalance(balanceCache.balance)
                .mintLimit(limitResult.accountLimitData.mintLimit)
                .burnLimit(limitResult.accountLimitData.burnLimit)
                .chargeLimit(limitResult.accountLimitData.chargeLimit)
                .transferLimit(limitResult.accountLimitData.transferLimit)
                .cumulativeLimit(limitResult.accountLimitData.cumulativeLimit)
                .cumulativeAmount(cumulativeAmount)
                .cumulativeDate(cumulativeDate)
                .appliedAt(AppTimeStamp.of(getAccountResult.accountData.appliedAt))
                .registeredAt(AppTimeStamp.of(getAccountResult.accountData.registeredAt))
                .terminatingAt(AppTimeStamp.of(getAccountResult.accountData.terminatingAt))
                .terminatedAt(AppTimeStamp.of(getAccountResult.accountData.terminatedAt))
                .build();
    }

    /**
     * 全アカウント情報取得を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws NotFoundException 共通領域用口座／付加領域用口座の取得に失敗した場合
     *     E0001 account_idが見つかりません
     */
    @Transactional(readOnly = true)
    public GetAccountAllResult executeAllZone(GetAccountServiceCommand command) throws NotFoundException {
        ValidatorId validatorId = command.validatorId;
        AccountId accountId = command.accountId;

        // Validator#getAccountAllを取得
        ValidatorGetAccountAllResult getAccountAllResult =
                validatorGetAccountAll.execute(validatorId, accountId, callBlockchainContractService);

        // ゾーンID取得
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // キャッシュ残高を取得
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, zoneId);

        AppTimeStamp cumulativeDate = this.initConvertCumulativeDate(
                getAccountAllResult.accountData.registeredAt, getAccountAllResult.accountData.cumulativeDate);
        // 最終累積日時が昨日以前の場合、累積額を 0 にする
        Amount cumulativeAmount = this.calcCumulativeAmount(
                cumulativeDate, getAccountAllResult.accountData.cumulativeAmount);

        return GetAccountAllResult.initResult(
                command.accountId, getAccountAllResult, balanceCache.balance, cumulativeDate, cumulativeAmount
        );
    }

    /**
     * FinZoneアカウントのBizZoneのアカウントステータスを取得します
     *
     * @param command コマンド
     * @return 実行結果
     * @throws ForbiddenException 権限がない場合
     *     E0003 権限がありません
     */
    public GetBizAccountStatusResult executeGetBizAccountStatus(GetBizAccountStatusCommand command) throws ForbiddenException{
        AccountId accountId = command.accountId;

        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        // FinZone の場合は実行できない
        if (zoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // FinancialCheck#GetBizAccountStatus を実行
        FinancialCheckGetBizAccountStatusResult getBizAccountStatusResult =
                financialCheckGetBizAccountStatus.execute(accountId, zoneId, callBlockchainContractService);

        return GetBizAccountStatusResult.initResult(accountId, getBizAccountStatusResult);
    }


    private AppTimeStamp initConvertCumulativeDate(BlockTimestamp registeredAt, BlockTimestamp cumulativeDate) {
        // 初回取得時は0なので登録日時に変換する
        AppTimeStamp cumulativeDateAppTimeStamp = AppTimeStamp.of(cumulativeDate);
        if (cumulativeDateAppTimeStamp != null) {
            return cumulativeDateAppTimeStamp;
        }

        return AppTimeStamp.of(registeredAt);
    }

    private Amount calcCumulativeAmount(AppTimeStamp cumulativeDate, Amount limit) {
        Instant cuurentInstant = clockService.instant();
        AppTimeStamp current = AppTimeStamp.of(cuurentInstant);

        // ZonedDateTime の ZoneId は同じなので、LocalDate に変換して同一日付が比較する
        LocalDate currentLocalDate = current.zonedDateTime().toLocalDate();
        LocalDate cumulativeLocalDate = cumulativeDate.zonedDateTime().toLocalDate();

        // 累積額は、最終累積日時が昨日以前の場合、0にする。
        return currentLocalDate.equals(cumulativeLocalDate) ? limit : new Amount(BigInteger.ZERO);
    }
}
