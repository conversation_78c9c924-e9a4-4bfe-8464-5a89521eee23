package com.decurret_dcp.dcjpy.core.application.transaction;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.DischargeServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.CheckDischargeResult;
import com.decurret_dcp.dcjpy.core.application.transaction.result.DischargeResult;
import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.CheckExchange;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckExchangeCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.JpyTokenTransferCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class DischargeService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final ClockService clockService;
    private final CheckExchange checkExchange;
    private final ValidatorGetAccount validatorGetAccount;
    private final ProviderGetZone getZone;
    private final DcfConfig config;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * ディスチャージ前確認を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コインのディスチャージ前確認がNGの場合
     *    E0014 権限がありません
     *    E0001 account_idが見つかりません
     *    E0005 残高が不足しています
     *    E0045 account_idが無効です
     *    E0025 charge_amountが1日の上限を超えています
     *    E0073 charge_amountが上限を超えています
     */
    @Transactional(readOnly = true)
    public CheckDischargeResult check(DischargeServiceCommand command) throws BadRequestException {
        ZoneId fromZoneId = ZoneIdThreadLocalHolder.getZoneId();

        // アカウントのチェック
        // ディスチャージ前確認
        return this.commonCheck(command, fromZoneId);
    }

    private CheckDischargeResult commonCheck(DischargeServiceCommand command, ZoneId fromZoneId) {
        // FinZone の場合はディスチャージを実行できない
        if (fromZoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // account情報取得
        ValidatorGetAccountResult accountResult
                = validatorGetAccount.execute(command.validatorId, command.accountId, callBlockchainContractService);

        // 残高確認
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, fromZoneId);
        Balance bizZoneBalance = balanceCache.balance;

        if (command.dischargeAmount.value.compareTo(bizZoneBalance.value) > 0) {
            throw new BadRequestException(MessageCode.BALANCE_NOT_ENOUGH);
        }

        // FinZoneでのAccountの有効性の確認を行う
        CheckExchangeCommand checkExchangeCommand = command.toCheckCommand(fromZoneId);
        checkExchange.execute(checkExchangeCommand, callBlockchainContractService);

        // BizZoneのZone名を取得
        ProviderGetZoneResult bizZone = getZone.execute(callBlockchainContractService);

        return CheckDischargeResult.builder()
                .accountId(command.accountId)
                .accountName(accountResult.accountData.accountName)
                .fromZoneId(fromZoneId)
                .fromZoneName(bizZone.zoneName)
                .dischargeAmount(command.dischargeAmount)
                .build();
    }


    /**
     * ディスチャージを行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException コインのチャージがNGの場合
     *    E0028 provider_idが見つかりません
     *    E0005 残高が不足しています
     *    E0001 account_idが見つかりません
     *    E0059 account_idが本人未確認です
     *    E0045 account_idが無効です
     *    E0025 charge_amountが1日の上限を超えています
     *    E0073 charge_amountが上限を超えています
     */
    @Transactional(readOnly = true)
    public DischargeResult execute(DischargeServiceCommand command) throws BadRequestException {
        ZoneId fromZoneId = ZoneIdThreadLocalHolder.getZoneId();

        // アカウントのチェック
        // チャージ前確認
        CheckDischargeResult checkDischargeResult = this.commonCheck(command, fromZoneId);

        // コインのディスチャージ実行
        this.transfer(command, fromZoneId);

        // 残高更新
        BalanceCache updatedBalancerCache;
        try {
            updatedBalancerCache = this.balanceCacheRepository.subtractBalanceCache(
                    command.accountId, fromZoneId, command.dischargeAmount
            );
        } catch (RuntimeException exc) {
            log.error("Failed to subtract balance cache after discharged. {}", command, exc);
            throw exc;
        }

        return DischargeResult.builder()
                .accountId(command.accountId)
                .accountName(checkDischargeResult.accountName)
                .fromZoneId(checkDischargeResult.fromZoneId)
                .fromZoneName(checkDischargeResult.fromZoneName)
                .dischargeAmount(command.dischargeAmount)
                .balance(updatedBalancerCache.balance)
                .build();
    }

    private void transfer(DischargeServiceCommand command, ZoneId fromZoneId)
            throws BadRequestException {

        // 現在時刻+timeoutTimestamp(秒)のUNIX TIMEをタイムアウト値にする
        long unixTime = clockService.instant().getEpochSecond() + config.getTimeoutTimestamp();

        JpyTokenTransferCommand transferCommand = command.toTransferCommand(fromZoneId, unixTime);

        EntityId entityId = new EntityId(command.validatorId);
        sendBlockchainContractService.execute(transferCommand, entityId);
    }
}
