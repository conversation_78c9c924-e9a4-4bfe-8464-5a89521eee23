package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;
import com.decurret_dcp.dcjpy.core.fundamental.SortOrder;

public class GetAccountListServiceCommand {
    public final ValidatorId validatorId;
    public final Offset offset;
    public final Limit limit;
    public final SortOrder sortOrder;

    public GetAccountListServiceCommand(ValidatorId validatorId, Offset offset, Limit limit, SortOrder sortOrder) {

        Assertion.assertNotNull(validatorId, "validatorId");
        Assertion.assertNotNull(offset, "offset");
        Assertion.assertNotNull(limit, "limit");
        Assertion.assertNotNull(sortOrder, "sortOrder");
        this.validatorId = validatorId;
        this.offset = offset;
        this.limit = limit;
        this.sortOrder = sortOrder;
    }
}