package com.decurret_dcp.dcjpy.core.application.provider;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.provider.command.CreateProviderServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.command.ProviderAddProviderCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.provider.Provider;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderId;
import com.decurret_dcp.dcjpy.core.domain.model.provider.ProviderIdGenerator;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CreateProviderService {

    private final ProviderIdGenerator providerIdGenerator;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * プロバイダの作成を行います
     * @param command コマンド
     * @return 作成したプロバイダ
     */
    @Transactional(readOnly = true)
    public Provider execute(CreateProviderServiceCommand command) {
        ProviderId providerId = providerIdGenerator.generate();
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        ProviderAddProviderCommand addProviderCommand = command.initContractCommand(providerId, zoneId);
        EntityId signerId = new EntityId(command.adminId);

        sendBlockchainContractService.execute(addProviderCommand, signerId);

        return Provider.builder()
                .providerId(providerId)
                .zoneId(zoneId)
                .zoneName(command.zoneName)
                .build();
    }
}
