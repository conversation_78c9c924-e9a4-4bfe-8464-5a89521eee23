package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.nft_metadata;

import java.util.List;

import org.seasar.doma.Dao;
import org.seasar.doma.Insert;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;
import org.seasar.doma.jdbc.Result;

import com.decurret_dcp.dcjpy.core.domain.model.nft_metadata.NftMetadataEntity;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

@ConfigAutowireable
@Dao
public interface NftMetadataDao {

    @Insert(sqlFile = true)
    Result<NftMetadataEntity> insert(NftMetadataEntity nftMetadata);

    @Select
    List<NftMetadataEntity> selectForMetadataId(String nftId, ZoneId zoneId, List<MetadataId> metadataIdList);
}
