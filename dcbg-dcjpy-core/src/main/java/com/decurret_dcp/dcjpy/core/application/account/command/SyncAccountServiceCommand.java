package com.decurret_dcp.dcjpy.core.application.account.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account_sync_bridge.command.SyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckSyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.jpy_token_transfer_bridge.Timeout;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class SyncAccountServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId accountId;
    public final String accountName;
    public final Amount approvalAmount;
    public final Signature accountSignature;
    public final Info info;

    public CheckSyncAccountCommand toCheckSyncAccountCommand(ZoneId zoneId, AccountStatus accountStatus) {
        return new CheckSyncAccountCommand(
                this.validatorId,
                this.accountId,
                zoneId,
                accountStatus,
                this.accountSignature,
                this.info);
    }

    public SyncAccountCommand toSyncAccountCommand(
            ZoneId zoneId, String zoneName, AccountStatus accountStatus, long unixTime
    ) {
        var timeout = new Timeout(unixTime);
        return new SyncAccountCommand(
                this.validatorId,
                this.accountId,
                this.accountName,
                zoneId,
                zoneName,
                accountStatus,
                this.approvalAmount,
                timeout
        );
    }
}
