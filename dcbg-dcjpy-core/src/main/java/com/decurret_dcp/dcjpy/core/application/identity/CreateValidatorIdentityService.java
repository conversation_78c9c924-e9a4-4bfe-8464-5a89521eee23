package com.decurret_dcp.dcjpy.core.application.identity;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.identity.command.CreateValidatorIdentityServiceCommand;
import com.decurret_dcp.dcjpy.core.application.identity.result.CreateValidatorIdentityResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.HasIssuer;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.HasValidator;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddValidatorRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntity;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntityRepository;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentity;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentityManager;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.UserPoolClient;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerIdNullable;
import com.decurret_dcp.dcjpy.core.domain.model.secret.CredentialStorage;
import com.decurret_dcp.dcjpy.core.domain.model.secret.Oauth2Secret;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CreateValidatorIdentityService {

    private final SendBlockchainContractService sendBlockchainContractService;
    private final CallBlockchainContractService callBlockchainContractService;
    private final HasValidator hasValidator;
    private final HasIssuer hasIssuer;
    private final EntityIdentityManager entityIdentityManager;
    private final ClientEntityRepository clientEntityRepository;
    private final CredentialStorage credentialStorage;

    /**
     * バリデータアイデンティティの作成を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException バリデータアイデンティティの作成に失敗した場合
     *     E0042 validator_idが見つかりません
     *     E0028 provider_idが見つかりません
     *     E0043 issuer_idは必須項目です
     *     E0017 issuer_idが見つかりません
     *     E0016 issuer_idが無効です
     *     E0044 issuer_idは指定出来ません
     */
    @Transactional
    public CreateValidatorIdentityResult execute(CreateValidatorIdentityServiceCommand command)
            throws BadRequestException {

        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        hasValidator.execute(command.validatorId, new HasValidator.PathValueSwitch(), callBlockchainContractService);
        IssuerIdNullable issuerId = command.issuerId;

        this.checkIssuer(issuerId, zoneId);

        EntityIdentity identity = this.entityIdentityManager.saveIdentity(command.validatorId, zoneId);

        // コントラクト登録
        AddValidatorRoleCommand addValidatorRoleCommand = command.toAddValidatorRoleCommand(identity.eoa);
        EntityId adminEntityId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(addValidatorRoleCommand, adminEntityId);

        UserPoolClient userPoolClient = identity.userPoolClient;

        EntityId validatorEntityId = new EntityId(command.validatorId);
        Oauth2Secret clientSecret = Oauth2Secret.builder()
                .clientSecret(userPoolClient.getClientSecret())
                .build();

        this.credentialStorage.saveClientSecret(validatorEntityId, clientSecret);

        return CreateValidatorIdentityResult.builder()
                .validatorId(command.validatorId)
                .issuerId(issuerId)
                .clientId(userPoolClient.getClientId())
                .clientSecret(userPoolClient.getClientSecret())
                .build();
    }

    private void checkIssuer(IssuerIdNullable issuerId, ZoneId zoneId) {
        if (zoneId.isFinancialZone() == false) {
            if (issuerId.isEmpty() == false) {
                throw new BadRequestException(MessageCode.ISSUER_ID_BE_EMPTY);
            }

            return;
        }

        // 以降、FinancialZone の場合のチェク処理

        if (issuerId.isEmpty()) {
            throw new BadRequestException(MessageCode.ISSUER_ID_NOT_EMPTY);
        }

        EntityId issuerIdEntity = new EntityId(new IssuerId(issuerId.value));
        ClientEntity clientIssuerEntity = clientEntityRepository.selectByEntityId(issuerIdEntity);

        // イシュアのClientEntityが存在するかをチェック
        if (clientIssuerEntity == null) {
            throw new NotFoundException(MessageCode.ISSUER_ID_NOT_FOUND);
        }

        // コントラクトにイシュアIDが存在するかチェック
        hasIssuer.execute(
                new IssuerId(issuerId.getValue()),
                new HasIssuer.RequestSwitch(),
                callBlockchainContractService
        );
    }
}
