package com.decurret_dcp.dcjpy.core.application.issuer.command;

import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.fundamental.Assertion;

public class GetIssuerServiceCommand {

    public final IssuerId issuerId;

    public GetIssuerServiceCommand(IssuerId issuerId) {
        Assertion.assertNotNull(issuerId, "issuerId");
        this.issuerId = issuerId;
    }
}
