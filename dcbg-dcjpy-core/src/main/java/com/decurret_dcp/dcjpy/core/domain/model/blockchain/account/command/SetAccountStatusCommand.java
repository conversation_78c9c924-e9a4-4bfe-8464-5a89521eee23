package com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.command;

import java.util.List;
import java.util.Map;

import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Bytes32;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.account.ReasonCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractBytes32Value;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractName;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.ContractStringValue;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.TraceIdThreadLocalHolder;

import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class SetAccountStatusCommand extends BlockchainContractCommand {

    public SetAccountStatusCommand(
            IssuerId issuerId, AccountId accountId, AccountStatus accountStatus, ReasonCode reasonCode
    ) {
        super(ContractName.ISSUER, "setAccountStatus",
              Map.ofEntries(
                      Map.entry("issuerId", new ContractBytes32Value(issuerId.getValue())),
                      Map.entry("accountId", new ContractBytes32Value(accountId.getValue())),
                      Map.entry("accountStatus", new ContractBytes32Value(accountStatus.getValue())),
                      Map.entry("reasonCode", new ContractBytes32Value(reasonCode.getValue())),
                      Map.entry("traceId", new ContractBytes32Value(TraceIdThreadLocalHolder.getTraceId().getValue()))
              )
        );
    }

    @Override
    public List<Type<?>> asAbiParams() {
        return List.of(
                new Bytes32(((ContractBytes32Value) getArgs().get("issuerId")).asBytes32()),
                new Bytes32(((ContractBytes32Value) getArgs().get("accountId")).asBytes32()),
                new Bytes32(((ContractBytes32Value) getArgs().get("reasonCode")).asBytes32())
        );
    }
}
