package com.decurret_dcp.dcjpy.core.application.transaction.command;

import org.seasar.doma.jdbc.SelectOptions;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.command.TransactionListForAllAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.SortOrder;

import lombok.Builder;

@Builder
public class GetAllAccountTransactionListServiceCommand {

    public final ValidatorId validatorId;
    public final Offset offset;
    public final Limit limit;
    public final AppTimeStamp from;
    public final AppTimeStamp to;
    public final SortOrder dateSort;

    public TransactionListForAllAccountCommand create(SelectOptions options) {

        return TransactionListForAllAccountCommand.builder()
                .validatorId(this.validatorId)
                .from(this.from)
                .to(this.to)
                .dateSort(this.dateSort)
                .build();
    }
}
