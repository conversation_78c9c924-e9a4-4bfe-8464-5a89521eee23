package com.decurret_dcp.dcjpy.core.application.identity;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.identity.command.CreateIssuerIdentityServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.HasIssuer;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.AddIssuerRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentity;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentityManager;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.UserPoolClient;
import com.decurret_dcp.dcjpy.core.domain.model.secret.CredentialStorage;
import com.decurret_dcp.dcjpy.core.domain.model.secret.Oauth2Secret;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class CreateIssuerIdentityService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final HasIssuer hasIssuer;
    private final EntityIdentityManager entityIdentityManager;
    private final CredentialStorage credentialStorage;

    /**
     * イシュアアイデンティティの作成を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException イシュアアイデンティティの作成に失敗した場合
     *     E0017 issuer_idが見つかりません
     *     E0016 issuer_idが無効です
     */
    @Transactional
    public UserPoolClient execute(CreateIssuerIdentityServiceCommand command) throws BadRequestException {
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        hasIssuer.execute(command.issuerId, new HasIssuer.PathValueSwitch(), callBlockchainContractService);

        EntityIdentity identity = this.entityIdentityManager.saveIdentity(command.issuerId, zoneId);

        AddIssuerRoleCommand addIssuerRoleCommand = command.toAddIssuerRoleCommand(identity.eoa);
        EntityId adminEntityId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(addIssuerRoleCommand, adminEntityId);

        EntityId issuerEntityId = new EntityId(command.issuerId);
        Oauth2Secret clientSecret = Oauth2Secret.builder()
                .clientSecret(identity.userPoolClient.getClientSecret())
                .build();

        this.credentialStorage.saveClientSecret(issuerEntityId, clientSecret);

        return identity.userPoolClient;
    }
}
