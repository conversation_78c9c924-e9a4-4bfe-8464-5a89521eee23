package com.decurret_dcp.dcjpy.core.application.validator;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.validator.command.CreateValidatorServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.HasIssuer;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddValidatorCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.validator.Validator;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorIdGenerator;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CreateValidatorService {

    private final HasIssuer hasIssuer;
    private final ValidatorIdGenerator idGenerator;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final CallBlockchainContractService callBlockchainContractService;

    /**
     * バリデータの作成を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException バリデータの作成に失敗した場合
     *     E0028 provider_idが見つかりません
     *     E0043 issuer_idは必須項目です
     *     E0044 issuer_idは指定出来ません
     *     E0017 issuer_idが見つかりません
     *     E0016 issuer_idが無効です
     *     E0075 このrequest_idは処理済です
     */
    @Transactional(readOnly = true)
    public Validator execute(CreateValidatorServiceCommand command) throws BadRequestException {
        // ZoneIdを取得
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        this.checkIssuer(command, zoneId);

        ValidatorId validatorId = idGenerator.generate();

        AddValidatorCommand addValidatorCommand = command.toAddValidatorCommand(validatorId);
        EntityId entityId = new EntityId(command.adminId);
        sendBlockchainContractService.execute(addValidatorCommand, entityId);

        return Validator.builder()
                .validatorId(validatorId)
                .name(command.validatorName)
                .issuerId(command.issuerId)
                .build();
    }

    private void checkIssuer(CreateValidatorServiceCommand command, ZoneId zoneId) {
        if (zoneId.isFinancialZone() == false) {
            // BizZoneでイシュアIDがセットされている場合エラー
            if (command.issuerId.isEmpty() == false) {
                throw new BadRequestException(MessageCode.ISSUER_ID_BE_EMPTY);
            }

            return;
        }

        // FinZoneの場合は、発行者存在チェックを実行
        if (command.issuerId.isEmpty()) {
            throw new BadRequestException(MessageCode.ISSUER_ID_NOT_EMPTY);
        }

        hasIssuer.execute(
                new IssuerId(command.issuerId.getValue()),
                new HasIssuer.RequestSwitch(),
                callBlockchainContractService
        );
    }
}
