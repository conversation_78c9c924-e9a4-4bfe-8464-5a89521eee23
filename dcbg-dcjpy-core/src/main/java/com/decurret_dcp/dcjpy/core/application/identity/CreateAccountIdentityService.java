package com.decurret_dcp.dcjpy.core.application.identity;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.identity.command.CreateAccountIdentityServiceCommand;
import com.decurret_dcp.dcjpy.core.application.identity.result.CreateAccountIdentityResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.AddAccountRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.EntityIdentityManager;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntitySigner;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class CreateAccountIdentityService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final IssuerHasAccount hasAccount;
    private final EntityIdentityManager entityIdentityManager;

    /**
     * FinZone用口座アイデンティティの作成を行います。
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 共通領域用口座アイデンティティの作成に失敗した場合
     *     E0001 account_idが見つかりません
     */
    @Transactional
    public CreateAccountIdentityResult execute(CreateAccountIdentityServiceCommand command)
            throws BadRequestException {

        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        hasAccount.execute(command.issuerId,
                           command.accountId,
                           new IssuerHasAccount.PathValueSwitch(),
                           callBlockchainContractService
        );

        EntitySigner entitySigner = this.entityIdentityManager.saveKey(command.accountId, zoneId);

        AddAccountRoleCommand addAccountRoleCommand = command.toAddAccountRoleCommand(entitySigner);
        EntityId entityId = new EntityId(command.issuerId);
        sendBlockchainContractService.execute(addAccountRoleCommand, entityId);

        return new CreateAccountIdentityResult(command.accountId);
    }
}
