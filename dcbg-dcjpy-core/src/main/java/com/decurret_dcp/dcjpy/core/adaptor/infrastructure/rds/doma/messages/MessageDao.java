package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.rds.doma.messages;

import java.util.List;

import org.seasar.doma.Dao;
import org.seasar.doma.Select;
import org.seasar.doma.boot.ConfigAutowireable;

import com.decurret_dcp.dcjpy.core.domain.model.message.Message;

@ConfigAutowireable
@Dao
public interface MessageDao {
    @Select
    List<Message> selectAll();

    @Select
    Message selectByMessageCode(String messageCode);
}
