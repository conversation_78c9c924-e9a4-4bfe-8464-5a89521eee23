package com.decurret_dcp.dcjpy.core.application.account;

import java.math.BigInteger;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.OrderTerminatingBizAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.OrderTerminatingBizAccountServiceResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.clock.ClockService;
import com.decurret_dcp.dcjpy.core.config.DcfConfig;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCache;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account_sync_bridge.command.SyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.CheckSyncAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckSyncAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.ProviderGetZone;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.provider.result.ProviderGetZoneResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorGetAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class OrderTerminatingBizAccountService {

    private final ProviderGetZone providerGetZone;

    private final ValidatorGetAccount validatorGetAccount;

    private final CheckSyncAccount checkSyncAccount;

    private final CallBlockchainContractService callBlockchainContractService;

    private final SendBlockchainContractService sendBlockchainContractService;

    private final ClockService clockService;

    private final DcfConfig config;

    private final BalanceCacheRepository balanceCacheRepository;

    @Transactional
    public OrderTerminatingBizAccountServiceResult execute(OrderTerminatingBizAccountServiceCommand command) {

        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();
        if (zoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        ProviderGetZoneResult zone = findZone();

        // Validator#getAccount()を呼び出す
        ValidatorGetAccountResult getAccountResult = getAccount(command);

        // FinancialCheck#checkSyncAccount()を呼び出す
        EntityId signerId = new EntityId(command.validatorId);
        checkSyncAccount(command, zoneId, signerId);

        // 残高キャッシュのチェック
        BalanceCache balanceCache = this.balanceCacheRepository.findBalanceCache(command.accountId, zoneId);
        if (!balanceCache.balance.isZero()) {
            // 残高キャッシュの残高が0でない場合はエラー
            throw new BadRequestException(MessageCode.BALANCE_EXISTS);
        }

        // AccountSyncBridge#syncAccount()を呼び出す
        syncAccount(command, getAccountResult, zone, signerId);

        return OrderTerminatingBizAccountServiceResult.builder()
                .accountId(command.accountId)
                .zoneid(zoneId)
                .build();
    }

    private ProviderGetZoneResult findZone() {
        return providerGetZone.execute(this.callBlockchainContractService);
    }

    private ValidatorGetAccountResult getAccount(OrderTerminatingBizAccountServiceCommand command) {
        ValidatorGetAccountResult account = validatorGetAccount.execute(
                command.validatorId, command.accountId, this.callBlockchainContractService);

        boolean existBalance = !account.accountData.balance.value.equals(BigInteger.ZERO);
        if (existBalance) {
            throw new BadRequestException(MessageCode.BALANCE_EXISTS);
        }

        return account;
    }

    private void checkSyncAccount(
            OrderTerminatingBizAccountServiceCommand command, ZoneId zoneId, EntityId signerId
    ) {
        CheckSyncAccountCommand checkCommand = command.toCheckSyncAccountCommand(zoneId);
        checkSyncAccount.execute(checkCommand, signerId, this.callBlockchainContractService);
    }

    private void syncAccount(
            OrderTerminatingBizAccountServiceCommand command, ValidatorGetAccountResult getAccountResult,
            ProviderGetZoneResult zone, EntityId signerId
    ) {
        // 現在時刻+timeoutTimestamp(秒)のUNIX TIMEをタイムアウト値にする
        long unixTime = clockService.instant().getEpochSecond() + config.getTimeoutTimestamp();
        String accountName = getAccountResult.accountData.accountName;
        ZoneId zoneId = zone.zoneId;
        String zoneName = zone.zoneName;
        SyncAccountCommand syncAccountCommand = command.toSyncAccountCommand(accountName, zoneId, zoneName, unixTime);

        sendBlockchainContractService.executeWithoutSignature(syncAccountCommand);
    }
}
