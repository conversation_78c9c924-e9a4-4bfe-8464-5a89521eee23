package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.security.web.firewall.RequestRejectedHandler;

import com.decurret_dcp.dcjpy.core.message.MessageCode;
import com.decurret_dcp.dcjpy.core.presentation.rest.GenericErrorResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

public class CustomRequestRejectedHandler implements RequestRejectedHandler {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       RequestRejectedException requestRejectedException) throws IOException {
        HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
        String code = MessageCode.URL_STRING_INVALID.getCode();
        GenericErrorResponse errorResponse = new GenericErrorResponse(
                code,
                httpStatus.getReasonPhrase(),
                "The URL contains characters that cannot be used"
        );
        response.setStatus(httpStatus.value());
        response.getOutputStream().print(objectMapper.writeValueAsString(errorResponse));
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    }
}
