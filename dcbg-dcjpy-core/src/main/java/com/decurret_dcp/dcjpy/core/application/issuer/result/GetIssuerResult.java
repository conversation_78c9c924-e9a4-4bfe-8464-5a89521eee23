package com.decurret_dcp.dcjpy.core.application.issuer.result;

import com.decurret_dcp.dcjpy.core.application.issuer.command.GetIssuerServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.bank.BankCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetIssuerResult;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.Builder;

@Builder
public class GetIssuerResult {
    public final IssuerId issuerId;
    public final BankCode bankCode;
    public final String name;

    public static GetIssuerResult initResult(IssuerGetIssuerResult contractResult, GetIssuerServiceCommand command) {
        return GetIssuerResult.builder()
                .issuerId(command.issuerId)
                .bankCode(contractResult.bankCode)
                .name(contractResult.name)
                .build();
    }
}
