package com.decurret_dcp.dcjpy.core.application.blockchain;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import com.decurret_dcp.dcjpy.core.application.sign.GenerateExternalSignService;
import com.decurret_dcp.dcjpy.core.domain.model.RequestId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainContractCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainTransactionRevertedException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainClient;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendRequest;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendResponse;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.RequestIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class SendBlockchainContractService {

    private final BlockchainClient blockchainClient;
    private final GenerateExternalSignService generateExternalSignService;

    /**
     * 外部署名つきで/sendリクエストする
     *
     * @param command command
     * @param signerId signerId
     * @return 実行結果
     * @throws IllegalArgumentException commandがnullの場合
     *     E0006 コマンドがnullです
     *     E0075 このrequest_idは処理済です
     *
     * @throws BlockchainTransactionRevertedException コントラクトにsendした結果、エラーが返却された場合
     */
    public BlockchainSendResponse execute(BlockchainContractCommand command, EntityId signerId)
            throws IllegalArgumentException, BlockchainTransactionRevertedException {

        if (command == null) {
            throw new IllegalArgumentException("command is null");
        }

        BlockchainSendRequest request = generateExternalSignService.createBlockchainSendRequest(command, signerId);
        return this.doSend(request);
    }

    /**
     * 外部署名なしで/sendリクエストする
     *
     * @param command コマンド
     * @return 実行結果
     * @throws IllegalArgumentException　commandがnullの場合
     *     E0006 コマンドがnullです
     * @throws BlockchainTransactionRevertedException コントラクトにsendした結果、エラーが返却された場合
     */
    public BlockchainSendResponse executeWithoutSignature(BlockchainContractCommand command)
            throws IllegalArgumentException, BlockchainTransactionRevertedException {

        if (command == null) {
            throw new IllegalArgumentException("command is null");
        }

        RequestId requestId = RequestIdThreadLocalHolder.getRequestId();
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        BlockchainSendRequest request = BlockchainSendRequest.builder()
                .requestId(requestId.getValue())
                .zoneId((zoneId != null) ? zoneId.getValue().toString() : "")
                .contractName(command.getContractName().getValue())
                .method(command.getMethod())
                .args(createContractArgs(command))
                .isAsync(command.isAsync())
                .build();

        return this.doSend(request);
    }

    private Map<String, Object> createContractArgs(BlockchainContractCommand command) {

        Map<String, Object> args = new HashMap<>();
        command.getArgs().forEach((name, value) -> args.put(name, value.getValue()));
        return args;
    }

    private BlockchainSendResponse doSend(BlockchainSendRequest request) {
        BlockchainSendResponse response;
        try {
            response = blockchainClient.send(request);
        } catch (HttpClientErrorException exc) {
            HttpStatus httpStatus = exc.getStatusCode();

            // request_id 重複エラー
            if (httpStatus == HttpStatus.CONFLICT) {
                throw new BadRequestException(MessageCode.REQUEST_ID_PROCESSED, exc);
            }

            throw exc;
        }

        // BCClient より正常に処理された結果が返却された場合は正常終了
        if (response.result == true) {
            return response;
        }

        String code = "";
        String[] strList = response.error.split(":");
        if (strList.length > 1) {
            code = strList[0];
        }

        throw new BlockchainTransactionRevertedException(response.error, response.transactionHash, code);
    }
}
