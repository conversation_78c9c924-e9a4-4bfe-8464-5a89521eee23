package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;

import lombok.Builder;

@Builder
public class CheckTransferResult {
    public final AccountId sendAccountId;
    public final AccountId fromAccountId;
    public final AccountId toAccountId;
    public final String toAccountName;
    public final Amount transferAmount;
    public final String memo;
}
