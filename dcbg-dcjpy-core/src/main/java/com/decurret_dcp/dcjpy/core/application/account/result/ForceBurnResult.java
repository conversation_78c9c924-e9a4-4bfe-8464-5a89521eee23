package com.decurret_dcp.dcjpy.core.application.account.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;

import lombok.Builder;

@Builder
public class ForceBurnResult {

    public final AccountId accountId;

    public final String accountName;

    public final Balance balance;

    public final Amount burnAmount;
}
