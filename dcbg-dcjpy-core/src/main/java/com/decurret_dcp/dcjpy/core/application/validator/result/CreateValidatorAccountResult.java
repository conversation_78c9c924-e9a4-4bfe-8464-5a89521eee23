package com.decurret_dcp.dcjpy.core.application.validator.result;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountStatus;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;

import lombok.Builder;

@Builder
public class CreateValidatorAccountResult {

    /** アカウント名 */
    public final String accountName;

    /** アカウント状態 */
    public final AccountStatus accountStatus;

    /** アカウント残高 */
    public final Balance balance;

    /** 利用申込日時 */
    public final AppTimeStamp appliedAt;

    /** 利用確定日時 */
    public final AppTimeStamp registeredAt;

    /** 解約申込日時 */
    public final AppTimeStamp terminatingAt;

    /** 解約確定日時 */
    public final AppTimeStamp terminatedAt;

    public static CreateValidatorAccountResult create(ValidatorGetAccountResult result) {
        return CreateValidatorAccountResult.builder()
                .accountName(result.accountData.accountName)
                .accountStatus(AccountStatus.of(result.accountData.accountStatus.getValue()))
                .balance(result.accountData.balance)
                .appliedAt(AppTimeStamp.of(result.accountData.appliedAt))
                .registeredAt(AppTimeStamp.of(result.accountData.registeredAt))
                .terminatedAt(AppTimeStamp.of(result.accountData.terminatingAt))
                .terminatedAt(AppTimeStamp.of(result.accountData.terminatedAt))
                .build();
    }
}
