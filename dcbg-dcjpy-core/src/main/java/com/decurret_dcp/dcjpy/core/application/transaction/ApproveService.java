package com.decurret_dcp.dcjpy.core.application.transaction;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.transaction.command.ApproveServiceCommand;
import com.decurret_dcp.dcjpy.core.application.transaction.result.ApproveResult;
import com.decurret_dcp.dcjpy.core.application.transaction.result.CheckApproveResult;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainCallErrorException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.BlockchainTransactionRevertedException;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.account.IsTerminated;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckApproveCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.ApproveCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.ValidatorHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainCallResponse;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.error.ContractError;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ApproveService {

    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final ValidatorHasAccount validatorHasAccount;
    private final IsTerminated isTerminated;

    /**
     * 送金許可設定前確認を行う
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 送金許可設定前確認の結果がNGの場合
     *    E0064 owner_account_idとspender_account_idに同じIDは指定出来ません
     *    E0020 owner_account_idが見つかりません
     *    E0021 spender_account_idが見つかりません
     *    E0035 spender_account_idは解約済アカウントです
     *    E0001 spender_account_idは解約済アカウントです
     *    E0059 account_idが本人未確認です
     *    E0045 account_idが無効です
     *    E0062 account_signatureが不正です
     *    E0063 infoが不正です
     */
    @Transactional(readOnly = true)
    public CheckApproveResult check(ApproveServiceCommand command) throws BadRequestException {
        checkCommon(command);
        CheckApproveCommand checkApproveCommand = command.toCheckCommand();
        EntityId signerId = new EntityId(command.validatorId);

        BlockchainCallResponse response = callBlockchainContractService.execute(checkApproveCommand, signerId);
        Object err = response.data.get("err");
        if (err != null && !"".equals(err.toString())) {
            String errStr = ContractError.substrErrorCode(err.toString());
            handleBusinessError(errStr);

            //業務エラーとならない場合は、BlockchainCallErrorExceptionをスローする
            throw new BlockchainCallErrorException(err.toString());
        }

        return new CheckApproveResult(command.ownerId, command.spenderId, command.amount);
    }

    /**
     * 送金許可設定を行う
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException 送金許可設定の結果がNGの場合
     *    E0064 owner_account_idとspender_account_idに同じIDは指定出来ません
     *    E0020 owner_account_idが見つかりません
     *    E0021 spender_account_idが見つかりません
     *    E0035 spender_account_idは解約済アカウントです
     *    E0001 spender_account_idは解約済アカウントです
     *    E0059 account_idが本人未確認です
     *    E0045 account_idが無効です
     *    E0062 account_signatureが不正です
     *    E0063 infoが不正です
     */
    @Transactional(readOnly = true)
    public ApproveResult execute(ApproveServiceCommand command) throws BadRequestException {
        checkCommon(command);
        ApproveCommand approveCommand = command.toApproveCommand();
        EntityId signerId = new EntityId(command.validatorId);
        try {
            sendBlockchainContractService.execute(approveCommand, signerId);
        } catch (BlockchainTransactionRevertedException exc) {
            handleBusinessError(exc.getCode());
            //業務エラーとならない場合は、そのままExceptionをスローする
            throw exc;
        }

        return new ApproveResult(command.ownerId, command.spenderId, command.amount);
    }

    private void checkCommon(ApproveServiceCommand command) {
        if (command.ownerId.getValue().equals(command.spenderId.getValue())) {
            throw new BadRequestException(MessageCode.OWNER_ACCOUNT_ID_SPENDER_ACCOUNT_ID_SPENDER_INVALID);
        }

        validatorHasAccount.execute(command.validatorId,
                                    command.ownerId,
                                    MessageCode.OWNER_ACCOUNT_ID_NOT_FOUND,
                                    new ValidatorHasAccount.PathValueSwitch(),
                                    callBlockchainContractService);

        isTerminated.isTrueOrNotExistThrowBadRequestException(command.spenderId,
                                                              MessageCode.SPENDER_ACCOUNT_ID_NOT_FOUND,
                                                              MessageCode.SPENDER_ACCOUNT_ID_STATUS_IS_VALID,
                                                              callBlockchainContractService);
    }

    private void handleBusinessError(String errorCode) {
        Map<String, MessageCode> badRequestExceptionCodeMap = initBadRequestExceptionCodeMap();

        if (badRequestExceptionCodeMap.containsKey(errorCode)) {
            throw new BadRequestException(badRequestExceptionCodeMap.get(errorCode));
        }
    }

    private Map<String, MessageCode> initBadRequestExceptionCodeMap() {
        Map<String, MessageCode> errorCodeMap = new HashMap<>(6, 1.0F);
        errorCodeMap.put(ContractError.ACCOUNT_INVALID_VALUE.getErrorCode(), MessageCode.ACCOUNT_ID_NOT_FOUND);
        errorCodeMap.put(ContractError.ACCOUNT_ID_NOT_EXIST.getErrorCode(), MessageCode.ACCOUNT_ID_NOT_FOUND);
        errorCodeMap.put(ContractError.ACCOUNT_NOT_IDENTIFIED.getErrorCode(), MessageCode.ACCOUNT_ID_NOT_IDENTIFIED);
        errorCodeMap.put(ContractError.ACCOUNT_DISABLED.getErrorCode(), MessageCode.ACCOUNT_ID_NOT_ENABLED);
        errorCodeMap.put(ContractError.ACCOUNT_SIGNATURE_INVALID_VALUE.getErrorCode(),
                         MessageCode.SIGNATURE_INVALID);
        errorCodeMap.put(ContractError.INFO_INVALID_VALUE.getErrorCode(), MessageCode.SIGNATURE_EXPIRED);
        return errorCodeMap;
    }
}
