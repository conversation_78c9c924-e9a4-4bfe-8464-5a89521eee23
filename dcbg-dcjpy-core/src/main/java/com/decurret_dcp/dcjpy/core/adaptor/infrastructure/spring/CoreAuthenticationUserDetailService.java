package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring;

import java.text.ParseException;
import java.util.Set;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.core.application.identity_authority.ClientEntityAccessService;
import com.decurret_dcp.dcjpy.core.consts.DCFConst;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntity;
import com.decurret_dcp.dcjpy.core.domain.model.id_provider.ClientId;
import com.decurret_dcp.dcjpy.core.domain.model.user.CoreUserDetails;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.proc.BadJOSEException;
import com.nimbusds.jose.proc.SecurityContext;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.proc.ConfigurableJWTProcessor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
public class CoreAuthenticationUserDetailService
        implements AuthenticationUserDetailsService<PreAuthenticatedAuthenticationToken> {

    private static final String BEARER_PREFIX = "Bearer ";

    private static final String CLIENT_ID_KEY = "client_id";

    private static final GrantedAuthority ROLE_ADMIN = new SimpleGrantedAuthority("ROLE_ADMIN");

    private static final GrantedAuthority ROLE_PROVIDER = new SimpleGrantedAuthority("ROLE_PROVIDER");

    private static final GrantedAuthority ROLE_ISSUER = new SimpleGrantedAuthority("ROLE_ISSUER");

    private static final GrantedAuthority ROLE_VALIDATOR = new SimpleGrantedAuthority("ROLE_VALIDATOR");

    private final ConfigurableJWTProcessor<SecurityContext> jwtProcessor;

    private final ClientEntityAccessService clientEntityAccessService;

    @Override
    public UserDetails loadUserDetails(PreAuthenticatedAuthenticationToken token)
            throws UsernameNotFoundException {

        // フィルタで取得したAuthorizationヘッダの値
        var credential = token.getCredentials().toString();
        if ((credential == null) || credential.isEmpty()) {
            log.warn("Request has no Authorization header");
            throw new UsernameNotFoundException("IdentityAuthority not found");
        }

        if (DCFConst.HEALTH_CREDENTIAL.equals(credential)) {
            /* NOTE: 本来であれば、springの機能でhealthチェックのリクエストはloadUserDetailsに来ないように
             * できればよかったが、方法が不明のため、healthチェックのリクエストの場合、credentialに
             * 文字列を設定し、ここでExceptionを投げている。
             * healthチェックはpermitAllが付いているので、ここでエラーとなっても、healthチェック結果は正しく返る
             */
            throw new UsernameNotFoundException("Health Request");
        }

        credential = StringUtils.replace(credential, BEARER_PREFIX, "");

        JWTClaimsSet claimsSet;
        String clientId;
        try {
            claimsSet = jwtProcessor.process(credential, null);
            clientId = claimsSet.getStringClaim(CLIENT_ID_KEY);
        } catch (ParseException | BadJOSEException | JOSEException exc) {
            log.warn("Invalid access token: {}", exc.getMessage());
            throw new UsernameNotFoundException("IdentityAuthority not found", exc);
        }

        if (StringUtils.hasText(clientId) == false) {
            log.warn("Client id is not found from jwt.");
            throw new UsernameNotFoundException("ClientId not found");
        }

        ClientEntity clientEntity = clientEntityAccessService.getByClientId(new ClientId(clientId));
        if (clientEntity == null) {
            // ここでアクセス権限が取得できないのはシステムのセットアップ不足
            log.error("ClientEntity not found. clientId = {}", clientId);
            throw new UsernameNotFoundException("ClientEntity not found");
        }

        GrantedAuthority authority = switch (clientEntity.entityType) {
            case ADMIN -> ROLE_ADMIN;
            case PROVIDER -> ROLE_PROVIDER;
            case ISSUER -> ROLE_ISSUER;
            case VALIDATOR -> ROLE_VALIDATOR;
        };

        String sub = claimsSet.getSubject();
        return new CoreUserDetails(sub, Set.of(authority), clientEntity);
    }
}
