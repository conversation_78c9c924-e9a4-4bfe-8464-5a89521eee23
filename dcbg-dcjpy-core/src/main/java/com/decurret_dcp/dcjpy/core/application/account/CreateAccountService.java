package com.decurret_dcp.dcjpy.core.application.account;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.account.command.CreateAccountServiceCommand;
import com.decurret_dcp.dcjpy.core.application.account.result.CreateAccountServiceResult;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.domain.model.balance_cache.BalanceCacheRepository;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.validator.command.AddAccountCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.ForbiddenException;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class CreateAccountService {
    private final SendBlockchainContractService sendBlockchainContractService;
    private final BalanceCacheRepository balanceCacheRepository;

    /**
     * FinZone口座の作成を行う
     *
     * @param command コマンド
     * @return 実行結果
     * @throws ForbiddenException 権限がない場合
     *     E0014 権限がありません
     * @throws BadRequestException 共通領域用口座の作成に失敗した場合
     *     E0028 provider_idが見つかりません
     */
    @Transactional
    public CreateAccountServiceResult execute(CreateAccountServiceCommand command)
            throws ForbiddenException, BadRequestException {

        // ZoneIdをスレッドローカルから取得する
        ZoneId zoneId = ZoneIdThreadLocalHolder.getZoneId();

        if (!zoneId.isFinancialZone()) {
            throw new ForbiddenException(MessageCode.INSUFFICIENT_AUTHORITY);
        }

        // コントラクトにアカウント登録
        AddAccountCommand args = command.toAddAccountCommand();

        EntityId signerId = new EntityId(command.validatorId);
        sendBlockchainContractService.execute(args, signerId);

        //残高キャッシュの作成
        this.balanceCacheRepository.createBalanceCache(command.accountId, zoneId);

        return CreateAccountServiceResult.initResult(command);
    }
}
