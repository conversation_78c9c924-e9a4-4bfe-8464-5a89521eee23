package com.decurret_dcp.dcjpy.core.application.token_spec.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.renewable_energy_token.command.RenewableEnergyTokenGetTokenListCommand;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.fundamental.SortOrder;

import lombok.Builder;

@Builder
public class GetNftListServiceCommand {

    public final ValidatorId validatorId;

    public final String nftId;

    public final AccountId accountId;

    public final SortOrder sortOrder;

    public final Offset offset;

    public final Limit limit;

    public RenewableEnergyTokenGetTokenListCommand toGetNftCommand() {
        return new RenewableEnergyTokenGetTokenListCommand(
                this.validatorId,
                this.accountId,
                this.offset,
                this.limit,
                this.sortOrder
        );
    }
}
