package com.decurret_dcp.dcjpy.core.application.issuer.result;

import java.util.List;

import com.decurret_dcp.dcjpy.core.domain.model.bank.BankCode;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.result.IssuerGetIssuerListResult;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.TotalCount;

import lombok.Builder;

@Builder
public class GetIssuerListResult {

    public final List<Issuer> issuerList;
    public final TotalCount totalCount;

    @Builder
    public static class Issuer {

        public final IssuerId issuerId;
        public final BankCode bankCode;
        public final String name;

        public static Issuer initIssuer(IssuerGetIssuerListResult.Issuer contractIssuers) {
            return Issuer.builder()
                    .issuerId(contractIssuers.issuerId.issuerId())
                    .bankCode(contractIssuers.bankCode)
                    .name(contractIssuers.name)
                    .build();
        }
    }

    public static GetIssuerListResult initResult(IssuerGetIssuerListResult contractResult) {
        List<Issuer> issuers = contractResult.issuers.stream()
                .map(Issuer::initIssuer)
                .toList();

        return GetIssuerListResult.builder()
                .issuerList(issuers)
                .totalCount(contractResult.totalCount)
                .build();
    }
}
