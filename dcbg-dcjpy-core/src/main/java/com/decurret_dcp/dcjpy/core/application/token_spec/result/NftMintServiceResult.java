package com.decurret_dcp.dcjpy.core.application.token_spec.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetaDataDetail;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.MetadataId;
import com.decurret_dcp.dcjpy.core.domain.model.token_spec.NftTokenId;

import lombok.Builder;

@Builder
public class NftMintServiceResult {
    public final NftTokenId nftTokenId;

    public final AccountId mintedAccountId;

    public final String mintedAccountName;

    public final AccountId ownerAccountId;

    public final String ownerAccountName;

    public final boolean locked;

    public final MetadataId metadataId;

    public final String metadataHash;

    public final MetaDataDetail metadataDetail;
}
