package com.decurret_dcp.dcjpy.core.application.issuer;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.issuer.command.AddIssuerServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.AddIssuerCommand;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.Issuer;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerIdGenerator;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class CreateIssuerService {

    private final IssuerIdGenerator issuerIdGenerator;
    private final SendBlockchainContractService sendBlockchainContractService;

    /**
     * イシュアの作成を行います
     *
     * @param command コマンド
     */
    @Transactional
    public Issuer execute(AddIssuerServiceCommand command) {
        IssuerId issuerId = issuerIdGenerator.generate();
        EntityId signerId = new EntityId(command.adminId);

        AddIssuerCommand addIssuerCommand = command.initContractCommand(issuerId);

        sendBlockchainContractService.execute(addIssuerCommand, signerId);

        return Issuer.builder()
                .issuerId(issuerId)
                .bankCode(command.bankCode)
                .name(command.name)
                .build();
    }
}
