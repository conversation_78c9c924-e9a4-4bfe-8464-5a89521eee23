package com.decurret_dcp.dcjpy.core.adaptor.infrastructure.spring;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.MDC;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import com.decurret_dcp.dcjpy.core.domain.model.client_entity.ClientEntity;
import com.decurret_dcp.dcjpy.core.domain.model.thread_local.ZoneIdThreadLocalHolder;
import com.decurret_dcp.dcjpy.core.domain.model.user.CoreUserDetails;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

/**
 * ZoneIdをCacheするためのフィルター
 */
public class ZoneIdCacheFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        CoreUserDetails userDetails = this.getDetails();
        if (userDetails == null) {
            filterChain.doFilter(request, response);
            return;
        }

        ClientEntity clientEntity = userDetails.getPrincipal();
        ZoneId zoneId = clientEntity.zoneId;

        ZoneIdThreadLocalHolder.setZoneId(zoneId);
        MDC.put("entityId", clientEntity.entityId.getValue());

        try {
            filterChain.doFilter(request, response);
        } finally {
            MDC.remove("entityId");
            ZoneIdThreadLocalHolder.remove();
        }
    }

    private CoreUserDetails getDetails() {
        SecurityContext context = SecurityContextHolder.getContext();
        if (context == null) {
            return null;
        }

        Authentication authentication = context.getAuthentication();
        if (authentication == null) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if ((principal instanceof CoreUserDetails) == false) {
            return null;
        }

        return (CoreUserDetails) principal;
    }
}
