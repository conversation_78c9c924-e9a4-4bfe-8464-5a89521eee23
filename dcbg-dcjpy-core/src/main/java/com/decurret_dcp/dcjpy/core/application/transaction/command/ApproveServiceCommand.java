package com.decurret_dcp.dcjpy.core.application.transaction.command;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.financial_check.command.CheckApproveCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.ApproveCommand;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Info;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Signature;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;

import lombok.Builder;

@Builder
public class ApproveServiceCommand {
    public final ValidatorId validatorId;
    public final AccountId ownerId;
    public final AccountId spenderId;
    public final Amount amount;
    public final Signature accountSignature;
    public final Info info;

    public CheckApproveCommand toCheckCommand() {
        return new CheckApproveCommand(
                this.validatorId,
                this.ownerId,
                this.spenderId,
                this.amount,
                this.accountSignature,
                this.info);
    }

    public ApproveCommand toApproveCommand() {
        return new ApproveCommand(
                this.validatorId,
                this.ownerId,
                this.spenderId,
                this.amount,
                this.accountSignature,
                this.info);
    }
}
