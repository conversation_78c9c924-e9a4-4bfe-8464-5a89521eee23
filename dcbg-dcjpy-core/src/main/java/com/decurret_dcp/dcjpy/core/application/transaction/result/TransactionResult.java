package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.AppTimeStamp;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.account.AccountIdNullable;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Balance;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.MiscValue1Nullable;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.MiscValue2Nullable;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionId;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionType;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorId;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class TransactionResult {
    /** トランザクションID */
    public final TransactionId transactionId;

    /** トランザクションハッシュ */
    public final TransactionHash transactionHash;

    /** アカウントID */
    public final AccountId accountId;

    /** アカウント名 */
    public final String accountName;

    /** バリデータID */
    public final ValidatorId validatorId;

    /** ゾーンID */
    public final ZoneId zoneId;

    /** ゾーン名 */
    public final String zoneName;

    /** 取引日時 */
    public final AppTimeStamp transactedAt;

    /** 取引種別 */
    public final TransactionType transactionType;

    /** 取引額 */
    public final Amount amount;

    /** 取引後残高 */
    public final Balance postBalance;

    /** 取引先アカウントID */
    public final AccountIdNullable otherAccountId;

    /** 取引先アカウント名 */
    public final String otherAccountName;

    /** 取引先ゾーンID */
    public final ZoneId otherZoneId;

    /** 取引先ゾーン名 */
    public final String otherZoneName;

    /** 送金指示者アカウントID */
    public final AccountIdNullable sendAccountId;

    /** 送金指示者アカウント名 */
    public final String sendAccountName;

    /** 汎用値1 */
    public final MiscValue1Nullable miscValue1;

    /** 汎用値2 */
    public final MiscValue2Nullable miscValue2;

    /** 摘要 */
    public final String memo;
}
