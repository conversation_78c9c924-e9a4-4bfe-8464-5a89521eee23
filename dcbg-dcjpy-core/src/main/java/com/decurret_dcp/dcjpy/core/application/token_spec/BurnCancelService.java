package com.decurret_dcp.dcjpy.core.application.token_spec;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.blockchain.SendBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.token_spec.command.BurnCancelServiceCommand;
import com.decurret_dcp.dcjpy.core.application.token_spec.result.BurnCancelServiceResult;
import com.decurret_dcp.dcjpy.core.consts.DCFConst;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.IssuerHasAccount;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain.token.command.BurnCancelCommand;
import com.decurret_dcp.dcjpy.core.domain.model.blockchain_client.BlockchainSendResponse;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.EntityId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.BadRequestException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.NotFoundException;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.TransactionHash;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.Transaction;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionDetail;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionRepository;
import com.decurret_dcp.dcjpy.core.domain.model.transaction.TransactionType;
import com.decurret_dcp.dcjpy.core.message.MessageCode;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class BurnCancelService {

    private final IssuerHasAccount issuerHasAccount;
    private final CallBlockchainContractService callBlockchainContractService;
    private final SendBlockchainContractService sendBlockchainContractService;
    private final TransactionRepository transactionRepository;

    /**
     * コイン償却の取消を行います
     *
     * @param command コマンド
     * @return 実行結果
     * @throws BadRequestException イシュアとアカウントの紐付け確認に失敗した場合
     *     E0001 account_idが見つかりません
     * @throws NotFoundException コイン償却のトランザクションが存在しない場合
     *     E0050 transaction_hashが見つかりません
     */
    @Transactional(readOnly = true)
    public BurnCancelServiceResult execute(BurnCancelServiceCommand command) throws
            BadRequestException, NotFoundException {

        issuerHasAccount.execute(command.issuerId,
                                 command.accountId,
                                 new IssuerHasAccount.RequestSwitch(), callBlockchainContractService);

        Transaction transaction = transactionRepository.selectByAccountIdAndTxHashAndEventType(
                command.accountId,
                command.cancelTransactionHash,
                new TransactionType(DCFConst.BURN));
        if (transaction == null) {
            throw new NotFoundException(MessageCode.TRANSACTION_HASH_NOT_FOUND);
        }

        // コントラクト関数実行
        BlockchainSendResponse blockchainSendResponse = burnCancel(command, transaction);

        return new BurnCancelServiceResult(
                command.accountId,
                command.cancelTransactionHash,
                new TransactionHash(blockchainSendResponse.transactionHash)
        );
    }

    private BlockchainSendResponse burnCancel(BurnCancelServiceCommand command, Transaction transaction) {

        BurnCancelCommand burnCancelCommand = command.toBurnCancelCommand(transaction);

        EntityId signerId = new EntityId(command.issuerId);

        return sendBlockchainContractService.execute(burnCancelCommand, signerId);
    }
}
