package com.decurret_dcp.dcjpy.core.application.identity.command;

import com.decurret_dcp.dcjpy.core.domain.model.blockchain.issuer.command.AddIssuerRoleCommand;
import com.decurret_dcp.dcjpy.core.domain.model.client_entity.AdminId;
import com.decurret_dcp.dcjpy.core.domain.model.entity_signer.Eoa;
import com.decurret_dcp.dcjpy.core.domain.model.issuer.IssuerId;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@AllArgsConstructor
@EqualsAndHashCode
public class CreateIssuerIdentityServiceCommand {

    public final AdminId adminId;

    public final IssuerId issuerId;

    public AddIssuerRoleCommand toAddIssuerRoleCommand(Eoa eoa) {
        return new AddIssuerRoleCommand(this.issuerId, eoa);
    }
}
