package com.decurret_dcp.dcjpy.core.application.transaction.result;

import com.decurret_dcp.dcjpy.core.domain.model.account.AccountId;
import com.decurret_dcp.dcjpy.core.domain.model.fundamental.Amount;
import com.decurret_dcp.dcjpy.core.domain.model.zone.ZoneId;

import lombok.Builder;

@Builder
public class CheckDischargeResult {

    public final AccountId accountId;
    public final String accountName;
    public final ZoneId fromZoneId;
    public final String fromZoneName;
    public final Amount dischargeAmount;

}
