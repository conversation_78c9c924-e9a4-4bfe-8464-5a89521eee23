package com.decurret_dcp.dcjpy.core.application.validator;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.core.application.blockchain.CallBlockchainContractService;
import com.decurret_dcp.dcjpy.core.application.validator.command.GetValidatorListServiceCommand;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Limit;
import com.decurret_dcp.dcjpy.core.domain.model.pagination.Offset;
import com.decurret_dcp.dcjpy.core.domain.model.validator.GetValidatorListResult;
import com.decurret_dcp.dcjpy.core.domain.model.validator.ValidatorGetValidatorList;
import com.decurret_dcp.dcjpy.core.domain.model.validator.result.ValidatorGetValidatorListResult;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class GetValidatorListService {

    private final CallBlockchainContractService callBlockchainContractService;

    private final ValidatorGetValidatorList validatorGetValidatorList;

    /**
     * バリデータ一覧の取得を行います
     *
     * @param command コマンド
     * @return 実行結果
     */
    @Transactional(readOnly = true)
    public GetValidatorListResult execute(GetValidatorListServiceCommand command) {
        Offset offset = command.offset;
        Limit limit = command.limit;

        ValidatorGetValidatorListResult result = validatorGetValidatorList.execute(offset, limit, callBlockchainContractService);

        return GetValidatorListResult.create(result);
    }
}
