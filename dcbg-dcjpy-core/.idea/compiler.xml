<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" procOnly="true">
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-configuration-processor/2.7.6/ef52bd1c96c4b04f3fe6c72a6108a3bea24f966f/spring-boot-configuration-processor-2.7.6.jar" />
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.24/13a394eed5c4f9efb2a6d956e2086f1d81e857d9/lombok-1.18.24.jar" />
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.seasar.doma/doma-processor/2.53.1/669e06d977447ebc4d36b9ac1d5a760e24feceee/doma-processor-2.53.1.jar" />
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.seasar.doma/doma-core/2.53.1/faa3a1af2dc9f12551b2303c7eaa10462bd180c/doma-core-2.53.1.jar" />
        </processorPath>
        <module name="dcbg-dcjpy-core.main" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="17" />
  </component>
</project>